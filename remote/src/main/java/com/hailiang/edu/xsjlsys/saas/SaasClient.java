package com.hailiang.edu.xsjlsys.saas;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.MapUtils;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.consts.SaasLoginEndPointConst;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.saas.AuthenticationResourceDto;
import com.hailiang.edu.xsjlsys.dto.saas.RequestLoginDto;
import com.hailiang.edu.xsjlsys.dto.saas.SaasClassDto;
import com.hailiang.edu.xsjlsys.dto.saas.SaasLoginRespDto;
import com.hailiang.edu.xsjlsys.dto.saas.req.*;
import com.hailiang.edu.xsjlsys.dto.saas.resp.*;
import com.hailiang.edu.xsjlsys.emuns.ApiCodeEnum;
import com.hailiang.edu.xsjlsys.emuns.SaasAppVersionCodeEnum;
import com.hailiang.edu.xsjlsys.reqo.*;
import com.hailiang.edu.xsjlsys.saas.feign.SaasFeign;
import com.hailiang.edu.xsjlsys.saas.feign.SaasFeignInner;
import com.hailiang.saas.op.auth.authentication.AuthenticationService;
import com.hailiang.saas.op.auth.authentication.staff.StaffAuthenticationReq;
import com.hailiang.saas.op.auth.authentication.staff.StaffAuthenticationResp;
import com.hailiang.saas.op.auth.model.R;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("all")
public class SaasClient {

    @Value("${saas.appId}")
    private String appId;

    @Value("${saas.appSecret}")
    private String appSecret;

    @Value("${saas.accessToken}")
    private String accessToken;

    @Value("${saas.openUrl}")
    private String openUrl;

    @Value("${saas.defaultSite}")
    private String defaultSite;

    @Value("${dingDing.appId}")
    private String dingAppId;

    @Value("${open.newAuth}")
    private Boolean newAuth;

    @Resource
    SaasFeign saasFeign;

    @Resource
    SaasFeignInner saasFeignInner;

    @Resource
    private AuthenticationService authenticationService;

    private int maxInt = 999999;

    public String getString(Map<String, Object> map) {
        List<String> list = new ArrayList<>();
        if (MapUtils.isNotEmpty(map)) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                StringBuffer stringBuffer = new StringBuffer();
                String key = entry.getKey();
                if (!(Constant.AUTHORIZATION.equals(key) || Constant.ACCESS_TOKEN.equals(key))) {
                    Object value = entry.getValue();
                    stringBuffer.append(key).append(value);
                    list.add(stringBuffer.toString());
                }
            }
        }
        // 排序
        Collections.sort(list);
        StringBuffer sbf = new StringBuffer();
        for (String str : list) {
            sbf.append(str);
        }
        return sbf.toString();
    }


    public AuthenticationResourceDto doLogin(RequestLoginDto requestLoginDto) throws BusinessException {

        if (Objects.isNull(newAuth) || Boolean.FALSE.equals(newAuth)) {
            // 当前时间
            String currentTimeStr = String.valueOf(System.currentTimeMillis());
            // 随机数 1-999999，用于防止重复攻击
            Random random = new Random();
            int randomInt = random.nextInt(maxInt);
            Map<String, Object> param = new HashMap<>();
            param.put(Constant.APP_ID, appId);
            param.put(Constant.TIMESTAMP, currentTimeStr);
            param.put(Constant.NONCE, randomInt);
            param.put(Constant.STAFF_ID, requestLoginDto.getStaffId());
            param.put(Constant.SCHOOL_ID, requestLoginDto.getSchoolId());
            param.put(Constant.TENANT_ID, requestLoginDto.getTenantId());
            param.put(Constant.ACCESS_TOKEN, accessToken);
            param.put(Constant.SITE, defaultSite);
            param.put(Constant.AUTHORIZATION, requestLoginDto.getAuthorization());
            String string = getString(param);
            String sign = SecurityUtil.hmacSha1ToHexStr(string, appSecret);
            String resStr = httpPost(openUrl, new HashMap<>(), param, sign, currentTimeStr, randomInt);
            log.info("saas 响应信息resStr:" + resStr);
            if (null != resStr) {
                CommonResultRespDto<JSONObject> commonResultRespDto = JSONObject.parseObject(resStr, CommonResultRespDto.class);

                if (commonResultRespDto.getSuccess()) {
                    JSONObject data = commonResultRespDto.getData();
                    String resCode = data.get(Constant.RESCODE).toString();
                    if (CommonEnum.AuthEnum.CHECK_SUCCESS.getCode().equals(resCode)) {
                        JSONObject authMap = (JSONObject) data.get("auth");
                        JSONObject authenticationResourceMap = (JSONObject) authMap.get("authenticationResource");
                        return JSONObject.parseObject(JSONObject.toJSONString(authenticationResourceMap), AuthenticationResourceDto.class);
                    } else {
                        String resDesc = data.get(Constant.RESDESC).toString();
                        throw new BusinessException(resDesc, ApiCodeEnum.LOGIN_ERROR.getCode());
                    }
                }
            }
        } else {
            StaffAuthenticationReq context = new StaffAuthenticationReq();
            context.setAuthorization(requestLoginDto.getAuthorization());
            context.setSchoolId(Long.parseLong(requestLoginDto.getSchoolId()));
            context.setStaffId(Long.parseLong(requestLoginDto.getStaffId()));
            context.setTenantId(Long.parseLong(requestLoginDto.getTenantId()));
            R<StaffAuthenticationResp> execute = authenticationService.tokenAuthentication(context);
            log.info("saas 响应信息resStr:" + JSON.toJSONString(execute));
            if (null != execute) {
                if (Objects.nonNull(execute.getSuccess()) && execute.getSuccess()) {
                    StaffAuthenticationResp data = execute.getData();
                    String resCode = String.valueOf(execute.getStatus());
                    StaffAuthenticationResp.AuthenticationResource authenticationResource = data.getAuthenticationResource();

                    return JSONObject.parseObject(JSON.toJSONString(authenticationResource), AuthenticationResourceDto.class);
                } else {
                    throw new BusinessException(execute.getMessage(), ApiCodeEnum.LOGIN_ERROR.getCode());
                }
            }
        }
        return null;
    }


    public String httpPost(String url, Map<String, Object> params, Map<String, Object> header, String sign, String currentTimeStr, int randomInt) {

        String result = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //2.声明post请求
        HttpPost httpPost = new HttpPost(url);
        //3.设置请求类型
        httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded");
        httpPost.setHeader(Constant.APP_ID, appId.toString());
        httpPost.setHeader(Constant.STAFF_ID, header.get(Constant.STAFF_ID).toString());
        httpPost.setHeader(Constant.SCHOOL_ID, header.get(Constant.SCHOOL_ID).toString());
        httpPost.setHeader(Constant.TENANT_ID, header.get(Constant.TENANT_ID).toString());
        httpPost.setHeader(Constant.SIGN, sign);
        httpPost.setHeader(Constant.ACCESS_TOKEN, accessToken);
        httpPost.setHeader(Constant.AUTHORIZATION, header.get(Constant.AUTHORIZATION).toString());
        // 验证方式，默认为post
        httpPost.setHeader(Constant.METHODAUTH, "post");
        // 站点，默认为hangzhou
        httpPost.setHeader(Constant.SITE, defaultSite);
        // 当前时间
        httpPost.setHeader(Constant.TIMESTAMP, currentTimeStr);
        httpPost.setHeader(Constant.NONCE, String.valueOf(randomInt));
        httpPost.setHeader(Constant.LANGUAGE, "java");
        //4.添加参数
        List<NameValuePair> parameters = new ArrayList<NameValuePair>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            parameters.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
        }

        UrlEncodedFormEntity formEntity;
        try {

            if (!CollectionUtils.isEmpty(parameters)) {
                formEntity = new UrlEncodedFormEntity(parameters, "UTF-8");
                httpPost.setEntity(formEntity);
            }
            //5.发送请求
            CloseableHttpResponse response = httpClient.execute(httpPost);
            log.info("httpCode：" + response.getStatusLine().getStatusCode());
            log.info("----------------返回信息：" + JSONObject.toJSONString(response.getEntity()));
//            log.debug("----------------返回信息：" + JSON.toJSONString(response.getEntity()));
            if (response.getStatusLine().getStatusCode() == 200) {
                HttpEntity entity = response.getEntity();
                result = EntityUtils.toString(entity, "utf-8");
            }
            //6.关闭资源
            response.close();
            httpClient.close();
        } catch (UnsupportedEncodingException e) {
            //  Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            //  Auto-generated catch block
            e.printStackTrace();
        }
        return result;
    }


    public FClassRespDto getClassInfoByClassId(String saasClassId) {
        try {
            List<Long> classIds = new ArrayList<>();
            classIds.add(Long.valueOf(saasClassId));

            CommonResultRespDto<List<FClassRespDto>> commonResultRespDto = saasFeign.getClassByIds(classIds);
            if (commonResultRespDto.getSuccess()) {
                List<FClassRespDto> classVOS = commonResultRespDto.getData();
                if (!CollectionUtils.isEmpty(classVOS)) {
                    return classVOS.get(0);
                }
            }
        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return null;
    }

    /**
     * 查询教师当前班级列表
     *
     * @return
     */
    public List<SaasClassDto> getClassListByStaff(XsUserInfo xsUserInfo) {
        List<SaasClassDto> saasClassDtoList = new ArrayList<>();
        //調用saas接口查詢員工班級信息
        StaffClassQueryReqDto query = new StaffClassQueryReqDto();
        query.setStaffId(Long.valueOf(xsUserInfo.getStaffId()));
        query.setSchoolId(Long.valueOf(xsUserInfo.getSchoolId()));
        query.setClassStatus("0");
        log.info("staffClassQuery: {}", JSONObject.toJSONString(query));
        CommonResultRespDto<PageResultRespDto<TchClassInfoRespDto>> commonResult = saasFeign.queryStaffBaseClass(query);
        log.info("commonResult: {}", JSONObject.toJSONString(commonResult));
        if (commonResult.getSuccess()) {
            PageResultRespDto<TchClassInfoRespDto> result1 = commonResult.getData();
            List<TchClassInfoRespDto> classInfoVOList = result1.getList();
            if (!CollectionUtils.isEmpty(classInfoVOList)) {
                //结果转换
                converClassInfoToDto(saasClassDtoList, classInfoVOList);
            }
        }
        return saasClassDtoList;
    }

    /**
     * 查询教师当前班级列表
     *
     * @return
     */
    public List<SaasClassDto> getClassListByStaIdAndSchId(String staffId, String schoolId) {
        List<SaasClassDto> saasClassDtoList = new ArrayList<>();
        //調用saas接口查詢員工班級信息
        StaffClassQueryReqDto query = new StaffClassQueryReqDto();
        query.setStaffId(Long.valueOf(staffId));
        query.setSchoolId(Long.valueOf(schoolId));
        query.setClassStatus("0");
        log.info("staffClassQuery: {}", JSONObject.toJSONString(query));
        CommonResultRespDto<PageResultRespDto<TchClassInfoRespDto>> commonResult = saasFeign.queryStaffBaseClass(query);
        log.info("commonResult: {}", JSONObject.toJSONString(commonResult));
        if (commonResult.getSuccess()) {
            PageResultRespDto<TchClassInfoRespDto> result1 = commonResult.getData();
            List<TchClassInfoRespDto> classInfoVOList = result1.getList();
            if (!CollectionUtils.isEmpty(classInfoVOList)) {
                //结果转换
                converClassInfoToDto(saasClassDtoList, classInfoVOList);
            }
        }
        return saasClassDtoList;
    }


    private void converClassInfoToDto(List<SaasClassDto> saasClassDtoList, List<TchClassInfoRespDto> classInfoVOList) {
        for (TchClassInfoRespDto classInfoVO : classInfoVOList) {
            //过滤已毕业班级
            if ("1".equals(classInfoVO.getUpgradeStatus())) {
                continue;
            }
            SaasClassDto saasClassDto = new SaasClassDto();
            saasClassDto.setClassId(String.valueOf(classInfoVO.getClassId()));
            saasClassDto.setSchoolId(String.valueOf(classInfoVO.getSchoolId()));
            //当勾选了日常展示时 才显示别名
            saasClassDto.setClassAlias(classInfoVO.getIsAlias().equals("1") ? classInfoVO.getClassAlias() : "");
            saasClassDto.setClassName(classInfoVO.getClassName());
            saasClassDto.setSchoolName(classInfoVO.getSchoolName());
            saasClassDto.setGradeCode(classInfoVO.getGradeCode());
            saasClassDto.setGradeName(classInfoVO.getGradeName());
            saasClassDto.setCampusId(String.valueOf(classInfoVO.getCampusId()));
            saasClassDto.setCampusName(classInfoVO.getCampusName());
            saasClassDto.setClassType(classInfoVO.getClassType());

            saasClassDtoList.add(saasClassDto);
        }

    }

    public List<ClassStudentRespDto> getStuListByCalss(String saasClassId, XsUserInfo xsUserInfo) {
        ClassStudentInfoQueryReqDto r = new ClassStudentInfoQueryReqDto();
        r.setClassId(Long.valueOf(saasClassId));
        r.setSchoolId(Long.valueOf(xsUserInfo.getSchoolId()));
        r.setStaffId(Long.valueOf(xsUserInfo.getStaffId()));
        r.setPageSize(500);
        CommonResultRespDto<PageResultRespDto<ClassStudentRespDto>> pageResultCommonResult = saasFeign.queryClassStudentPage(r);
        if (pageResultCommonResult.getSuccess()) {
            PageResultRespDto<ClassStudentRespDto> data = pageResultCommonResult.getData();
            List<ClassStudentRespDto> classStudentRespDtos = data.getList();
            log.info("classStudentRespDtos: {}", JSONObject.toJSONString(classStudentRespDtos));
            return classStudentRespDtos;
        }
        return new ArrayList<>();
    }

    public List<ClassStaffRespDto> getTeacherListByClass(ClassManageInfoReq classManageInfoReq, XsUserInfo xsUserInfo) {
        try {
            //对接saas接口  根据班级id 获取教师列表
            CommonResultRespDto<List<ClassStaffRespDto>> commonResultRespDto = saasFeign.
                    queryTeachersByClassId(Long.valueOf(classManageInfoReq.getSaasClassId()));
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                List<ClassStaffRespDto> teacherList = commonResultRespDto.getData();
                log.info("teacherList: {}", JSONObject.toJSONString(teacherList));
                return teacherList;
            }

        } catch (Exception e) {
            log.error("发生异常", e);
        }


        return new ArrayList<>();
    }


    public Map<Long, Long> getUserIdMapStaffId(Map<Long, Long> userIdWithOpenIdMap, String saasTenantId) {
        try {
            if (CollectionUtils.isEmpty(userIdWithOpenIdMap)) {
                return new HashMap<>();
            }
            StaffUnderTenantReqDto staffUnderTenantReqDto = new StaffUnderTenantReqDto();
            staffUnderTenantReqDto.setTenantId(Long.valueOf(saasTenantId));
            List<Long> userIdList = new ArrayList<>();
            for (Map.Entry<Long, Long> longStringEntry : userIdWithOpenIdMap.entrySet()) {
                userIdList.add(longStringEntry.getValue());
            }
            staffUnderTenantReqDto.setUserIdList(userIdList);
            CommonResultRespDto<List<StaffUnderTenantRespDto>> commonResultRespDto = saasFeignInner.
                    getSaasStaffListUnderTenant(staffUnderTenantReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                List<StaffUnderTenantRespDto> staffUnderTenantRespDtoList = commonResultRespDto.getData();
                Map<Long, Long> saasUserIdMapStaffId = staffUnderTenantRespDtoList.stream()
                        .collect(Collectors.toMap(StaffUnderTenantRespDto::getUserId, StaffUnderTenantRespDto::getStaffId, (p1, p2) -> p2));
                if (!CollectionUtils.isEmpty(saasUserIdMapStaffId)) {
                    Map<Long, Long> userIdMapStaffId = new HashMap<>();
                    for (Map.Entry<Long, Long> longStringEntry : userIdWithOpenIdMap.entrySet()) {
                        if (saasUserIdMapStaffId.containsKey(longStringEntry.getValue())) {
                            Long staffId = saasUserIdMapStaffId.get(longStringEntry.getValue());
                            userIdMapStaffId.put(longStringEntry.getKey(), staffId);
                        }
                    }
                    return userIdMapStaffId;
                }
            }
        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return new HashMap<>();
    }


    public List<TeachManageStaffTeachRespDto> getTeachManageStaffTeachListByCondition(SaasTeachManageStaffTeachReqDto saasTeachManageStaffTeachListReqDto) {

        try {
            CommonResultRespDto<List<TeachManageStaffTeachRespDto>> commonResultRespDto = saasFeignInner.
                    getSaasTeachManageStaffTeachListByCondition(saasTeachManageStaffTeachListReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                return commonResultRespDto.getData();
            }

        } catch (Exception e) {
            log.error("发生异常", e);
        }


        return new ArrayList<>();

    }


    public String getSchoolName(String schoolId) {

        try {
            CommonResultRespDto<TchSchoolRespDto> tchSchoolRespDtoCommonResultRespDto = saasFeign.queryById(Long.valueOf(schoolId));
            log.info("tchSchoolRespDtoCommonResultRespDto: {}", JSONObject.toJSONString(tchSchoolRespDtoCommonResultRespDto));
            if (tchSchoolRespDtoCommonResultRespDto.getSuccess() && tchSchoolRespDtoCommonResultRespDto.getStatus().equals(200)) {
                TchSchoolRespDto tchSchoolRespDto = tchSchoolRespDtoCommonResultRespDto.getData();
                if (tchSchoolRespDto != null) {
                    return tchSchoolRespDto.getSchoolName();
                }
                return "";
            }

        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return "";
    }

    public String getDingAppKey() {
        try {

            CommonResultRespDto commonResultRespDto = saasFeign.getDingAppKey();

            log.info("getDingAppKey: {}", JSONObject.toJSONString(commonResultRespDto));
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                if (null != commonResultRespDto.getData()) {
                    return String.valueOf(commonResultRespDto.getData());
                }
            }

        } catch (Exception e) {
            log.error("发生异常", e);
        }

        return "";
    }

    public SaasLoginRespDto doDingLogin(LoginReq loginReq) throws BusinessException {

        SaasLoginDingReqDto saasLoginDingReqDto = new SaasLoginDingReqDto();
        saasLoginDingReqDto.setAuthCode(loginReq.getAuthCode());
        saasLoginDingReqDto.setEndpoint(SaasLoginEndPointConst.WEB_BROWSER);
        saasLoginDingReqDto.setChannel("DING_DING");
        CommonResultRespDto commonResultRespDto = saasFeign.doLogin(saasLoginDingReqDto);

        log.info("doDingLogin: {}", JSONObject.toJSONString(commonResultRespDto));

        if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
            Object data = commonResultRespDto.getData();
            SaasLoginRespDto saasLoginRespDto = JSONObject.parseObject(JSONObject.toJSONString(data), SaasLoginRespDto.class);
            return saasLoginRespDto;
        } else {
            throw new BusinessException("钉钉扫码异常");
        }
    }

    public List<UserSchoolRespDto> getSchoolList(String openId) throws BusinessException {


        CommonResultRespDto<List<UserSchoolRespDto>> commonResult = saasFeign.userAppSchool(Long.valueOf(openId), Long.valueOf(appId));

        if (commonResult.getSuccess() && commonResult.getStatus().equals(200)) {

            return commonResult.getData();
        } else {
            throw new BusinessException("学校信息获取异常");
        }

    }

    /**
     * 获取手机验证码
     *
     * @param saasReq
     * @return
     * @throws BusinessException
     */
    public String getVerificationCode(SaasReq saasReq) throws BusinessException {
        MessageCodeReqDto messageCodeReqDto = new MessageCodeReqDto();
        messageCodeReqDto.setMobile(saasReq.getPhone());
        messageCodeReqDto.setAppId(appId);
        messageCodeReqDto.setVerificationType("LOGIN");
        CommonResultRespDto commonResultRespDto = saasFeign.getVerificationCode(messageCodeReqDto);
        log.info("getVerificationCode: {}", JSONObject.toJSONString(commonResultRespDto));
        if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
            if (null != commonResultRespDto.getData()) {
                Object data = commonResultRespDto.getData();
                return data.toString();
            }


        } else {
            throw new BusinessException(commonResultRespDto.getMessage());
        }
        return "";
    }

    public SaasLoginRespDto codeLogin(SaasReq saasReq) throws BusinessException {

        SaasLoginDingReqDto saasLoginDingReqDto = new SaasLoginDingReqDto();
        saasLoginDingReqDto.setUserName(saasReq.getPhone());
        saasLoginDingReqDto.setCode(saasReq.getVerificationCode());
        saasLoginDingReqDto.setVerificationType("LOGIN");
        saasLoginDingReqDto.setEndpoint(SaasLoginEndPointConst.H_5);
        saasLoginDingReqDto.setChannel("VERIFICATION");
        CommonResultRespDto commonResultRespDto = saasFeign.doLogin(saasLoginDingReqDto);

        log.info("codeLogin: {}", JSONObject.toJSONString(commonResultRespDto));

        if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
            Object data = commonResultRespDto.getData();
            SaasLoginRespDto saasLoginRespDto = JSONObject.parseObject(JSONObject.toJSONString(data), SaasLoginRespDto.class);
            return saasLoginRespDto;
        } else {
            throw new BusinessException(commonResultRespDto.getMessage());
        }
    }


    public void accountLoginPicture(SaasReq saasReq, HttpServletResponse httpServletResponse) throws IOException {
        Response response = saasFeignInner.getGraphicVerification(saasReq.getPhone(), "5");
        //结果响应出去
        OutputStream out = httpServletResponse.getOutputStream();
        try {
//            httpServletResponse.setContentType("application/octet-stream");
            httpServletResponse.setContentType("image/png");
            IOUtils.copy(response.body().asInputStream(), out);
            out.flush();
        } catch (Exception e) {
            log.error("发生异常", e);
        } finally {
            out.close();
        }
    }


    public SaasLoginRespDto accountLogin(SaasReq saasReq) {
        SaasLoginReqDto saasLoginReqDto = new SaasLoginReqDto();
        saasLoginReqDto.setUserName(saasReq.getPhone());
        saasLoginReqDto.setEndpoint(SaasLoginEndPointConst.H_5);
        saasLoginReqDto.setChannel("PASSWORD");
        saasLoginReqDto.setPassword(saasReq.getPassword());
        saasLoginReqDto.setAppType("pass");

        if (!StrUtil.isEmpty(saasReq.getPictureCode())) {
            saasLoginReqDto.setPictureCode(saasReq.getPictureCode());
        }

        log.info("saasLoginReqDto {}", JSONObject.toJSONString(saasLoginReqDto));
        CommonResultRespDto<Object> commonResult = saasFeignInner.doLogin(saasLoginReqDto);
        log.info("accountLogin: {}", JSONObject.toJSONString(commonResult));

        Integer status = commonResult.getStatus();
        if (null == status) {
            throw new BusinessException("saas接口异常");
        }

        if (status.equals(Constant.SUCCESS_CODE)) {
            //正常业务逻辑
            return JSON.parseObject(JSON.toJSONString(commonResult.getData()), SaasLoginRespDto.class);
        } else {

            if (status.equals(Constant.ERROR_CODE_VERIFICATION_LOGIN_FAILD)
                    || status.equals(Constant.ERROR_CODE_VERIFICATION_FAILD)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.VERIFICATION_ERROR.getCode());
            }

            if (status.equals(Constant.ERROR_CODE_LOCK)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.LOCK_ERROR.getCode());
            }

            if (status.equals(Constant.ERROR_CODE_PASSWORD_EXPIRED)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.PASSWORD_EXPIRED.getCode());
            }

            if (status.equals(Constant.ERROR_CODE_FIRST_LOGIN)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.FIRST_LOGIN_ERROR.getCode());
            }

            throw new BusinessException(commonResult.getMessage());

        }
    }

    public SaasLoginRespDto dingAppLogin(LoginReq loginReq) throws BusinessException {

        SaasLoginReqDto saasLoginReqDto = new SaasLoginReqDto();
        saasLoginReqDto.setAuthCode(loginReq.getAuthCode());
        saasLoginReqDto.setEndpoint("H_5");
        saasLoginReqDto.setChannel("DING_DING");
        saasLoginReqDto.setAppId(dingAppId);
        saasLoginReqDto.setAppType("INNER");
        saasLoginReqDto.setPlatformType("DINGTALK");

        CommonResultRespDto<Object> commonResult = saasFeignInner.doLogin(saasLoginReqDto);
        log.info("dingAppLogin:" + JSONObject.toJSONString(commonResult));

        Integer status = commonResult.getStatus();
        if (null == status) {
            throw new BusinessException("saas接口异常");
        }

        if (status.equals(Constant.SUCCESS_CODE)) {
            //正常业务逻辑
            return JSON.parseObject(JSON.toJSONString(commonResult.getData()), SaasLoginRespDto.class);
        } else {
            throw new BusinessException(commonResult.getMessage());
        }
    }


    /**
     * @param saasReq
     * @return
     * @throws BusinessException
     */
    public SaasOrgDto getSaasOrgTreeByCondition(SaasOrgTreeListReqDto staffClassQueryReqDto) throws BusinessException {
        CommonResultRespDto<List<SaasOrgDto>> saasOrgTreeData = saasFeignInner.getSaasOrgTreeListByCondition(staffClassQueryReqDto);
        log.info("getSaasOrgTreeListByCondition: {}", JSONObject.toJSONString(saasOrgTreeData));
        if (saasOrgTreeData.getSuccess() && saasOrgTreeData.getStatus().equals(200)) {
            if (null != saasOrgTreeData.getData()) {
                return saasOrgTreeData.getData().get(0);
            }
        } else {
            throw new BusinessException(saasOrgTreeData.getMessage());
        }
        return null;
    }

    public String getFeiShuAppId() {
        try {

            CommonResultRespDto commonResultRespDto = saasFeignInner.getFeiShuAppId();

            log.info("getFeiShuAppId: {}", JSONObject.toJSONString(commonResultRespDto));
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                if (null != commonResultRespDto.getData()) {
                    return String.valueOf(commonResultRespDto.getData());
                }
            }

        } catch (Exception e) {
            log.error("发生异常", e);
        }

        return "";
    }

    public SaasLoginRespDto doFeiShuLogin(FeiShuReq feiShuReq) throws BusinessException {

        SaasLoginDingReqDto saasLoginDingReqDto = new SaasLoginDingReqDto();
        saasLoginDingReqDto.setAuthCode(feiShuReq.getAuthCode());
        saasLoginDingReqDto.setEndpoint(SaasLoginEndPointConst.H_5);
        saasLoginDingReqDto.setChannel("FEI_SHU");
        saasLoginDingReqDto.setAppId(feiShuReq.getAppId());
        saasLoginDingReqDto.setAppType("INNER");
        saasLoginDingReqDto.setPlatformType("FEI_SHU");
        saasLoginDingReqDto.setRedirectUrl(feiShuReq.getRedirectUrl());

        CommonResultRespDto commonResultRespDto = saasFeignInner.doFeiShuLogin(saasLoginDingReqDto);

        log.info("doFeiShuLogin: {}", JSONObject.toJSONString(commonResultRespDto));

        if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
            Object data = commonResultRespDto.getData();
            SaasLoginRespDto saasLoginRespDto = JSONObject.parseObject(JSONObject.toJSONString(data), SaasLoginRespDto.class);
            return saasLoginRespDto;
        } else {
            throw new BusinessException("飞书扫码异常");
        }
    }

    public FSchoolRespDto querySchoolByAppId(Long appId) throws BusinessException {
        CommonResultRespDto<FSchoolRespDto> commonResult = saasFeign.querySchoolByAppId(appId);
        log.info("querySchoolByAppId: {}", JSONObject.toJSONString(commonResult));
        if (commonResult.getSuccess() && commonResult.getStatus().equals(200)) {
            return commonResult.getData();
        } else {
            throw new BusinessException("星动力应用绑定学校信息获取异常");
        }
    }

    public List<SaasUserRoleDto> getUserRoleList(SaasUserRoleReqDto userRoleReqDto) throws BusinessException {
        List<SaasUserRoleDto> userRoleDtos = Lists.newArrayList();
        try {
            CommonResultRespDto<List<SaasUserRoleDto>> saasFeignInnerUserRoleList = saasFeignInner
                    .getUserRoleList(userRoleReqDto);

            if (saasFeignInnerUserRoleList != null) {
                if (saasFeignInnerUserRoleList.getSuccess()
                        && saasFeignInnerUserRoleList.getStatus().equals(200)) {

                    userRoleDtos = saasFeignInnerUserRoleList.getData();
                }
            }
        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return userRoleDtos;
    }

    public List<ClassInfoRespDto> queryClassListByCondition(ClassQueryReqDto reqDto) throws BusinessException {
        List<ClassInfoRespDto> classInfoRespDtos = Lists.newArrayList();
        try {
            CommonResultRespDto<PageResultRespDto<ClassInfoRespDto>> pageResultRespDtoCommonResultRespDto = saasFeign
                    .queryClassPageByCondition(reqDto);

            if (pageResultRespDtoCommonResultRespDto != null) {
                if (pageResultRespDtoCommonResultRespDto.getSuccess()
                        && pageResultRespDtoCommonResultRespDto.getStatus().equals(200)) {

                    classInfoRespDtos = pageResultRespDtoCommonResultRespDto.getData().getList();
                }
            }
        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return classInfoRespDtos;
    }

    public List<SaasTchTermRespDto> getTchTermList(SaasTchTermReqDto saasTchTermReqDto) throws BusinessException {
        List<SaasTchTermRespDto> tchTermRespDtoList = new ArrayList<>();
        try {
            CommonResultRespDto<List<SaasTchTermRespDto>> tchTermList = saasFeignInner.getTchTermList(saasTchTermReqDto);

            if (tchTermList != null) {
                if (tchTermList.getSuccess()
                        && tchTermList.getStatus().equals(200)) {

                    tchTermRespDtoList = tchTermList.getData();
                }
            }
        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return tchTermRespDtoList;
    }

    public List<UnderClassInfoRespDto> getClassDetailByIds(UnderClassQueryReqDto underClassQueryReqDto) throws BusinessException {

        List<UnderClassInfoRespDto> classInfoRespDtos = new ArrayList<>();
        try {
            CommonResultRespDto<List<UnderClassInfoRespDto>> classDetailByIds = saasFeignInner.getClassDetailByIds(underClassQueryReqDto);

            if (classDetailByIds != null) {
                if (classDetailByIds.getSuccess() && classDetailByIds.getStatus().equals(200)) {

                    classInfoRespDtos = classDetailByIds.getData();
                }
            }
        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return classInfoRespDtos;
    }

    public SaasLoginRespDto wechatLogin(LoginReq loginReq) {

        SaasLoginReqDto saasLoginReqDto = new SaasLoginReqDto();
        saasLoginReqDto.setAuthCode(loginReq.getAuthCode());
        saasLoginReqDto.setEndpoint(SaasLoginEndPointConst.WEB_BROWSER);
        saasLoginReqDto.setChannel("WECHAT_QR_CODE");
        saasLoginReqDto.setWechatLoginType("SCAN_QR");

        CommonResultRespDto<Object> commonResult = saasFeignInner.doLogin(saasLoginReqDto);

        if (Objects.isNull(commonResult)) {
            throw new BusinessException("saas接口异常");
        }
        Integer status = commonResult.getStatus();
        if (Objects.isNull(status)) {
            throw new BusinessException("saas接口异常");
        }
        log.info("dingAppLogin:{}", JSONObject.toJSONString(commonResult));

        if (status.equals(Constant.SUCCESS_CODE) || status.equals(Constant.WECHAT_UN_BINDING)) {
            //正常业务逻辑 以及未绑定的情况
            return JSON.parseObject(JSON.toJSONString(commonResult.getData()), SaasLoginRespDto.class);
        } else {

            if (status.equals(Constant.WECHAT_UN_REGISTER)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.WECHAT_UN_REGISTER.getCode());
            }

            if (status.equals(Constant.WECHAT_HAS_BINDING)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.WECHAT_HAS_BINDING.getCode());
            }

            if (status.equals(Constant.WECHAT_INVALID_CODE)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.WECHAT_INVALID_CODE.getCode());
            }
            throw new BusinessException(commonResult.getMessage());
        }
    }

    public WechatUserRespDto wechatInfo(long saasUserId) {

        WechatUserReqDto wechatUserReqDto = new WechatUserReqDto();
        wechatUserReqDto.setIdList(Collections.singletonList(saasUserId));

        CommonResultRespDto<List<WechatUserRespDto>> wechatUserRespDtoList = saasFeignInner.wechatInfo(wechatUserReqDto);
        if (Objects.isNull(wechatUserRespDtoList)) {
            throw new BusinessException("saas接口异常");
        }
        Integer status = wechatUserRespDtoList.getStatus();
        if (Objects.isNull(status)) {
            throw new BusinessException("saas接口异常");
        }
        log.info("wechatIsBind:" + JSONObject.toJSONString(wechatUserRespDtoList));
        if (status.equals(Constant.SUCCESS_CODE)) {
            //正常业务逻辑
            List<WechatUserRespDto> data = wechatUserRespDtoList.getData();
            if (CollUtil.isEmpty(data)) {
                return null;
            }
            return data.get(0);
        } else {
            throw new BusinessException(wechatUserRespDtoList.getMessage());
        }
    }

    public void wechatUnbind(long saasUserId) {
        WechatUserReqDto wechatUserReqDto = new WechatUserReqDto();
        wechatUserReqDto.setUserId(saasUserId);

        CommonResultRespDto<Integer> commonResultRespDto = saasFeignInner.wechatUnbind(wechatUserReqDto);

        if (Objects.isNull(commonResultRespDto)) {
            throw new BusinessException("saas接口异常");
        }
        Integer status = commonResultRespDto.getStatus();
        if (Objects.isNull(status)) {
            throw new BusinessException("saas接口异常");
        }
        if (status.equals(Constant.SUCCESS_CODE)) {
            return;
        } else {
            throw new BusinessException(commonResultRespDto.getMessage());
        }
    }

    public String wechatBind(LoginReq loginReq, long saasUserId) {

        WechatUserReqDto wechatUserReqDto = new WechatUserReqDto();
        wechatUserReqDto.setUserId(saasUserId);
        wechatUserReqDto.setAuthCode(loginReq.getAuthCode());

        CommonResultRespDto<String> commonResult = saasFeignInner.wechatBind(wechatUserReqDto);

        if (Objects.isNull(commonResult)) {
            throw new BusinessException("saas接口异常");
        }
        Integer status = commonResult.getStatus();
        if (Objects.isNull(status)) {
            throw new BusinessException("saas接口异常");
        }
        if (status.equals(Constant.SUCCESS_CODE) || status.equals(Constant.WECHAT_UN_BINDING)) {
            //正常业务逻辑 以及未绑定的情况
            return JSON.parseObject(JSON.toJSONString(commonResult.getData()), String.class);
        } else {

            if (status.equals(Constant.WECHAT_UN_REGISTER)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.WECHAT_UN_REGISTER.getCode());
            }

            if (status.equals(Constant.WECHAT_HAS_BINDING)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.WECHAT_HAS_BINDING.getCode());
            }

            if (status.equals(Constant.WECHAT_INVALID_CODE)) {
                throw new BusinessException(commonResult.getMessage(), ApiCodeEnum.WECHAT_INVALID_CODE.getCode());
            }
            throw new BusinessException(commonResult.getMessage());
        }
    }

    public StaffBatchVO batchStaff(Long staffId) {

        SaasBatchStaffReqDto saasBatchStaffReqDto = new SaasBatchStaffReqDto();
        saasBatchStaffReqDto.setStaffIdList(Collections.singletonList(staffId));
        saasBatchStaffReqDto.setState(0);

        CommonResultRespDto<List<StaffBatchVO>> listCommonResultRespDto = saasFeignInner.batchStaff(saasBatchStaffReqDto);
        if (Objects.isNull(listCommonResultRespDto)) {
            throw new BusinessException("根据staffIds 请求 saas接口异常");
        }
        Integer status = listCommonResultRespDto.getStatus();
        if (Objects.isNull(status)) {
            throw new BusinessException("saas接口异常");
        }
        log.info("batchStaff: {}", JSONObject.toJSONString(listCommonResultRespDto));
        if (status.equals(Constant.SUCCESS_CODE)) {
            //正常业务逻辑
            List<StaffBatchVO> data = listCommonResultRespDto.getData();
            if (CollUtil.isEmpty(data)) {
                return null;
            }
            return data.get(0);
        } else {
            throw new BusinessException(listCommonResultRespDto.getMessage());
        }
    }


    public List<StaffFullInfoVO> queryByCampusSectionId(Long sectionId) {

        StaffUnderSectionQueryDTO staffUnderSectionQueryDTO = new StaffUnderSectionQueryDTO();
        staffUnderSectionQueryDTO.setCampusSectionId(sectionId);

        CommonResultRespDto<List<StaffFullInfoVO>> listCommonResultRespDto = saasFeignInner.queryByCampusSectionId(staffUnderSectionQueryDTO);
        if (Objects.isNull(listCommonResultRespDto)) {
            throw new BusinessException("queryByCampusSectionId  saas接口异常");
        }
        Integer status = listCommonResultRespDto.getStatus();
        if (Objects.isNull(status)) {
            throw new BusinessException("saas接口异常");
        }
        log.info("StaffFullInfoVO: {}", JSONObject.toJSONString(listCommonResultRespDto));
        if (status.equals(Constant.SUCCESS_CODE)) {
            //正常业务逻辑
            List<StaffFullInfoVO> data = listCommonResultRespDto.getData();
            if (CollUtil.isEmpty(data)) {
                return null;
            }
            return data;
        } else {
            throw new BusinessException(listCommonResultRespDto.getMessage());
        }
    }

    public HelpCenterFloatBallRespDto helpCenterFloatBall(SaasFloatBallReq req, XsUserInfo xsUserInfo) {

        HelpCenterFloatBallReqDto helpCenterFloatBallReqDto = new HelpCenterFloatBallReqDto();
        helpCenterFloatBallReqDto.setId(Long.parseLong(req.getHelpCenterAppId()));
        helpCenterFloatBallReqDto.setStaffId(Long.valueOf(xsUserInfo.getStaffId()));
        helpCenterFloatBallReqDto.setSchoolId(Long.valueOf(xsUserInfo.getSchoolId()));

        CommonResultRespDto<HelpCenterFloatBallRespDto> commonResultRespDto = saasFeignInner.helpCenterFloatBall(helpCenterFloatBallReqDto);

        log.info("helpCenterFloatBallRespDto: {}", JSONObject.toJSONString(commonResultRespDto));

        if (Objects.isNull(commonResultRespDto)) {
            throw new BusinessException("saas接口异常");
        }
        Integer status = commonResultRespDto.getStatus();
        if (Objects.isNull(status)) {
            throw new BusinessException("saas接口异常");
        }
        if (status.equals(Constant.SUCCESS_CODE)) {
            //正常业务逻辑
            HelpCenterFloatBallRespDto helpCenterFloatBallRespDto = commonResultRespDto.getData();
            if (Objects.isNull(commonResultRespDto)) {
                throw new BusinessException("saas接口异常");
            }
            return helpCenterFloatBallRespDto;
        } else {
            throw new BusinessException(commonResultRespDto.getMessage());
        }
    }

    public void helpCenterFeedback(SaasFeedbackReq req, XsUserInfo xsUserInfo) {

        HelpCenterFeedbackReqDto feedbackReqDto = new HelpCenterFeedbackReqDto();
        feedbackReqDto.setAppId(Long.parseLong(appId));
        feedbackReqDto.setClientType(req.getClientType());
        feedbackReqDto.setSchoolId(Long.parseLong(xsUserInfo.getSchoolId()));
        feedbackReqDto.setFirstLevel(req.getFirstLevel());
        feedbackReqDto.setFbPersonType(req.getFbPersonType());
        feedbackReqDto.setFbPersonId(Long.parseLong(req.getFbPersonId()));
        feedbackReqDto.setFbTime(DateUtil.now());
        feedbackReqDto.setFbContent(req.getFbContent());
        feedbackReqDto.setFbImages(req.getFbImages());

        CommonResultRespDto<Void> feedback = saasFeignInner.feedback(feedbackReqDto);

        log.info("helpCenterFeedback: {}", JSONObject.toJSONString(feedback));

        if (Objects.isNull(feedback)) {
            throw new BusinessException("saas接口异常");
        }
        Integer status = feedback.getStatus();
        if (Objects.isNull(status)) {
            throw new BusinessException("saas接口异常");
        }
        if (!status.equals(Constant.SUCCESS_CODE)) {
            throw new BusinessException(feedback.getMessage());

        }
    }

    public AppVersionRespDto appVersion(XsUserInfo xsUserInfo) {

        try {
            AppVersionReqDto appVersionReqDto = new AppVersionReqDto();
            appVersionReqDto.setAppId(Long.parseLong(appId));
            appVersionReqDto.setTenantId(Long.valueOf(xsUserInfo.getTenantId()));

            CommonResultRespDto<AppVersionRespDto> appVersion = saasFeignInner.appVersion(appVersionReqDto);

            log.info("appVersion: {}", JSONObject.toJSONString(appVersion));

            if (Objects.isNull(appVersion)) {
                throw new BusinessException("saas接口异常");
            }
            Integer status = appVersion.getStatus();
            if (Objects.isNull(status)) {
                throw new BusinessException("saas接口异常");
            }
            if (!status.equals(Constant.SUCCESS_CODE)) {
                throw new BusinessException(appVersion.getMessage());
            }
            //正常业务逻辑
            AppVersionRespDto appVersionData = appVersion.getData();
            if (!Objects.isNull(appVersionData)) {
                return appVersionData;
            }

            //异常情况 设置默认值
            AppVersionRespDto appVersionRespDto = new AppVersionRespDto();
            appVersionRespDto.setVersionCode(SaasAppVersionCodeEnum.VERSION_5.getCode());
            return appVersionRespDto;

        } catch (Exception e) {
            log.error("发生异常", e);
        }

        //默认版本
        AppVersionRespDto appVersionRespDto = new AppVersionRespDto();
        appVersionRespDto.setVersionCode(SaasAppVersionCodeEnum.VERSION_5.getCode());

        return appVersionRespDto;
    }

    public List<SchoolBaseVO> querySchoolCampusListByIds(SchoolIdsQuery req){
        if (req == null || CollUtil.isEmpty(req.getSchoolIds())) {
            log.warn("【Saas】-【学校】-【根据学校ids查询学校和校区基本信息】-【入参为空】, 方法名:{}, 入参:{}",
                    "saasSchoolClient.querySchoolCampusListByIds",
                    JSON.toJSONString(req)
            );
            return Collections.emptyList();
        }
        CommonResultRespDto<List<SchoolBaseVO>> listCommonResultRespDto = saasFeignInner.querySchoolCampusListByIds(
                req);
        if (listCommonResultRespDto == null || !listCommonResultRespDto.getSuccess()) {
            log.warn("【Saas】-【学校】-【根据学校ids查询学校和校区基本信息】-【查询失败】, 方法名:{}, 入参:{}，出参:{}",
                    "saasSchoolClient.querySchoolCampusListByIds",
                    JSON.toJSONString(req),
                    JSON.toJSONString(listCommonResultRespDto)
            );
            throw new BusinessException("saas接口异常");
        }
        return listCommonResultRespDto.getData();
    }

}

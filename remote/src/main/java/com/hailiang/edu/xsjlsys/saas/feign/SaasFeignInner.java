package com.hailiang.edu.xsjlsys.saas.feign;

import com.hailiang.edu.xsjlsys.dto.saas.req.*;
import com.hailiang.edu.xsjlsys.dto.saas.resp.*;
import com.hailiang.edu.xsjlsys.saas.config.FeignConfiguration;
import feign.Response;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * saas 对内
 */
@FeignClient(url = "${open.feign.inner}", name = "SaasFeignInner", configuration = FeignConfiguration.class)
public interface SaasFeignInner {

    @PostMapping("/educational/org/v1/tree/list")
    CommonResultRespDto<List<SaasOrgDto>> getSaasOrgTreeListByCondition(@RequestBody SaasOrgTreeListReqDto saasOrgTreeListReqDto);

    @PostMapping("/saas-auth/doLogin")
    CommonResultRespDto<Object> doLogin(@RequestBody SaasLoginReqDto saasLoginReqDto);


    @GetMapping("/saas-auth/getGraphicVerification")
    Response getGraphicVerification(@RequestParam("phone") String phone, @RequestParam("textingScene") String textingScene);


    /**
     * 查询学校下教职工的任职情况
     * http://10.30.5.48:40001/project/221/interface/api/23186
     *
     * @param saasTeachManageStaffTeachListReqDto
     * @return
     */
    @PostMapping("/educational/teachManage/v1/staff/teach/list")
    CommonResultRespDto<List<TeachManageStaffTeachRespDto>> getSaasTeachManageStaffTeachListByCondition(@RequestBody SaasTeachManageStaffTeachReqDto saasTeachManageStaffTeachListReqDto);


    /**
     * 根据租户ID、用户id集合查询教职工列表
     * http://10.30.5.48:40001/project/221/interface/api/29356
     *
     * @param staffUnderTenantReqDto
     * @return
     */
    @PostMapping("/staff/v2/list/under/tenant")
    CommonResultRespDto<List<StaffUnderTenantRespDto>> getSaasStaffListUnderTenant(@RequestBody StaffUnderTenantReqDto staffUnderTenantReqDto);

    /**
     * 获取飞书appId
     *
     * @return
     */
    @PostMapping({"/saas-auth/get_fei_shu_app_id"})
    CommonResultRespDto getFeiShuAppId();

    /**
     * 飞书扫码登录
     *
     * @return
     */
    @PostMapping({"/saas-auth/doLogin"})
    CommonResultRespDto doFeiShuLogin(SaasLoginDingReqDto saasLoginDingReqDto);

    /**
     * 查询组织下多个教职工的角色列表
     *
     * @return
     */
    @PostMapping({"/role/v1/staff/role/list"})
    CommonResultRespDto<List<SaasUserRoleDto>> getUserRoleList(SaasUserRoleReqDto userRoleReqDto);


    /**
     * 学期列表查询
     *
     * @return
     */
    @PostMapping({"/tchTerm/queryTermList"})
    CommonResultRespDto<List<SaasTchTermRespDto>> getTchTermList(SaasTchTermReqDto saasTchTermReqDto);

    /**
     * 根据班级集合查询班级信息
     *
     * @param underClassQueryReqDto
     * @return
     */
    @PostMapping({"/classInfo/v1/list/under/by_class_ids"})
    CommonResultRespDto<List<UnderClassInfoRespDto>> getClassDetailByIds(UnderClassQueryReqDto underClassQueryReqDto);

    @PostMapping({"/saas-auth/user/queryByIdList"})
    CommonResultRespDto<List<WechatUserRespDto>> wechatInfo(WechatUserReqDto wechatUserReqDto);

    @PostMapping({"/saas-auth/user/wechat/unbind"})
    CommonResultRespDto<Integer> wechatUnbind(WechatUserReqDto wechatUserReqDto);

    @PostMapping({"/saas-auth/user/wechat/bind"})
    CommonResultRespDto<String> wechatBind(WechatUserReqDto wechatUserReqDto);

    @PostMapping({"/staff/v1/list/batch/staff"})
    CommonResultRespDto<List<StaffBatchVO>> batchStaff(SaasBatchStaffReqDto saasBatchStaffReqDto);

    @PostMapping({"/staff/v1/list/queryByCampusSectionId"})
    CommonResultRespDto<List<StaffFullInfoVO>> queryByCampusSectionId(StaffUnderSectionQueryDTO saasStaffUnderSectionQueryDTO);

    @PostMapping({"/helpCenter/front/floatBall"})
    CommonResultRespDto<HelpCenterFloatBallRespDto> helpCenterFloatBall(HelpCenterFloatBallReqDto helpCenterFloatBallReqDto);

    @PostMapping({"/feedback/v1/save"})
    CommonResultRespDto<Void> feedback(HelpCenterFeedbackReqDto reqDto);


    @PostMapping({"/app/v1/get_by_tenant_auth"})
    CommonResultRespDto<AppVersionRespDto> appVersion(AppVersionReqDto appVersionReqDto);

    @PostMapping({"/school/v1/school_campus_list"})
    CommonResultRespDto<List<SchoolBaseVO>> querySchoolCampusListByIds(@RequestBody @Valid SchoolIdsQuery var1);
}

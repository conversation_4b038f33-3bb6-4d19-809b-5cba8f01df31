package com.hailiang.edu.xsjlsys.service;

import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dal.entity.XsSmsReportStudent;
import com.hailiang.edu.xsjlsys.dto.UserDto;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.behavior.BehaviorReportDetailDto;
import com.hailiang.edu.xsjlsys.dto.callName.RangeLevelRespDto;
import com.hailiang.edu.xsjlsys.dto.excel.TagDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanTagCommentDto;
import com.hailiang.edu.xsjlsys.dto.point.PlanCommentTagDto;
import com.hailiang.edu.xsjlsys.dto.point.resp.RecordAddRespDto;
import com.hailiang.edu.xsjlsys.dto.point.resp.StudentReportRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.RankSingleScoreDto;
import com.hailiang.edu.xsjlsys.dto.rank.RankStudentScoreDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.BehaviorInfoRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.RankGroupScoreRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.RankStudentScoreRespDto;
import com.hailiang.edu.xsjlsys.dto.report.SelectOptionDto;
import com.hailiang.edu.xsjlsys.dto.sms.SmsReportDetailDto;
import com.hailiang.edu.xsjlsys.dto.sms.SmsReportDto;
import com.hailiang.edu.xsjlsys.query.point.ScoreQuery;
import com.hailiang.edu.xsjlsys.reqo.RecordReq;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

public interface RecordV2Service {

    RecordAddRespDto add(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanTagCommentDto> schoolTagList,
                         List<PlanCommentTagDto> detailObjList, List<PlanTagCommentDto.PlanComment> schoolPlanCommentList) throws BusinessException;

    /**
     * 设置积分明细数据列表,当前明细记录列表 和 上一期明细记录列表
     *
     * @param recordReq
     * @param xsUserInfo
     * @param xsPointRecordList       当前明细记录列表
     * @param beforeXsPointRecordList 上一期明细记录列表
     */
    void setCurrentAndBeforePointRecordListV2(RecordReq recordReq, XsUserInfo xsUserInfo
            , List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList);

    /**
     * 获取小组榜信息
     *
     * @param recordReq
     * @param xsUserInfo
     * @param planGroupDetailDto
     * @param xsPointRecordList       当前明细记录列表
     * @param beforeXsPointRecordList 上一期明细记录列表
     * @return
     */
    RankGroupScoreRespDto group(RecordReq recordReq, XsUserInfo xsUserInfo, PlanGroupDetailDto planGroupDetailDto
            , List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList, Boolean includeMinusData);

    /**
     * 班级积分概况
     *
     * @param recordReq
     * @param xsUserInfo
     * @param planGroupDetailDto
     * @param studentDtoList
     * @param xsPointRecordList       当前明细记录列表
     * @param beforeXsPointRecordList 上一期明细记录列表
     * @return
     */
    BehaviorInfoRespDto classInfo(RecordReq recordReq, XsUserInfo xsUserInfo, PlanGroupDetailDto planGroupDetailDto, List<PlanGroupDetailDto.StudentDto> studentDtoList
            , List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList);

    /**
     * 获取个人榜信息
     *
     * @param recordReq
     * @param xsUserInfo
     * @param xsPointRecordList       当前明细记录列表
     * @param beforeXsPointRecordList 上一期明细记录列表
     * @return
     */
    RankStudentScoreRespDto personal(RecordReq recordReq, XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList, Boolean includeMinusData);

    /**
     * 获取进步榜信息
     *
     * @param rankStudentScoreDtoList
     * @return
     */
    List<RankStudentScoreDto> progress(List<RankStudentScoreDto> rankStudentScoreDtoList, Boolean includeMinusData);


    JSONObject getList(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanGroupDetailDto.StudentDto> studentDtoList) throws BusinessException;


    List<TagDto> tagGetList(RecordReq recordReq, XsUserInfo xsUserInfo) throws BusinessException;

    void del(RecordReq recordReq, XsUserInfo xsUserInfo) throws BusinessException;


    StudentReportRespDto report(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanGroupDetailDto.GroupDto> groupList
            , List<PlanGroupDetailDto.StudentDto> studentDtoList) throws BusinessException;

    ScoreQuery groupSearchCondition(RecordReq recordReq) throws BusinessException;


    void export(RecordReq recordReq, XsUserInfo xsUserInfo
            , PlanGroupDetailDto planGroupDetailDto, List<PlanGroupDetailDto.StudentDto> studentDtoList, HttpServletResponse response) throws BusinessException, IOException;


    List<SelectOptionDto> selectOptionLists(RecordReq recordReq, XsUserInfo xsUserInfo, List<UserDto> userDtoList);


    Long getStudent(RecordReq recordReq) throws MalformedURLException;

    List<SmsReportDto> reportGetLists(RecordReq recordReq);


    void setReportDetailReqParam(RecordReq recordReq);

    List<SmsReportDetailDto> smsReportDetail(RecordReq recordReq, XsSmsReportStudent xsSmsReportStudent);

    XsSmsReportStudent validateSmsReportStudent(RecordReq recordReq);

    BehaviorReportDetailDto getSmsReportExt(XsSmsReportStudent xsSmsReportStudent);

    /**
     * 设置积分明细数据列表,当前明细记录列表
     *
     * @param recordReq
     * @param xsUserInfo
     * @param xsPointRecordList 当前明细记录列表
     * @return
     */
    boolean setXsPointRecordListV2(RecordReq recordReq, XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList);

    /**
     * 获取单项榜信息
     *
     * @param recordReq
     * @param xsUserInfo
     * @param xsPointRecordList 当前明细记录列表
     * @param subIndex          截取数据下标
     * @return
     */
    List<RankSingleScoreDto> single(RecordReq recordReq, XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList, Integer subIndex, Boolean includeEmptyTag);

    /**
     * 获取分层榜信息
     *
     * @param recordReq
     * @param xsUserInfo
     * @param xsPointRecordList 当前明细记录列表
     * @return
     */
    List<RangeLevelRespDto> hierarchy(RecordReq recordReq, XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList);


    /**
     * 设置获取奖状详情参数
     *
     * @param recordReq
     * @param xsSmsReportStudent
     */
    void setAwardDetailReqParam(RecordReq recordReq, XsSmsReportStudent xsSmsReportStudent);
}

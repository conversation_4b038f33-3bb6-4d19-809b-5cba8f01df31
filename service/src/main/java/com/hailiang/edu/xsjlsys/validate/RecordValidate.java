package com.hailiang.edu.xsjlsys.validate;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.dto.ResultJson;
import com.hailiang.edu.xsjlsys.emuns.ApiCodeEnum;
import com.hailiang.edu.xsjlsys.reqo.PrizeReq;
import com.hailiang.edu.xsjlsys.reqo.RecordReq;
import net.sf.oval.ConstraintViolation;
import net.sf.oval.Validator;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@SuppressWarnings("all")
public class RecordValidate extends BaseValidate {

    public ResultJson add(RecordReq recordReq) {

        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(recordReq, "add");
        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        if ((null == recordReq.getPlanCommentIdList() || CollUtil.isEmpty(recordReq.getPlanCommentIdList())) && null == recordReq.getAddPlanCommentInfo()) {
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "getPlanCommentIdList或addPlanCommentInfo 必传其一");
        }

        if (null != recordReq.getAddPlanCommentInfo()) {
            message = validator.validate(recordReq.getAddPlanCommentInfo(), "add");
            if (!message.isEmpty()) {
                String msg = message.get(0).getMessage();
                return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
            }
        }

        if (null != recordReq.getPlanCommentIdList() && !CollUtil.isEmpty(recordReq.getPlanCommentIdList())) {

            for (RecordReq.PlanComments planComments : recordReq.getPlanCommentIdList()) {
                message = validator.validate(planComments, "add");
                if (!message.isEmpty()) {
                    String msg = message.get(0).getMessage();
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                }
            }
        }

        return ResultJson.success(new JSONObject());
    }

}

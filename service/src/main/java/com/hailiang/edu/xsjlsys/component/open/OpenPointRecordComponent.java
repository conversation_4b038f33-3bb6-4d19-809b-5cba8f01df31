package com.hailiang.edu.xsjlsys.component.open;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlsys.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlsys.consts.ApplyLevelConst;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.point.AiPlanCommentTagDto;
import com.hailiang.edu.xsjlsys.dto.stu.StudentDto;
import com.hailiang.edu.xsjlsys.reqo.AiPointReq;
import com.hailiang.edu.xsjlsys.reqo.open.OpenReq;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class OpenPointRecordComponent {

    @Resource
    IdManageComponent idManageComponent;

    public void setBasePointRecordList(OpenReq openReq, List<XsPointRecord> basePointRecordList,
                                       List<PlanGroupDetailDto.StudentDto> studentDtoList, OpenReq.Comment comment) {

        if (CollUtil.isEmpty(studentDtoList)) {
            return;
        }

        for (PlanGroupDetailDto.StudentDto studentDto : studentDtoList) {

            XsPointRecord xsPointRecord = new XsPointRecord();
            xsPointRecord.init();

            xsPointRecord.setId(idManageComponent.nextId());
            //需要专门的文案描述处理类
            xsPointRecord.setContent(comment.getCommentName());

            xsPointRecord.setStudentId(Long.valueOf(studentDto.getStudentId()));
            //方案积分
            xsPointRecord.setScore(NumberUtil.round(comment.getScore(), 7).doubleValue());
            xsPointRecord.setPlanId(openReq.getPlanId());
            xsPointRecord.setChannel(XsPointRecord.CHANNEL_COMMENT);
            xsPointRecord.setScene(XsPointRecord.SCENE_PERSONAL);

            //点评项数据模型
            xsPointRecord.setSaasClassId(String.valueOf(openReq.getSaasClassId()));
            xsPointRecord.setSaasSchoolId(String.valueOf(openReq.getSaasSchoolId()));

            //点评模型相关
            xsPointRecord.setPlanCommentId(comment.getCommentId());
            xsPointRecord.setPlanCommentContent("");
            xsPointRecord.setPlanTagId(comment.getTagId());
            xsPointRecord.setPlanTagName("");

            if (comment.getModuleCode() != null) {
                xsPointRecord.setModuleCode(comment.getModuleCode());
            }

            basePointRecordList.add(xsPointRecord);
        }
    }


    public void setAiPointRecordList(AiPointReq req, List<XsPointRecord> basePointRecordList,
                                     AiPlanCommentTagDto comment, XsUserInfo xsUserInfo) {


        for (String studentId : comment.getStudentIds()) {

            //增加积分记录模型
            XsPointRecord xsPointRecord = new XsPointRecord();
            xsPointRecord.init();

            xsPointRecord.setId(idManageComponent.nextId());
            //需要专门的文案描述处理类
            xsPointRecord.setContent(comment.getContent());

            xsPointRecord.setStudentId(Long.valueOf(studentId));
            xsPointRecord.setUserId(xsUserInfo.getUserId());
            xsPointRecord.setAccountName(xsUserInfo.getAccountName());
            //方案积分
            xsPointRecord.setScore(comment.getScore());
            xsPointRecord.setPlanId(req.getPlanId());
            xsPointRecord.setChannel(XsPointRecord.CHANNEL_COMMENT);
            xsPointRecord.setScene(XsPointRecord.SCENE_PERSONAL);

            //点评项数据模型
            xsPointRecord.setSaasClassId(req.getSaasClassId());
            xsPointRecord.setSaasSchoolId(req.getSaasSchoolId());
            xsPointRecord.setSaasTenantId(xsUserInfo.getTenantId());

            //点评模型相关
            xsPointRecord.setPlanCommentId(comment.getPlanCommentId());
            xsPointRecord.setPlanCommentContent(comment.getContent());
            xsPointRecord.setPlanTagId(comment.getPlanTagId());
            xsPointRecord.setPlanTagName(comment.getPlanTagName());
            xsPointRecord.setModuleCode(comment.getModuleCode());
            xsPointRecord.setApplyLevel(ApplyLevelConst.GENERAL);
            xsPointRecord.setApplyLevel(comment.getApplyLevel());

            //游戏模型缺省值
            xsPointRecord.setGameId(0L);
            xsPointRecord.setGameName("");
            xsPointRecord.setGameRecordId(0L);
            xsPointRecord.setGameRecordTitle("");
            //奖品兑换
            xsPointRecord.setRedemptionRecordId(0L);

            xsPointRecord.setSubjectCode(req.getSubjectCode());

            basePointRecordList.add(xsPointRecord);
        }
    }
}

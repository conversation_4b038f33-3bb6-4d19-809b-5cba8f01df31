package com.hailiang.edu.xsjlsys.service.impl;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlsys.consts.DeletedConst;
import com.hailiang.edu.xsjlsys.consts.DeviceDelConst;
import com.hailiang.edu.xsjlsys.convert.Device.DeviceDtoConvert;
import com.hailiang.edu.xsjlsys.dal.dao.XsDeviceLoginAccountMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsDeviceMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsUserMapper;
import com.hailiang.edu.xsjlsys.dal.entity.XsDevice;
import com.hailiang.edu.xsjlsys.dal.entity.XsDeviceLoginAccount;
import com.hailiang.edu.xsjlsys.dal.entity.XsUser;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.device.DeviceDto;
import com.hailiang.edu.xsjlsys.dto.device.DeviceUserDto;
import com.hailiang.edu.xsjlsys.query.device.XsDeviceLoginAccountQuery;
import com.hailiang.edu.xsjlsys.query.device.XsDeviceQuery;
import com.hailiang.edu.xsjlsys.reqo.DeviceQueryReq;
import com.hailiang.edu.xsjlsys.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service("DeviceService")
@Slf4j
public class DeviceServiceImpl implements DeviceService {

    @Resource
    private XsDeviceMapper xsDeviceMapper;

    @Resource
    private XsDeviceLoginAccountMapper xsDeviceLoginAccountMapper;

    @Resource
    private XsUserMapper xsUserMapper;

    @Resource
    IdManageComponent idManageComponent;

    @Resource
    DeviceDtoConvert deviceDtoConvert;

    @Override
    public String deviceAdd(DeviceQueryReq req) {

        Long deviceId = idManageComponent.nextId();

        XsDevice xsDevice = new XsDevice();
        xsDevice.setDeviceId(deviceId);
        xsDevice.setDeviceName(req.getDeviceName());
        xsDevice.init();

        xsDeviceMapper.insert(xsDevice);

        return String.valueOf(deviceId);
    }

    @Override
    public List<DeviceUserDto> deviceUserLists(DeviceQueryReq req) {

        //根据设备 id 查询用户信息
        XsDeviceLoginAccountQuery xsDeviceLoginAccountQuery = new XsDeviceLoginAccountQuery();
        xsDeviceLoginAccountQuery.setDeviceId(Long.valueOf(req.getDeviceId()));
        xsDeviceLoginAccountQuery.setIsDeleted(DeletedConst.NO);
        xsDeviceLoginAccountQuery.setSortCriteria("update_time");
        List<XsDeviceLoginAccount> deviceLoginAccountList = xsDeviceLoginAccountMapper.getListByCondition(xsDeviceLoginAccountQuery);

        if (CollUtil.isEmpty(deviceLoginAccountList)) {
            return new ArrayList<>();
        }

        Set<Long> userIds = deviceLoginAccountList.stream().map(XsDeviceLoginAccount::getUserId).collect(Collectors.toSet());

        Map<Long, String> userIdToName = new HashMap<>();
        List<XsUser> userList = xsUserMapper.getListByIds(userIds);

        if (!CollUtil.isEmpty(userList)) {
            userIdToName = userList.stream().collect(Collectors.toMap(XsUser::getUserId, XsUser::getAccountName));
        }

        return deviceDtoConvert.getDeviceUserDtos(deviceLoginAccountList, userIdToName);

    }

    @Override
    public void deviceUserDel(DeviceQueryReq req) {

        //删除当前设备账号
        if (DeviceDelConst.CURRENT.equals(req.getDelType())) {
            xsDeviceLoginAccountMapper.deleteByDeviceIdAndUserId(Long.valueOf(req.getDeviceId()), req.getUserIds());
        } else {
            //删除其他设备账号
            xsDeviceLoginAccountMapper.deleteOtherDevice(Long.valueOf(req.getDeviceId()), req.getUserIds());
        }

    }

    @Override
    public List<DeviceDto> deviceLists(XsUserInfo xsUserInfo) {

        XsDeviceLoginAccountQuery xsDeviceLoginAccountQuery = new XsDeviceLoginAccountQuery();
        xsDeviceLoginAccountQuery.setIsDeleted(DeletedConst.NO);
        xsDeviceLoginAccountQuery.setUserId(xsUserInfo.getUserId());
        List<XsDeviceLoginAccount> deviceLoginAccountList = xsDeviceLoginAccountMapper.getListByCondition(xsDeviceLoginAccountQuery);

        if (CollUtil.isEmpty(deviceLoginAccountList)) {
            return new ArrayList<>();
        }

        Set<Long> deviceIds = deviceLoginAccountList.stream().map(XsDeviceLoginAccount::getDeviceId).collect(Collectors.toSet());

        XsDeviceQuery xsDeviceQuery = new XsDeviceQuery();
        xsDeviceQuery.setIsDeleted(DeletedConst.NO);
        xsDeviceQuery.setDeviceIds(deviceIds);
        List<XsDevice> deviceList = xsDeviceMapper.getListByCondition(xsDeviceQuery);

        return deviceDtoConvert.getDeviceDtos(deviceList);

    }

    @Override
    public Long deviceUserAdd(DeviceQueryReq req, XsUserInfo xsUserInfo) {

        XsDeviceLoginAccountQuery xsDeviceLoginAccountQuery = new XsDeviceLoginAccountQuery();
        xsDeviceLoginAccountQuery.setUserId(xsUserInfo.getUserId());
        xsDeviceLoginAccountQuery.setDeviceId(Long.valueOf(req.getDeviceId()));
        XsDeviceLoginAccount rowByCondition = xsDeviceLoginAccountMapper.getRowByCondition(xsDeviceLoginAccountQuery);

        //用户是否已经在该设备上  如果存在 则不写入。
        if (rowByCondition == null) {
            Long id = idManageComponent.nextId();
            XsDeviceLoginAccount xsDeviceLoginAccount = new XsDeviceLoginAccount();
            xsDeviceLoginAccount.setId(id);
            xsDeviceLoginAccount.setDeviceId(Long.valueOf(req.getDeviceId()));
            xsDeviceLoginAccount.setUserId(xsUserInfo.getUserId());
            xsDeviceLoginAccount.init();

            xsDeviceLoginAccountMapper.insert(xsDeviceLoginAccount);
            return id;
        } else {
            //更新时间
            rowByCondition.setUpdateTime(DateUtil.now());
            xsDeviceLoginAccountMapper.updateById(rowByCondition);
            return rowByCondition.getId();
        }

    }

    @Override
    public XsDevice validateDevice(Long deviceId) {

        XsDeviceQuery xsDeviceQuery = new XsDeviceQuery();
        xsDeviceQuery.setDeviceId(deviceId);
        xsDeviceQuery.setIsDeleted(DeletedConst.NO);

        XsDevice rowByCondition = xsDeviceMapper.getRowByCondition(xsDeviceQuery);

        if (rowByCondition == null) {
            throw new BusinessException("无效的设备序号");
        }

        return rowByCondition;
    }

}
package com.hailiang.edu.xsjlsys.component;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hailiang.edu.xsjlsys.component.excel.EvaluateSheetComponent;
import com.hailiang.edu.xsjlsys.component.excel.GroupSheetComponent;
import com.hailiang.edu.xsjlsys.component.excel.PointSheetComponent;
import com.hailiang.edu.xsjlsys.component.excel.StudentSheetComponent;
import com.hailiang.edu.xsjlsys.convert.plan.TagDtoConvert;
import com.hailiang.edu.xsjlsys.dal.entity.XsPlanTag;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.evaluate.EvaluateDetailExcelRespDto;
import com.hailiang.edu.xsjlsys.dto.excel.TagDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.zongping.OverAllRecordRespDto;
import com.hailiang.edu.xsjlsys.dto.zongping.OverAllStudentRespDto;
import com.hailiang.edu.xsjlsys.reqo.OverallRankingReq;
import com.hailiang.edu.xsjlsys.reqo.RecordReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;

@Component
@Slf4j
public class ExcelComponent {

    @Resource
    TagDtoConvert tagDtoConvert;


    @Resource
    GroupSheetComponent groupSheetComponent;

    @Resource
    StudentSheetComponent studentSheetComponent;

    @Resource
    PointSheetComponent pointSheetComponent;
    @Resource
    EvaluateSheetComponent evaluateSheetComponent;

    /**
     * Create a library of cell styles
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();
        CellStyle style;
        Font titleFont = wb.createFont();
        titleFont.setFontHeightInPoints((short) 12);
//        titleFont.setBold(true);
        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(titleFont);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());


        styles.put("title", style);

        Font monthFont = wb.createFont();
        monthFont.setFontHeightInPoints((short) 11);
        monthFont.setColor(IndexedColors.BLACK.getIndex());
        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("header", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
//        style.setDataFormat(wb.createDataFormat().getFormat("#.##"));
        styles.put("cell", style);

        return styles;
    }

    private String getSheetTitle(RecordReq recordReq) {

        String sheet1Title = recordReq.getClassName() + " ";

        if (!StrUtil.isEmpty(recordReq.getStartTime()) && !StrUtil.isEmpty(recordReq.getEndTime())) {
            sheet1Title = sheet1Title + DateUtil.format(DateUtil.parse(recordReq.getStartTime()), "yyyy.MM.dd")
                    + "-"
                    + DateUtil.format(DateUtil.parse(recordReq.getEndTime()), "yyyy.MM.dd");
        }

        return sheet1Title + "积分统计";

    }

    private String getOverallSheetTitle(OverallRankingReq overallRankingReq) {

        String sheet1Title = overallRankingReq.getClassName() + " ";

        if (!StrUtil.isEmpty(overallRankingReq.getStartTime()) && !StrUtil.isEmpty(overallRankingReq.getEndTime())) {
            sheet1Title = sheet1Title + DateUtil.format(DateUtil.parse(overallRankingReq.getStartTime()), "yyyy.MM.dd")
                    + "-"
                    + DateUtil.format(DateUtil.parse(overallRankingReq.getEndTime()), "yyyy.MM.dd");
        }

        return sheet1Title + "积分统计";

    }


    public void doClassInfo(RecordReq recordReq, PlanGroupDetailDto planGroupDetailDto
            , List<XsPointRecord> xsPointRecordList, List<XsPlanTag> xsPlanTagList, HttpServletResponse response
            , Boolean justSheet2) throws IOException {

        List<TagDto> tagDtoList = tagDtoConvert.getTagList(xsPointRecordList, xsPlanTagList);

        String sheet1Title = getSheetTitle(recordReq);

        Workbook book = new SXSSFWorkbook(1000);
        Map<String, CellStyle> mapStyle = createStyles(book);


        //该方式无效
//        sheet1.setDefaultColumnWidth(100);
        if (!justSheet2) {
            Sheet sheet1 = book.createSheet("积分统计");
            if (!CollectionUtils.isEmpty(planGroupDetailDto.getGroupList())) {
                //代表组模版导出
                groupSheetComponent.doGroupSheet1(sheet1, sheet1Title, mapStyle, planGroupDetailDto, xsPointRecordList, tagDtoList, recordReq);
            } else {
                //学生模版导出
                studentSheetComponent.doStudentSheet1(sheet1, sheet1Title, mapStyle, planGroupDetailDto, xsPointRecordList, tagDtoList, recordReq);
            }
        }
        Sheet sheet2 = book.createSheet("积分明细");

        pointSheetComponent.doStudentSheet2(sheet2, mapStyle, planGroupDetailDto, xsPointRecordList);

        //结果响应出去
        OutputStream out = response.getOutputStream();
        try {
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("name", URLEncoder.encode(sheet1Title, "UTF-8"));
            book.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("发生异常", e);
        } finally {
            out.close();
        }

    }


    public void doEvaluateInfo(List<EvaluateDetailExcelRespDto> excelRespDtoList, String title, HttpServletResponse response
    ) throws IOException {

        Workbook book = new SXSSFWorkbook(1000);
        Map<String, CellStyle> mapStyle = createStyles(book);

        Sheet sheet1 = book.createSheet("学生评语");
        evaluateSheetComponent.doEvaluateSheet(sheet1, title, mapStyle, excelRespDtoList);

        //结果响应出去
        OutputStream out = response.getOutputStream();
        try {
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("name", URLEncoder.encode(title, "UTF-8"));
            book.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("发生异常", e);
        } finally {
            out.close();
        }

    }


    public void doOverAll(OverallRankingReq overallRankingReq, List<OverAllRecordRespDto.Record> records,
                          List<OverAllStudentRespDto> overAllStudentRespDtos, HttpServletResponse response
    ) throws IOException {

        String sheet1Title = getOverallSheetTitle(overallRankingReq);

        Workbook book = new SXSSFWorkbook(5000);
        Map<String, CellStyle> mapStyle = createStyles(book);

        Sheet sheet1 = book.createSheet("积分统计");

        //学生模版导出
        studentSheetComponent.doOverallStudentSheet1(sheet1, sheet1Title, mapStyle, overAllStudentRespDtos);

        Sheet sheet2 = book.createSheet("积分明细");
        pointSheetComponent.doOverallStudentSheet2(sheet2, mapStyle, records);

        //结果响应出去
        OutputStream out = response.getOutputStream();
        try {
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("name", URLEncoder.encode(sheet1Title, "UTF-8"));
            book.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("发生异常", e);
        } finally {
            out.close();
        }

    }
}

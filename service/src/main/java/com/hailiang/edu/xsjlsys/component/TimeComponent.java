package com.hailiang.edu.xsjlsys.component;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.hailiang.edu.xsjlsys.query.point.ScoreQuery;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class TimeComponent {

    /**
     * 按照当天为周期统计
     *
     * @param scoreQuery
     */
    public void setDayTimeFrame(ScoreQuery scoreQuery, Date date) {
        //获得一天的开始
        String startTime = DateUtil.beginOfDay(date).toString();
        //获得一天的结束
        String endTime = DateUtil.endOfDay(date).toString();

        scoreQuery.setStartTime(startTime);
        scoreQuery.setEndTime(endTime);
    }

    /**
     * 设置 周为周期的时间窗口
     *
     * @param scoreQuery
     * @param date
     */
    public void setWeekTimeFrame(ScoreQuery scoreQuery, Date date) {
        //本周开始时间
        String startWeek = DateUtil.beginOfWeek(date).toString();
        //本周结束时间
        String endDay = DateUtil.endOfWeek(date).toString();

        scoreQuery.setStartTime(startWeek);
        scoreQuery.setEndTime(endDay);
    }

    /**
     * 获取本周为周期的时间窗口
     *
     * @param scoreQuery
     */
    public void setLocalWeekTimeFrame(ScoreQuery scoreQuery, Date date) {
        //本周开始时间
        String startWeek = DateUtil.beginOfWeek(date).toString();
        //今天结束时间
        String endDay = DateUtil.endOfDay(date).toString();

        scoreQuery.setStartTime(startWeek);
        scoreQuery.setEndTime(endDay);
    }

    /**
     * 设置 月为周期的时间窗口
     *
     * @param scoreQuery
     * @param date
     */
    public void setMonthTimeFrame(ScoreQuery scoreQuery, Date date) {
        //本月开始时间
        String beginMonth = DateUtil.beginOfMonth(date).toString();

        //今天结束时间
        String endDay = DateUtil.endOfMonth(date).toString();

        scoreQuery.setStartTime(beginMonth);
        scoreQuery.setEndTime(endDay);

    }

    /**
     * 获取本月为周期的时间窗口自
     *
     * @param scoreQuery
     */
    public void setLocalMonthTimeFrame(ScoreQuery scoreQuery, Date date) {
        //本月开始时间
        String beginMonth = DateUtil.beginOfMonth(date).toString();

        //今天结束时间
        String endDay = DateUtil.endOfDay(date).toString();

        scoreQuery.setStartTime(beginMonth);
        scoreQuery.setEndTime(endDay);

    }


    public void setDefineTimeFrame(ScoreQuery scoreQuery) {
        //获取开始时间
        String startTime = DateUtil.beginOfDay(DateUtil.parse(scoreQuery.getStartTime())).toString();
        //获取结束时间
        String endTime = DateUtil.endOfDay(DateUtil.parse(scoreQuery.getEndTime())).toString();

        scoreQuery.setStartTime(startTime);
        scoreQuery.setEndTime(endTime);
    }

    /**
     * 设置环比时间
     *
     * @param scoreQuery
     */
    public void setMomDateRange(ScoreQuery scoreQuery) {
        //计算当前周期的时间间隔
        Date date1 = DateUtil.parse(scoreQuery.getStartTime());
        Date date2 = DateUtil.parse(scoreQuery.getEndTime());
        //得出天数间隔
        int betweenDay = (int) (DateUtil.between(date1, date2, DateUnit.DAY) + 1);
        //环比起始时间
        String startMoMTime = DateUtil.offsetDay(date1, -betweenDay).toString();
        String endMoMTime = DateUtil.offsetDay(date2, -betweenDay).toString();

        scoreQuery.setStartTime(startMoMTime);
        scoreQuery.setEndTime(endMoMTime);

    }
}

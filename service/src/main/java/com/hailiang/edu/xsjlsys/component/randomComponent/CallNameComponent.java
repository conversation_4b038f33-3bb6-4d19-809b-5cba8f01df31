package com.hailiang.edu.xsjlsys.component.randomComponent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.hailiang.edu.xsjlsys.dal.biz.BehaviorStorage;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.callName.RangeLevelRespDto;
import com.hailiang.edu.xsjlsys.reqo.CallNameReq;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class CallNameComponent {

    @Resource
    BehaviorStorage behaviorStorage;

    /**
     * 结合缓存机制进行随机点名，当前规则是 点名人数半数内不会重复
     *
     * @param callNameReq
     * @param xsUserInfo
     * @param waitSelectStudentList 所有待抽选学生
     * @return
     */
    public RangeLevelRespDto.StudentDto getCallNameValue(CallNameReq callNameReq, XsUserInfo xsUserInfo, List<RangeLevelRespDto.StudentDto> waitSelectStudentList) {

        //已经抽选过的学生数据
        Set<String> callNameStudentIds = behaviorStorage.getCallNameStudentIds(callNameReq.getSaasClassId(), callNameReq.getPlanId(), xsUserInfo.getUserId());

        //过滤已经抽选过学生数据
        List<RangeLevelRespDto.StudentDto> realWaitSelectStudentList = waitSelectStudentList.stream().
                filter(t -> !callNameStudentIds.contains(t.getStudentId())).collect(Collectors.toList());

        //如果过滤完是空的 则使用过滤前的数据
        if (CollUtil.isEmpty(realWaitSelectStudentList)) {
            realWaitSelectStudentList = waitSelectStudentList;
        }

        //进行随机抽取
        RangeLevelRespDto.StudentDto callNameValue = realWaitSelectStudentList.get(RandomUtil.randomInt(0, realWaitSelectStudentList.size()));

        //更新缓存
        updateRedis(callNameReq, xsUserInfo, waitSelectStudentList, callNameStudentIds, callNameValue);

        return callNameValue;
    }

    private void updateRedis(CallNameReq callNameReq, XsUserInfo xsUserInfo, List<RangeLevelRespDto.StudentDto> waitSelectStudentList, Set<String> callNameStudentIds, RangeLevelRespDto.StudentDto callNameValue) {

        int bigNum = Math.floorDiv(waitSelectStudentList.size(), 2);

        if (bigNum <= 0) {
            return;
        }

        //缓存中没有值 或者 缓存中已点名学生数量小于 待点名学生数量的一半时
        if (CollUtil.isEmpty(callNameStudentIds) || callNameStudentIds.size() < bigNum) {
            //加入缓存
            behaviorStorage.setCallNameStudentId(callNameReq.getSaasClassId(), callNameReq.getPlanId(), xsUserInfo.getUserId(), callNameValue.getStudentId());
        }
        if (callNameStudentIds.size() >= bigNum) {

            //清空缓存
            behaviorStorage.clearCallName(callNameReq.getSaasClassId(), callNameReq.getPlanId(), xsUserInfo.getUserId());
            //再将结果加入缓存
            behaviorStorage.setCallNameStudentId(callNameReq.getSaasClassId(), callNameReq.getPlanId(), xsUserInfo.getUserId(), callNameValue.getStudentId());
        }
    }
}

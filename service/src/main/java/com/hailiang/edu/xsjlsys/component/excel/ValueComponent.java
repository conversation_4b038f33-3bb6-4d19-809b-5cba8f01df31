package com.hailiang.edu.xsjlsys.component.excel;

import cn.hutool.core.util.NumberUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class ValueComponent {

    public Object getShowCellValue(Object obj, boolean isExist) {
        if (!isExist) {
            return "-";
        }

        return obj;
    }

    /**
     * 设置Excel浮点数可做金额等数据统计
     *
     * @param cell  单元格类
     * @param value 传入的值
     */
    public void setExcelValue(Cell cell, Object value, CellStyle style) {
        // 写数据
        cell.setCellStyle(style);
        if (value == null) {
            cell.setCellValue("");
        } else {
            if (value instanceof Integer || value instanceof Long) {

//                cell.setCellValue(NumberUtil.decimalFormat("################.####", Double.valueOf(value.toString())));

                cell.setCellValue(Long.parseLong(value.toString()));
            } else if (value instanceof BigDecimal) {
                double val = NumberUtil.round(((BigDecimal) value).doubleValue(), 2).doubleValue();
                cell.setCellValue(val);
//                cell.setCellValue(NumberUtil.decimalFormat("################.####", val));
            } else {
                cell.setCellValue(value.toString());
            }
//            cell.setCellValue(value.toString());
        }
    }


}

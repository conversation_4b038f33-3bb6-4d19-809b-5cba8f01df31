package com.hailiang.edu.xsjlsys.component.excel;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlsys.dal.entity.XsEvaluateDetail;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.evaluate.EvaluateDetailExcelRespDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class EvaluateSheetComponent {

    @Resource
    ValueComponent valueComponent;


    public List<String> getPointHeader() {
        String[] header = {"学生学号", "学生姓名", "评语"};
        return Arrays.stream(header).collect(Collectors.toList());
    }


    public void doEvaluateSheet(Sheet sheet, String title, Map<String, CellStyle> mapStyle, List<EvaluateDetailExcelRespDto> excelRespDtoList) {
        List<String> headerList = getPointHeader();
        //excel第一行设置
        Row firstRow = sheet.createRow(0);
        int size = headerList.size();
        for (int i = 0; i < size; i++) {

            valueComponent.setExcelValue(firstRow.createCell(i), headerList.get(i), mapStyle.get("header"));
            //亲测有效 但是比例最后再调

            if (i == 2) {
                sheet.setColumnWidth(i, 80 * 256);
            } else {
                sheet.setColumnWidth(i, 15 * 256);
            }
        }

        if (CollectionUtils.isEmpty(excelRespDtoList)) {
            return;
        }

        int indexRow = 1;
            for (EvaluateDetailExcelRespDto detailExcelRespDto : excelRespDtoList) {

                Row evaluateRow = sheet.createRow(indexRow);
                valueComponent.setExcelValue(evaluateRow.createCell(0), detailExcelRespDto.getStudentCode(), mapStyle.get("cell"));
                valueComponent.setExcelValue(evaluateRow.createCell(1),detailExcelRespDto.getStudentName()
                        , mapStyle.get("cell"));

                valueComponent.setExcelValue(evaluateRow.createCell(2), detailExcelRespDto.getEvaluateDate(), mapStyle.get("cell"));
                indexRow++;
        }
    }
}

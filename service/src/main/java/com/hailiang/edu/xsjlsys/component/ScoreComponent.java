package com.hailiang.edu.xsjlsys.component;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.consts.ApiCodeConst;
import com.hailiang.edu.xsjlsys.consts.TimeTypeConst;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.plan.StudentSumScoreDto;
import com.hailiang.edu.xsjlsys.dto.point.GroupScoreDto;
import com.hailiang.edu.xsjlsys.query.point.ScoreQuery;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学生以及小组分数统一组件
 */
@Component
public class ScoreComponent {

    @Resource
    TimeComponent timeComponent;

    /**
     * 根据周期类型计算当前时间范围
     */
    public void setCurTimeFrame(ScoreQuery scoreQuery) throws BusinessException {

        //分情况处理各种类型时间范围
        switch (scoreQuery.getTimeType()) {
            case TimeTypeConst.DAY:
                timeComponent.setDayTimeFrame(scoreQuery, DateUtil.date());
                break;
            case TimeTypeConst.WEEK:
                timeComponent.setLocalWeekTimeFrame(scoreQuery, DateUtil.date());
                break;
            case TimeTypeConst.MONTH:
                timeComponent.setLocalMonthTimeFrame(scoreQuery, DateUtil.date());
                break;
            case TimeTypeConst.DEFINE:
                timeComponent.setDefineTimeFrame(scoreQuery);
                break;
            default:
                throw new BusinessException("时间类型不合法", ApiCodeConst.CODE_ERROR);
        }

    }

/*    public static void main(String[] args) {

        String dateStr1 = "2022-05-01 00:00:00";
        Date date1 = DateUtil.parse(dateStr1);

        String dateStr2 = "2022-05-02 23:59:59";
        Date date2 = DateUtil.parse(dateStr2);

        //相差一个月，31天
        Long betweenDay = DateUtil.between(date1, date2, DateUnit.DAY) + 1;
        System.out.println(betweenDay);
        System.out.println(DateUtil.offsetDay(date1, -betweenDay.intValue()));
        System.out.println(DateUtil.offsetDay(date2, -betweenDay.intValue()));
    }*/


    /**
     * 方案详情页积分数据设置 未进行四舍五入
     *
     * @param planGroupDetailDto
     * @param xsPointRecordList
     */
    public void setDetailScore(PlanGroupDetailDto planGroupDetailDto, List<XsPointRecord> xsPointRecordList) {

        for (PlanGroupDetailDto.GroupDto groupDto : planGroupDetailDto.getGroupList()) {
            GroupScoreDto groupScoreDto = new GroupScoreDto();
            groupScoreDto.setGroupScore(0.00);
            groupScoreDto.setGroupPlusScore(0.00);
            groupScoreDto.setGroupBehaviorPlusScore(0.00);
            groupScoreDto.setGroupMinusScore(0.00);
            groupScoreDto.setGroupBehaviorMinusScore(0.00);

            groupDto.setIsExistPointRecord(false);

            //先计算学生的具体得分情况
            setDetailStudentScore(groupDto.getStudentList(), xsPointRecordList, groupScoreDto);
            int size = groupDto.getStudentList().size();
            if (size > 0) {
                for (PlanGroupDetailDto.StudentDto studentDto : groupDto.getStudentList()) {
                    if (studentDto.getIsExistPointRecord()) {
                        groupDto.setIsExistPointRecord(true);
                        break;
                    }
                }

                groupDto.setAvgScore(BigDecimal.valueOf(NumberUtil.div(groupScoreDto.getGroupScore().doubleValue(), size)));
            } else {
                groupDto.setAvgScore(BigDecimal.valueOf(0));
            }
            groupDto.setSumScore(BigDecimal.valueOf(groupScoreDto.getGroupScore()));
            groupDto.setPlusScore(BigDecimal.valueOf(groupScoreDto.getGroupPlusScore()));
            groupDto.setBehaviorPlusScore(BigDecimal.valueOf(groupScoreDto.getGroupBehaviorPlusScore()));
            groupDto.setMinusScore(BigDecimal.valueOf(groupScoreDto.getGroupMinusScore()));
            groupDto.setBehaviorMinusScore(BigDecimal.valueOf(groupScoreDto.getGroupBehaviorMinusScore()));

        }

        for (PlanGroupDetailDto.UnGroupDto unGroupDto : planGroupDetailDto.getUnGroupList()) {
            GroupScoreDto groupScoreDto = new GroupScoreDto();
            groupScoreDto.setGroupScore(0.00);
            groupScoreDto.setGroupPlusScore(0.00);
            groupScoreDto.setGroupBehaviorPlusScore(0.00);
            groupScoreDto.setGroupMinusScore(0.00);
            groupScoreDto.setGroupBehaviorMinusScore(0.00);

            unGroupDto.setIsExistPointRecord(false);

            //先计算学生的具体得分情况
            setDetailStudentScore(unGroupDto.getStudentList(), xsPointRecordList, groupScoreDto);

            int size = unGroupDto.getStudentList().size();
            if (size > 0) {
                for (PlanGroupDetailDto.StudentDto studentDto : unGroupDto.getStudentList()) {
                    if (studentDto.getIsExistPointRecord()) {
                        unGroupDto.setIsExistPointRecord(true);
                        break;
                    }
                }

                unGroupDto.setAvgScore(BigDecimal.valueOf(NumberUtil.div(groupScoreDto.getGroupScore().doubleValue(), size)));
            } else {
                unGroupDto.setAvgScore(BigDecimal.valueOf(0));
            }

            unGroupDto.setSumScore(BigDecimal.valueOf(groupScoreDto.getGroupScore()));
            unGroupDto.setPlusScore(BigDecimal.valueOf(groupScoreDto.getGroupPlusScore()));
            unGroupDto.setBehaviorPlusScore(BigDecimal.valueOf(groupScoreDto.getGroupBehaviorPlusScore()));
            unGroupDto.setMinusScore(BigDecimal.valueOf(groupScoreDto.getGroupMinusScore()));
            unGroupDto.setBehaviorMinusScore(BigDecimal.valueOf(groupScoreDto.getGroupBehaviorMinusScore()));
        }
    }

    /**
     * 方案详情页积分数据设置 未进行四舍五入
     *
     * @param planGroupDetailDto
     * @param studentScoreMap
     */
    public void setDetailScoreV2(PlanGroupDetailDto planGroupDetailDto, Map<Long, Double> studentScoreMap) {

        for (PlanGroupDetailDto.GroupDto groupDto : planGroupDetailDto.getGroupList()) {
            GroupScoreDto groupScoreDto = new GroupScoreDto();
            groupScoreDto.setGroupScore(0.00);

            //先计算学生的具体得分情况
            setDetailStudentScoreV2(groupDto.getStudentList(), studentScoreMap, groupScoreDto);
            int size = groupDto.getStudentList().size();
            if (size > 0) {
                groupDto.setAvgScore(BigDecimal.valueOf(NumberUtil.div(groupScoreDto.getGroupScore().doubleValue(), size)));
            } else {
                groupDto.setAvgScore(BigDecimal.valueOf(0));
            }
            groupDto.setSumScore(BigDecimal.valueOf(groupScoreDto.getGroupScore()));
        }

        for (PlanGroupDetailDto.UnGroupDto unGroupDto : planGroupDetailDto.getUnGroupList()) {
            GroupScoreDto groupScoreDto = new GroupScoreDto();
            groupScoreDto.setGroupScore(0.00);

            //先计算学生的具体得分情况
            setDetailStudentScoreV2(unGroupDto.getStudentList(), studentScoreMap, groupScoreDto);

            int size = unGroupDto.getStudentList().size();
            if (size > 0) {

                unGroupDto.setAvgScore(BigDecimal.valueOf(NumberUtil.div(groupScoreDto.getGroupScore().doubleValue(), size)));
            } else {
                unGroupDto.setAvgScore(BigDecimal.valueOf(0));
            }

            unGroupDto.setSumScore(BigDecimal.valueOf(groupScoreDto.getGroupScore()));
        }
    }


    private void setDetailStudentScore(List<PlanGroupDetailDto.StudentDto> studentList, List<XsPointRecord> xsPointRecordList
            , GroupScoreDto groupScoreDto) {

        for (PlanGroupDetailDto.StudentDto studentDto : studentList) {
            List<XsPointRecord> xsPointRecordList1 = xsPointRecordList.stream()
                    .filter(xsPointRecord -> xsPointRecord.getStudentId().equals(Long.valueOf(studentDto.getStudentId())))
                    .collect(Collectors.toList());
            studentDto.setIsExistPointRecord(false);
            studentDto.setScore(BigDecimal.valueOf(0.00));
            studentDto.setPlusScore(BigDecimal.valueOf(0.00));
            studentDto.setBehaviorPlusScore(BigDecimal.valueOf(0.00));
            studentDto.setMinusScore(BigDecimal.valueOf(0.00));
            studentDto.setBehaviorMinusScore(BigDecimal.valueOf(0.00));

            if (!CollectionUtils.isEmpty(xsPointRecordList1)) {
                studentDto.setIsExistPointRecord(true);
                //学生总得分
                double allScore = 0.00;
                //正向总得分
                double plusScore = 0.00;
                //负向总得分
                double minusScore = 0.00;
                //行为表现正向总得分
                double behaviorPlusScore = 0.00;
                //行为表现负向总得分
                double behaviorMinusScore = 0.00;


                for (XsPointRecord xsPointRecord : xsPointRecordList1) {
                    allScore = NumberUtil.add(allScore, xsPointRecord.getScore().doubleValue());
                    if (NumberUtil.compare(xsPointRecord.getScore(), 0) >= 0) {
                        plusScore = NumberUtil.add(plusScore, xsPointRecord.getScore().doubleValue());

                        if (xsPointRecord.getChannel().equals(XsPointRecord.CHANNEL_COMMENT)
                                || xsPointRecord.getChannel().equals(XsPointRecord.CHANNEL_GAME)) {
                            behaviorPlusScore = NumberUtil.add(behaviorPlusScore, xsPointRecord.getScore().doubleValue());
                        }

                    } else {
                        minusScore = NumberUtil.add(minusScore, xsPointRecord.getScore().doubleValue());

                        if (xsPointRecord.getChannel().equals(XsPointRecord.CHANNEL_COMMENT)
                                || xsPointRecord.getChannel().equals(XsPointRecord.CHANNEL_GAME)) {
                            behaviorMinusScore = NumberUtil.add(behaviorMinusScore, xsPointRecord.getScore().doubleValue());
                        }

                    }
                }
                studentDto.setScore(BigDecimal.valueOf(allScore));
                studentDto.setPlusScore(BigDecimal.valueOf(plusScore));
                studentDto.setBehaviorPlusScore(BigDecimal.valueOf(behaviorPlusScore));
                studentDto.setMinusScore(BigDecimal.valueOf(minusScore));
                studentDto.setBehaviorMinusScore(BigDecimal.valueOf(behaviorMinusScore));


                groupScoreDto.setGroupScore(NumberUtil.add(groupScoreDto.getGroupScore().doubleValue(), allScore));
                groupScoreDto.setGroupPlusScore(NumberUtil.add(groupScoreDto.getGroupPlusScore().doubleValue(), plusScore));
                groupScoreDto.setGroupBehaviorPlusScore(NumberUtil.add(groupScoreDto.getGroupBehaviorPlusScore().doubleValue(), behaviorPlusScore));
                groupScoreDto.setGroupMinusScore(NumberUtil.add(groupScoreDto.getGroupMinusScore().doubleValue(), minusScore));
                groupScoreDto.setGroupBehaviorMinusScore(NumberUtil.add(groupScoreDto.getGroupBehaviorMinusScore().doubleValue(), behaviorMinusScore));

            }
        }
    }

    private void setDetailStudentScoreV2(List<PlanGroupDetailDto.StudentDto> studentList, Map<Long, Double> studentScoreMap
            , GroupScoreDto groupScoreDto) {

        if (CollUtil.isEmpty(studentList)) {
            return;
        }
        for (PlanGroupDetailDto.StudentDto studentDto : studentList) {
            Double studentScore = studentScoreMap.getOrDefault(Long.valueOf(studentDto.getStudentId()), 0.0);
            studentDto.setScore(BigDecimal.valueOf(studentScore));

            BigDecimal currentGroupScore = BigDecimal.valueOf(groupScoreDto.getGroupScore());
            BigDecimal newGroupScore = NumberUtil.add(currentGroupScore, BigDecimal.valueOf(studentScore));
            groupScoreDto.setGroupScore(newGroupScore.doubleValue());
        }
    }


    /**
     * 排行榜-小组榜接口 分值设置不进行四舍五入
     *
     * @param planGroupDetailDto
     * @param xsPointRecordList
     */
    public void setGroupRecordDetailScore(PlanGroupDetailDto planGroupDetailDto, List<XsPointRecord> xsPointRecordList) {

        for (PlanGroupDetailDto.GroupDto groupDto : planGroupDetailDto.getGroupList()) {
            GroupScoreDto groupScoreDto = new GroupScoreDto();
            groupScoreDto.setGroupScore(0.00);
            groupScoreDto.setGroupPlusScore(0.00);
            groupScoreDto.setGroupMinusScore(0.00);
            groupScoreDto.setGroupBehaviorPlusScore(0.00);
            groupScoreDto.setGroupBehaviorMinusScore(0.00);

            groupDto.setIsExistPointRecord(false);

            //先计算学生的具体得分情况
            setDetailStudentScore(groupDto.getStudentList(), xsPointRecordList, groupScoreDto);
            int size = groupDto.getStudentList().size();
            if (size > 0) {
                for (PlanGroupDetailDto.StudentDto studentDto : groupDto.getStudentList()) {
                    if (studentDto.getIsExistPointRecord()) {
                        groupDto.setIsExistPointRecord(true);
                        break;
                    }
                }
                groupDto.setAvgScore(BigDecimal.valueOf(NumberUtil.div(groupScoreDto.getGroupScore().doubleValue(), size)));
            } else {
                groupDto.setAvgScore(BigDecimal.valueOf(0));
            }

            groupDto.setSumScore(BigDecimal.valueOf(groupScoreDto.getGroupScore()));
        }
    }

    /**
     * 排行榜-个人学生分计算
     *
     * @param studentDtoList
     * @param xsPointRecordList
     */
    public void setStudentRecordDetailScore(List<PlanGroupDetailDto.StudentDto> studentDtoList, List<XsPointRecord> xsPointRecordList) {
        GroupScoreDto groupScoreDto = new GroupScoreDto();
        groupScoreDto.setGroupScore(0.00);
        groupScoreDto.setGroupPlusScore(0.00);
        groupScoreDto.setGroupMinusScore(0.00);
        groupScoreDto.setGroupBehaviorPlusScore(0.00);
        groupScoreDto.setGroupBehaviorMinusScore(0.00);

        setDetailStudentScore(studentDtoList, xsPointRecordList, groupScoreDto);
    }

}

package com.hailiang.edu.xsjlsys.component.excel;

import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlsys.convert.plan.PlanGroupDetailDtoConvert;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.excel.TagDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.reqo.RecordReq;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GroupSheetComponent {

    @Resource
    PlanGroupDetailDtoConvert planGroupDetailDtoConvert;

    @Resource
    ValueComponent valueComponent;

    public List<String> getClassGroupHeader(List<TagDto> tagDtoList, List<Long> exceptChannelIds) {

        String allScoreDesc = "总分(包含已兑换积分)";
        String avgScoreDesc = "人均分(包含已兑换积分)";
        if (!CollectionUtils.isEmpty(exceptChannelIds)) {
            allScoreDesc = "总分(不包含已兑换积分)";
            avgScoreDesc = "人均分(不包含已兑换积分)";
        }

        String[] header = {"排行", "组名", allScoreDesc, avgScoreDesc, "成员", allScoreDesc, "表扬分", "待改进分"};
        List<String> initList = Arrays.stream(header).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tagDtoList)) {
            for (TagDto tagDto : tagDtoList) {
                initList.add(tagDto.getTagName());
            }
        }
        return initList;
    }

    public void doGroupSheet1(Sheet sheet1, String title, Map<String, CellStyle> mapStyle
            , PlanGroupDetailDto planGroupDetailDto
            , List<XsPointRecord> xsPointRecordList, List<TagDto> tagDtoList, RecordReq recordReq) {
        List<String> headerList = getClassGroupHeader(tagDtoList, recordReq.getExceptChannelIds());
        //excel第一行设置
        Row firstRow = sheet1.createRow(0);
        //设置第二行
        Row secondRow = sheet1.createRow(1);
        int size = headerList.size();
        for (int i = 0; i < size; i++) {
            if (i == 0) {
                valueComponent.setExcelValue(firstRow.createCell(0), title, mapStyle.get("title"));
            } else {
                valueComponent.setExcelValue(firstRow.createCell(i), "", mapStyle.get("title"));
            }
            valueComponent.setExcelValue(secondRow.createCell(i), headerList.get(i), mapStyle.get("header"));
            //亲测有效 但是比例最后再调
            sheet1.setColumnWidth(i, 15 * 256);
        }
        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, size - 1));

        //小组按照平均分倒叙排
        planGroupDetailDtoConvert.setGroupAvgScoreSort(planGroupDetailDto);

        //小组成员按照总分倒叙排
        planGroupDetailDtoConvert.setSortStudentList(planGroupDetailDto);

        //进行四舍五入
        planGroupDetailDtoConvert.round(planGroupDetailDto, 2);

        //设置分类得分hashMap
        planGroupDetailDtoConvert.setTagScoreMap(planGroupDetailDto, xsPointRecordList);

        int indexRow = 2;
        for (PlanGroupDetailDto.GroupDto groupDto : planGroupDetailDto.getGroupList()) {
            if (CollectionUtils.isEmpty(groupDto.getStudentList())) {
                continue;
            }
            int stuSize = groupDto.getStudentList().size();
            int startRow = indexRow;
            int endRow = startRow + (stuSize - 1);
            for (int i = 0; i < stuSize; i++) {
                PlanGroupDetailDto.StudentDto studentDto = groupDto.getStudentList().get(i);
                Row groupRow = sheet1.createRow(indexRow);
                if (i == 0) {
                    valueComponent.setExcelValue(groupRow.createCell(0), valueComponent.getShowCellValue(groupDto.getSortVal(), groupDto.getIsExistPointRecord()), mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(1), groupDto.getGroupName(), mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(2), valueComponent.getShowCellValue(groupDto.getSumScore(), groupDto.getIsExistPointRecord()), mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(3), valueComponent.getShowCellValue(groupDto.getAvgScore(), groupDto.getIsExistPointRecord()), mapStyle.get("cell"));
                } else {
                    valueComponent.setExcelValue(groupRow.createCell(0), "", mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(1), "", mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(2), "", mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(3), "", mapStyle.get("cell"));
                }
                valueComponent.setExcelValue(groupRow.createCell(4), studentDto.getStudentName(), mapStyle.get("cell"));
                valueComponent.setExcelValue(groupRow.createCell(5), valueComponent.getShowCellValue(studentDto.getScore(), studentDto.getIsExistPointRecord()), mapStyle.get("cell"));
                //判断不太一样
                long plusCount = xsPointRecordList.stream()
                        .filter(t -> NumberUtil.compare(t.getScore(), 0) >= 0)
                        .filter(t -> !t.getChannel().equals(XsPointRecord.CHANNEL_PRIZE))
                        .filter(t -> t.getStudentId().equals(Long.valueOf(studentDto.getStudentId())))
                        .count();
                valueComponent.setExcelValue(groupRow.createCell(6), valueComponent.getShowCellValue(studentDto.getBehaviorPlusScore()
                        , plusCount > 0), mapStyle.get("cell"));
                //判断不太一样
                long minusCount = xsPointRecordList.stream()
                        .filter(t -> NumberUtil.compare(t.getScore(), 0) < 0)
                        .filter(t -> !t.getChannel().equals(XsPointRecord.CHANNEL_PRIZE))
                        .filter(t -> t.getStudentId().equals(Long.valueOf(studentDto.getStudentId())))
                        .count();
                valueComponent.setExcelValue(groupRow.createCell(7), valueComponent.getShowCellValue(studentDto.getBehaviorMinusScore()
                        , minusCount > 0), mapStyle.get("cell"));
                //前其是固定
                int next = 8;
                for (TagDto tagDto : tagDtoList) {
                    valueComponent.setExcelValue(groupRow.createCell(next), "-", mapStyle.get("cell"));
                    if (!CollectionUtils.isEmpty(studentDto.getTagScoreMap())
                            && studentDto.getTagScoreMap().containsKey(tagDto.getTagName())) {
                        valueComponent.setExcelValue(groupRow.createCell(next), studentDto.getTagScoreMap().get(tagDto.getTagName())
                                , mapStyle.get("cell"));
                    }
                    next++;
                }
                indexRow++;
            }

            if (endRow > startRow) {
                sheet1.addMergedRegion(new CellRangeAddress(startRow, endRow, 0, 0));
                sheet1.addMergedRegion(new CellRangeAddress(startRow, endRow, 1, 1));
                sheet1.addMergedRegion(new CellRangeAddress(startRow, endRow, 2, 2));
                sheet1.addMergedRegion(new CellRangeAddress(startRow, endRow, 3, 3));
            }
        }

        int unIndexRow = indexRow;
        for (PlanGroupDetailDto.UnGroupDto unGroupDto : planGroupDetailDto.getUnGroupList()) {
            if (CollectionUtils.isEmpty(unGroupDto.getStudentList())) {
                continue;
            }
            int stuSize = unGroupDto.getStudentList().size();
            int unStartRow = unIndexRow;
            int unEndRow = unStartRow + (stuSize - 1);


            for (int i = 0; i < stuSize; i++) {
                PlanGroupDetailDto.StudentDto studentDto = unGroupDto.getStudentList().get(i);
                Row groupRow = sheet1.createRow(unIndexRow);
                if (i == 0) {
                    valueComponent.setExcelValue(groupRow.createCell(0), "-", mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(1), unGroupDto.getGroupName(), mapStyle.get("cell"));
                    //setExcelValue(groupRow.createCell(2), getShowCellValue(unGroupDto.getSumScore(), unGroupDto.getIsExistPointRecord()), mapStyle.get("cell"));
                    //setExcelValue(groupRow.createCell(3), getShowCellValue(unGroupDto.getAvgScore(), unGroupDto.getIsExistPointRecord()), mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(2), "-", mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(3), "-", mapStyle.get("cell"));
                } else {
                    valueComponent.setExcelValue(groupRow.createCell(0), "", mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(1), "", mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(2), "", mapStyle.get("cell"));
                    valueComponent.setExcelValue(groupRow.createCell(3), "", mapStyle.get("cell"));
                }
                valueComponent.setExcelValue(groupRow.createCell(4), studentDto.getStudentName(), mapStyle.get("cell"));
                valueComponent.setExcelValue(groupRow.createCell(5), valueComponent.getShowCellValue(studentDto.getScore(), studentDto.getIsExistPointRecord()), mapStyle.get("cell"));
                //判断不太一样
                long plusCount = xsPointRecordList.stream()
                        .filter(t -> NumberUtil.compare(t.getScore(), 0) >= 0)
                        .filter(t -> !t.getChannel().equals(XsPointRecord.CHANNEL_PRIZE))
                        .filter(t -> t.getStudentId().equals(Long.valueOf(studentDto.getStudentId())))
                        .count();
                valueComponent.setExcelValue(groupRow.createCell(6), valueComponent.getShowCellValue(studentDto.getBehaviorPlusScore()
                        , plusCount > 0), mapStyle.get("cell"));
                //判断不太一样
                long minusCount = xsPointRecordList.stream()
                        .filter(t -> NumberUtil.compare(t.getScore(), 0) < 0)
                        .filter(t -> !t.getChannel().equals(XsPointRecord.CHANNEL_PRIZE))
                        .filter(t -> t.getStudentId().equals(Long.valueOf(studentDto.getStudentId())))
                        .count();
                valueComponent.setExcelValue(groupRow.createCell(7), valueComponent.getShowCellValue(studentDto.getBehaviorMinusScore()
                        , minusCount > 0), mapStyle.get("cell"));
                //前其是固定
                int next = 8;
                for (TagDto tagDto : tagDtoList) {
                    valueComponent.setExcelValue(groupRow.createCell(next), "-", mapStyle.get("cell"));
                    if (!CollectionUtils.isEmpty(studentDto.getTagScoreMap())
                            && studentDto.getTagScoreMap().containsKey(tagDto.getTagName())) {
                        valueComponent.setExcelValue(groupRow.createCell(next), studentDto.getTagScoreMap().get(tagDto.getTagName())
                                , mapStyle.get("cell"));
                    }
                    next++;
                }
                unIndexRow++;
            }
            if (unEndRow > unStartRow) {
                sheet1.addMergedRegion(new CellRangeAddress(unStartRow, unEndRow, 0, 0));
                sheet1.addMergedRegion(new CellRangeAddress(unStartRow, unEndRow, 1, 1));
                sheet1.addMergedRegion(new CellRangeAddress(unStartRow, unEndRow, 2, 2));
                sheet1.addMergedRegion(new CellRangeAddress(unStartRow, unEndRow, 3, 3));
            }
        }
    }


}

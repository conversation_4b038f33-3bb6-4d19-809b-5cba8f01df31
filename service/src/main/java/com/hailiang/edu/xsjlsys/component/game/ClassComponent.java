package com.hailiang.edu.xsjlsys.component.game;

import cn.hutool.core.collection.ArrayIter;
import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlsys.dto.game.GroupDto;
import com.hailiang.edu.xsjlsys.dto.game.PlanMeteDataDto;
import com.hailiang.edu.xsjlsys.dto.game.StudentDto;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 班级相关组件
 */
@Component
public class ClassComponent {

    /**
     * 获取班级所有学生列表
     *
     * @param planMeteDataDto
     * @return
     */
    public List<StudentDto> getClassAllStudentList(PlanMeteDataDto planMeteDataDto) {
        List<StudentDto> allStudentList = new ArrayList<>();

        for (GroupDto groupDto : planMeteDataDto.getGroupList()) {
            allStudentList.addAll(groupDto.getStudentList());
        }
        for (GroupDto groupDto : planMeteDataDto.getUnGroupList()) {
            allStudentList.addAll(groupDto.getStudentList());
        }

        //根据学生id去重
        if (!CollectionUtils.isEmpty(allStudentList)) {
            allStudentList = allStudentList.stream()
                    .collect(
                            Collectors.collectingAndThen(
                                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StudentDto::getStudentId))), ArrayList::new)
                    );
        }


        return allStudentList;
    }


    /**
     * 获取全班平均分,并且设置学生列表分值
     *
     * @param allStudentList         全班学生列表
     * @param hasScoreStudentDtoList 上传了成绩的学生列表
     * @return
     */
    public double getClassAvgScoreWithSetScore(List<StudentDto> allStudentList, List<StudentDto> hasScoreStudentDtoList) {

        for (StudentDto studentDto1 : allStudentList) {
            Optional<StudentDto> studentDtoOptional = hasScoreStudentDtoList.stream()
                    .filter(studentDto -> studentDto.getStudentId().equals(studentDto1.getStudentId())).findFirst();
            if (studentDtoOptional.isPresent()) {
                StudentDto studentDto11 = studentDtoOptional.get();
                studentDto1.setScore(studentDto11.getScore());
            }
        }
        double allScore = 0.00;
        int num = 0;
        for (StudentDto studentDto1 : allStudentList) {
            if (studentDto1.getScore() != null) {
                allScore = NumberUtil.add(allScore, studentDto1.getScore().doubleValue());
                num++;
            }
        }
        if (num > 0) {
            return NumberUtil.round(NumberUtil.div(allScore, num), 1).doubleValue();
        }

        return 0.00;
    }


    /**
     * 设置班级分值排名 有分值才有排名，无成绩排名为null
     *
     * @param allStudentList
     */
    public void setStudentScoreSortVal(List<StudentDto> allStudentList) {
        //有分值的班级学生列表
        List<StudentDto> hasScoreStudentList = allStudentList
                .stream()
                .filter(a -> Objects.nonNull(a.getScore()))
                .collect(Collectors.toList());
        //按照分值从高到底聚合
        List<Map.Entry<BigDecimal, List<StudentDto>>> list = hasScoreStudentList.stream().collect(Collectors.groupingBy(StudentDto::getScore)).entrySet()
                .stream().sorted((s1, s2) -> -Double.compare(s1.getKey().doubleValue(), s2.getKey().doubleValue()))
                .collect(Collectors.toList());

        int index = 1;
        for (Map.Entry<BigDecimal, List<StudentDto>> bigDecimalListEntry : list) {
            int size = bigDecimalListEntry.getValue().size();
            for (StudentDto studentDto1 : bigDecimalListEntry.getValue()) {
                studentDto1.setSortVal(index);
            }

            index = index + size;
        }
    }
}

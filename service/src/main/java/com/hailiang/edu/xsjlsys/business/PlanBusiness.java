package com.hailiang.edu.xsjlsys.business;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.dto.ResultJson;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.component.jwt.JwtToken;
import com.hailiang.edu.xsjlsys.consts.GroupTypeConst;
import com.hailiang.edu.xsjlsys.consts.PlanTypeConst;
import com.hailiang.edu.xsjlsys.consts.SortByCodeConst;
import com.hailiang.edu.xsjlsys.consts.SortOrderConst;
import com.hailiang.edu.xsjlsys.dal.biz.BehaviorStorage;
import com.hailiang.edu.xsjlsys.dal.entity.XsAvatar;
import com.hailiang.edu.xsjlsys.dal.entity.XsAwardRecord;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dal.entity.mongodb.AvatarHistory;
import com.hailiang.edu.xsjlsys.dto.UserDto;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.award.AwardRecordRespDto;
import com.hailiang.edu.xsjlsys.dto.game.StudentDto;
import com.hailiang.edu.xsjlsys.dto.game.resp.ErrorStudentRespDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto.GroupDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanTagCommentDto;
import com.hailiang.edu.xsjlsys.dto.seat.SeatArrangeRespDto;
import com.hailiang.edu.xsjlsys.dto.seat.SeatStudentDto;
import com.hailiang.edu.xsjlsys.dto.seat.SeatStudentPlanDto;
import com.hailiang.edu.xsjlsys.dto.share.SharePwdDto;
import com.hailiang.edu.xsjlsys.dto.zongping.resp.MotivationStudentCoinResDto;
import com.hailiang.edu.xsjlsys.dto.zongping.resp.MotivationStudentWalletDto;
import com.hailiang.edu.xsjlsys.dto.zongping.resp.MotivationStudentWalletItemDto;
import com.hailiang.edu.xsjlsys.emuns.ApiCodeEnum;
import com.hailiang.edu.xsjlsys.emuns.MotivationExchangeTypeEnum;
import com.hailiang.edu.xsjlsys.reqo.AwardRecordReq;
import com.hailiang.edu.xsjlsys.reqo.GameReq;
import com.hailiang.edu.xsjlsys.reqo.MotivationCampusIdReq;
import com.hailiang.edu.xsjlsys.reqo.MotivationStudentCoinReq;
import com.hailiang.edu.xsjlsys.reqo.MotivationStudentWalletReq;
import com.hailiang.edu.xsjlsys.reqo.PlanReq;
import com.hailiang.edu.xsjlsys.reqo.SeatQueryReq;
import com.hailiang.edu.xsjlsys.reqo.StudentAvatarReq;
import com.hailiang.edu.xsjlsys.service.AwardService;
import com.hailiang.edu.xsjlsys.service.ClassManageInfoService;
import com.hailiang.edu.xsjlsys.service.EvaluateMotivateService;
import com.hailiang.edu.xsjlsys.service.EvaluateService;
import com.hailiang.edu.xsjlsys.service.PlanService;
import com.hailiang.edu.xsjlsys.service.SeatService;
import com.hailiang.edu.xsjlsys.service.StudentService;
import com.hailiang.edu.xsjlsys.service.TemplateService;
import com.hailiang.edu.xsjlsys.service.ZongPingService;
import com.hailiang.edu.xsjlsys.util.RedisUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Slf4j
public class PlanBusiness {

    @Resource
    private PlanService planService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private StudentService studentService;
    @Resource
    private TemplateService templateService;
    @Resource
    private JwtToken jwtToken;
    @Resource
    private AwardService awardService;
    @Resource
    private ClassManageInfoService classManageInfoService;
    @Resource
    private SeatService seatService;
    @Resource
    private BehaviorStorage behaviorStorage;
    @Resource
    private ZongPingService zongPingService;
    @Resource
    private EvaluateService evaluateService;
    @Resource
    private EvaluateMotivateService evaluateMotivateService;
    @Resource
    RedisUtil redisUtil;

    public static String groupAdd = "groupAdd:%s";

    public static String groupAuto = "groupAuto:%s";

    private final String planDetailFormat = "planDetailSort-saasClassId:planId:userId:%s:%s:%s";

    private final String planListFormat = "planListSort-saasClassId:planId:userId:%s:%s:%s";

    public static String planExit = "planExit:%s";

    public ResultJson groupAdd(PlanReq planReq, XsUserInfo xsUserInfo) {

        //星未来获取 该教师是否有校级分类
        List<PlanTagCommentDto> schoolTagList = zongPingService.getSchoolTagList(planReq.getSaasCampusId(),
                xsUserInfo.getSchoolId(), Long.valueOf(xsUserInfo.getStaffId()));


        String lockKey = String.format(groupAdd, xsUserInfo.getUserId());
        //redis 分布式锁  加锁
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(30, TimeUnit.SECONDS);

        Long planId = null;
        try {
            List<PlanDto> planDtoList = classManageInfoService.getPlanByUser(Collections.singletonList(planReq.getSaasClassId()), planReq.getSaasSchoolId(), xsUserInfo);

            //近半年有积分记录的积分板
            List<Long> hafYearPlanIds = classManageInfoService.getHafYearPlanIds(xsUserInfo.getUserId(), xsUserInfo.getSchoolId());

            planId = planService.groupAdd(planReq, xsUserInfo, schoolTagList, planDtoList, hafYearPlanIds);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }


        JSONObject result = new JSONObject();
        result.put("planId", planId);
        return ResultJson.success(result);

    }

    public ResultJson groupDetail(PlanReq planReq, XsUserInfo xsUserInfo) {

        boolean asc = !Objects.isNull(planReq.getAsc()) && planReq.getAsc();

        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, asc);
        planService.setStudentTree(planGroupDetailDto);

        //缓存用户最新访问的班级方案
        planService.setBehaviorSelectPlan(planReq.getSaasClassId(), planGroupDetailDto.getPlanType(),
                xsUserInfo, planReq.getPlanId().intValue());

        return ResultJson.success(planGroupDetailDto);
    }


    public ResultJson rename(PlanReq planReq, XsUserInfo xsUserInfo) {

        planService.validateJoinPlan(planReq.getPlanId(), xsUserInfo.getUserId(), planReq.getSaasClassId());

        planService.rename(planReq, xsUserInfo);

        return ResultJson.success(new JSONObject());
    }


    public ResultJson detail(PlanReq planReq, XsUserInfo xsUserInfo) {

        if (!CollectionUtils.isEmpty(planReq.getExceptChannelIds())) {
            Set<Long> channelIds = new HashSet<>();
            channelIds.add((long) XsPointRecord.CHANNEL_COMMENT);
            channelIds.add((long) XsPointRecord.CHANNEL_GAME);
            channelIds.add((long) XsPointRecord.CHANNEL_PRIZE);

            for (Long exceptChannelId : planReq.getExceptChannelIds()) {
                if (!channelIds.contains(exceptChannelId)) {
                    throw new BusinessException("exceptChannelIds 非法");
                }
            }
        }

        //获取该方案下组详情
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //重新赋值并缓存sortBy字段
        if (xsUserInfo != null) {
            planService.setSortBy(planReq, planGroupDetailDto, xsUserInfo.getUserId(), planDetailFormat
                    , SortByCodeConst.GROUPID, SortOrderConst.ASC, Boolean.TRUE);
        }
        //需要做场景区分  该场景只需要查询学生分值即可
        Object optSqlCacheValue = redisUtil.get("xssys-optSql-status");

        if (optSqlCacheValue != null) {
            //积分初始值设置
            planService.setInitScoreV2(planGroupDetailDto, 2);

            //进行详细数据积分补充
            planService.setScoreV2(planGroupDetailDto, planReq, xsUserInfo);
        } else {
            //积分初始值设置
            planService.setInitScore(planGroupDetailDto, 2);

            //进行详细数据积分补充
            planService.setScore(planGroupDetailDto, planReq, xsUserInfo);
        }


        boolean temperatureEnable = planService.setTemperatureValAndGetEnable(planGroupDetailDto, planReq, xsUserInfo);
        planGroupDetailDto.setTemperatureEnable(temperatureEnable);

        //进行统一 四舍五入
        planService.round(planGroupDetailDto, 2);

        //进行内存排序
        planService.setSort(planGroupDetailDto, planReq);

        //生成树
        planService.setStudentTree(planGroupDetailDto);


        //只有手动分组的父子帮扶才需要
        if (planGroupDetailDto.getPlanType().equals(PlanTypeConst.MASTER_GROUP)) {

            //设置组学生展示排序
            planService.doStudentSortByTree(planGroupDetailDto);
        }


        // 奖项相关信息补充
        XsAwardRecord xsAwardRecord = awardService.getLastRecord(planReq.getPlanId(), planReq.getSaasClassId());
        if (xsAwardRecord != null) {

            //获取奖项详情
            AwardRecordReq awardRecordReq = new AwardRecordReq();
            awardRecordReq.setPlanId(planReq.getPlanId());
            awardRecordReq.setSaasClassId(planReq.getSaasClassId());
            awardRecordReq.setAwardRecordId(String.valueOf(xsAwardRecord.getAwardRecordId()));
            List<AwardRecordRespDto> awardRecordRespDtoList = awardService.awardRecordDetail(awardRecordReq, xsUserInfo);

            //进行奖项数据组装
            planService.setAwardInfo(planGroupDetailDto, awardRecordRespDtoList);
        }

        // 非体验班且有登录用户，记录缓存
        if (Objects.nonNull(xsUserInfo) && Long.parseLong(planReq.getSaasClassId()) > 0) {
            planService.setBehaviorSelectPlan(planReq.getSaasClassId(), planGroupDetailDto.getPlanType(),
                    xsUserInfo, planReq.getPlanId().intValue());
        }

        // 填充金币信息
        this.fillXwlCoin(planGroupDetailDto, planReq);

        return ResultJson.success(planGroupDetailDto);
    }

    /**
     * 填充星未来金币信息
     *
     * @param planGroupDetailDto
     * @param planReq
     */
    private void fillXwlCoin(PlanGroupDetailDto planGroupDetailDto, PlanReq planReq) {
        if (CollUtil.isEmpty(planGroupDetailDto.getGroupList()) || Objects.isNull(planReq.getSaasCampusId())) {
            return;
        }
        MotivationCampusIdReq campusIdReq = new MotivationCampusIdReq();
        campusIdReq.setSaasCampusId(Convert.toLong(planReq.getSaasCampusId()));
        // 老板校区直接返回
        if (!Objects.equals(true,  evaluateMotivateService.isNewCoinCampus(campusIdReq))) {
            return;
        }
        // 查询学生金币信息
        MotivationStudentCoinReq motivationStudentCoinReq = new MotivationStudentCoinReq();
        motivationStudentCoinReq.setPlanId(planReq.getPlanId());
        motivationStudentCoinReq.setSaasClassId(Convert.toLong(planReq.getSaasClassId()));
        motivationStudentCoinReq.setShowCoinType(1);
        motivationStudentCoinReq.setSaasCampusId(planReq.getSaasCampusId());

        List<MotivationStudentCoinResDto> motivationStudentCoinResInfos = evaluateMotivateService.listStudentPointCoin(
                motivationStudentCoinReq);
        if (CollUtil.isEmpty(motivationStudentCoinResInfos)){
            return;
        }
        Map<Long, BigDecimal> studentCoinMap = motivationStudentCoinResInfos.stream()
                .filter(s -> Objects.nonNull(s.getStudentId()) && Objects.nonNull(s.getCoin()))
                .collect(Collectors.toMap(MotivationStudentCoinResDto::getStudentId,
                        MotivationStudentCoinResDto::getCoin));
        // 填充金币信息
        for (GroupDto groupDto : planGroupDetailDto.getGroupList()) {
            // 分组下所有学生的金币总数
            BigDecimal totalCoin = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(groupDto.getStudentList())){
                for (PlanGroupDetailDto.StudentDto studentDto : groupDto.getStudentList()) {
                    if (studentCoinMap.containsKey(Convert.toLong(studentDto.getStudentId()))){
                        studentDto.setCoinNum(studentCoinMap.get(Convert.toLong(studentDto.getStudentId())));
                        totalCoin = totalCoin.add(studentDto.getCoinNum());
                    }
                }
            }
            if (CollUtil.isNotEmpty(groupDto.getTreeStudentList())){
                for (PlanGroupDetailDto.StudentDto studentDto : groupDto.getTreeStudentList()) {
                    if (studentCoinMap.containsKey(Convert.toLong(studentDto.getStudentId()))){
                        studentDto.setCoinNum(studentCoinMap.get(Convert.toLong(studentDto.getStudentId())));
                    }
                }
            }
            groupDto.setCoinNum(totalCoin);
        }
    }

    public ResultJson miniDetail(PlanReq planReq, XsUserInfo xsUserInfo) {

        if (!CollectionUtils.isEmpty(planReq.getExceptChannelIds())) {
            Set<Long> channelIds = new HashSet<>();
            channelIds.add((long) XsPointRecord.CHANNEL_COMMENT);
            channelIds.add((long) XsPointRecord.CHANNEL_GAME);
            channelIds.add((long) XsPointRecord.CHANNEL_PRIZE);

            for (Long exceptChannelId : planReq.getExceptChannelIds()) {
                if (!channelIds.contains(exceptChannelId)) {
                    throw new BusinessException("exceptChannelIds 非法");
                }
            }
        }

        //获取该方案下组详情
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //需要做场景区分  该场景只需要查询学生分值即可
        Object optSqlCacheValue = redisUtil.get("xssys-optSql-status");

        if (optSqlCacheValue != null) {
            //积分初始值设置
            planService.setInitScoreV2(planGroupDetailDto, 2);
            //进行详细数据积分补充
            planService.setScoreV2(planGroupDetailDto, planReq, xsUserInfo);
        } else {
            //积分初始值设置
            planService.setInitScore(planGroupDetailDto, 2);
            //进行详细数据积分补充
            planService.setScore(planGroupDetailDto, planReq, xsUserInfo);
        }

        //进行统一 四舍五入
        planService.round(planGroupDetailDto, 2);

        //进行内存排序
        planService.setSort(planGroupDetailDto, planReq);

        //生成树
        planService.setStudentTree(planGroupDetailDto);

        //只有手动分组的父子帮扶才需要
        if (planGroupDetailDto.getPlanType().equals(PlanTypeConst.MASTER_GROUP) && planGroupDetailDto.getGroupType().equals(GroupTypeConst.AUTO)) {

            //设置组学生展示排序
            planService.doStudentSortByTree(planGroupDetailDto);
        }

        // 奖项相关信息补充
        XsAwardRecord xsAwardRecord = awardService.getLastRecord(planReq.getPlanId(), planReq.getSaasClassId());
        if (xsAwardRecord != null) {

            //获取奖项详情
            AwardRecordReq awardRecordReq = new AwardRecordReq();
            awardRecordReq.setPlanId(planReq.getPlanId());
            awardRecordReq.setSaasClassId(planReq.getSaasClassId());
            awardRecordReq.setAwardRecordId(String.valueOf(xsAwardRecord.getAwardRecordId()));
            List<AwardRecordRespDto> awardRecordRespDtoList = awardService.awardRecordDetail(awardRecordReq, xsUserInfo);

            //进行奖项数据组装
            planService.setAwardInfo(planGroupDetailDto, awardRecordRespDtoList);
        }

        //设置mini榜单排序
        if (planReq.getSortBy().getCode().equals(SortByCodeConst.AVGSCORE) && planReq.getSortBy().getType().equals(SortOrderConst.DESC)) {

            planService.setGroupMiniSortVal(planGroupDetailDto.getGroupList());
        }

        return ResultJson.success(planGroupDetailDto);
    }

    public ResultJson shareDetail(PlanReq planReq) {

        SharePwdDto sharePwdDto = jwtToken.decodeSharePwd(planReq.getSharePwd());
        if (sharePwdDto == null) {
            throw new BusinessException("密钥异常", ApiCodeEnum.SHARE_ERROR.getCode());
        }

        planReq.setPlanId(sharePwdDto.getPlanId());
        planReq.setSaasClassId(sharePwdDto.getSaasClassId());
        planReq.setSaasSchoolId(sharePwdDto.getSaasSchoolId());
        planReq.setSaasGradeCode(sharePwdDto.getSaasGradeCode());
        planReq.setSaasCampusId(sharePwdDto.getSaasCampusId());


        return detail(planReq, null);

    }


    public ResultJson studentGetList(PlanReq planReq, XsUserInfo xsUserInfo) {

        //进行学生列表数据整理
        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planReq.getSaasClassId(), xsUserInfo);

        List<PlanGroupDetailDto.StudentDto> lastStudentList = planService.getStudentListOrderByStudentName(studentDtoList);

        JSONObject result = new JSONObject();
        result.put("studentList", lastStudentList);

        return ResultJson.success(result);
    }

    public ResultJson studentListV2(PlanReq planReq, XsUserInfo xsUserInfo) {
        String saasCampusId = planReq.getSaasCampusId();
        MotivationCampusIdReq campusIdReq = new MotivationCampusIdReq();
        campusIdReq.setSaasCampusId(Convert.toLong(saasCampusId));
        ResultJson oldRes = studentList(planReq, xsUserInfo);
        // 老板校区直接返回老板数据
        if (!Objects.equals(true,  evaluateMotivateService.isNewCoinCampus(campusIdReq))) {
            return oldRes;
        }
        return fillWalletBalance(planReq, xsUserInfo, oldRes, saasCampusId, MotivationExchangeTypeEnum.XSJL_PERSON);
    }


    public ResultJson studentList(PlanReq planReq, XsUserInfo xsUserInfo) {

        //获取该方案下组详情
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);


        //设置排序方式
        if (xsUserInfo != null) {
            Boolean flushSortCache = Objects.isNull(planReq.getFlushSortCache()) ? Boolean.TRUE : planReq.getFlushSortCache();
            planService.setSortBy(planReq, planGroupDetailDto, xsUserInfo.getUserId(), planListFormat
                    , SortByCodeConst.SCORE, SortOrderConst.DESC, flushSortCache);
        }

        //是否走新逻辑
        Object optSqlCacheValue = redisUtil.get("xssys-optSql-status");
        //需要做场景区分  该场景只需要查询学生分值即可
        if (optSqlCacheValue != null) {
            //积分初始值设置
            planService.setInitScoreV2(planGroupDetailDto, 2);
            //进行详细数据积分补充
            planService.setScoreV2(planGroupDetailDto, planReq, xsUserInfo);
        } else {
            //积分初始值设置
            planService.setInitScore(planGroupDetailDto, 2);
            //进行详细数据积分补充
            planService.setScore(planGroupDetailDto, planReq, xsUserInfo);
        }

        boolean temperatureEnable = planService.setTemperatureValAndGetEnable(planGroupDetailDto, planReq, xsUserInfo);

        // 奖项相关信息补充
        XsAwardRecord xsAwardRecord = awardService.getLastRecord(planReq.getPlanId(), planReq.getSaasClassId());
        if (xsAwardRecord != null) {

            //获取奖项详情
            AwardRecordReq awardRecordReq = new AwardRecordReq();
            awardRecordReq.setPlanId(planReq.getPlanId());
            awardRecordReq.setSaasClassId(planReq.getSaasClassId());
            awardRecordReq.setAwardRecordId(String.valueOf(xsAwardRecord.getAwardRecordId()));
            List<AwardRecordRespDto> awardRecordRespDtoList = awardService.awardRecordDetail(awardRecordReq, xsUserInfo);

            //进行奖项数据组装
            planService.setAwardInfo(planGroupDetailDto, awardRecordRespDtoList);
        }


        //进行学生列表数据整理
        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planGroupDetailDto);

        // 填充学生金币信息(需要开通新校区)
        MotivationCampusIdReq campusIdReq = new MotivationCampusIdReq();
        campusIdReq.setSaasCampusId(Convert.toLong(planReq.getSaasCampusId()));
        // 班级总金币
        BigDecimal classTotalCoin;
        if (Objects.nonNull(xsUserInfo)
                && ObjectUtil.equal(true, evaluateMotivateService.isNewCoinCampus(campusIdReq))) {
            classTotalCoin = planService.setCoinInfo(planReq, studentDtoList, xsUserInfo);
        } else {
            classTotalCoin = BigDecimal.ZERO;
        }

        //进行内存排序
        if (Objects.nonNull(planReq.getSortBy())) {
            planService.setStudentSort(studentDtoList, planReq);
        }

        List<PlanGroupDetailDto.StudentDto> lastStudentList = planService.getLastStudentList(studentDtoList);
        PlanGroupDetailDto.StudentDto allClassStudent = lastStudentList.get(0);
        allClassStudent.setTemperatureVal(temperatureEnable ? 1 : -1);


        // 设置班级总金币
        if (Objects.nonNull(xsUserInfo)
                && ObjectUtil.equal(true, evaluateMotivateService.isNewCoinCampus(campusIdReq))) {
            lastStudentList.forEach(s->{
                if (ObjectUtil.equal(s.getStudentId(),"-1")){
                    s.setCoinNum(classTotalCoin);
                }
            });
        }


        //缓存用户最新访问的班级方案 体验班不缓存
        if (xsUserInfo != null && Long.parseLong(planReq.getSaasClassId()) > 0) {
            planService.setBehaviorSelectPlan(planReq.getSaasClassId(), planGroupDetailDto.getPlanType(),
                    xsUserInfo, planReq.getPlanId().intValue());
        }

        JSONObject result = new JSONObject();
        result.put("studentList", lastStudentList);
        result.put("sortBy", planGroupDetailDto.getSortBy());
        result.put("showStudentAvatar", behaviorStorage.getShowStudentAvatar(xsUserInfo));
        result.put("temperatureEnable", temperatureEnable);
        return ResultJson.success(result);
    }

    public ResultJson classStudentListV2(PlanReq planReq, XsUserInfo xsUserInfo) {
        String saasCampusId = planReq.getSaasCampusId();
        MotivationCampusIdReq campusIdReq = new MotivationCampusIdReq();
        campusIdReq.setSaasCampusId(Convert.toLong(saasCampusId));
        ResultJson oldRes = classStudentList(planReq, xsUserInfo);
        // 老板校区直接返回老板数据
        if (!Objects.equals(true,  evaluateMotivateService.isNewCoinCampus(campusIdReq))) {
            return oldRes;
        }
        return fillWalletBalance(planReq, xsUserInfo, oldRes, saasCampusId, MotivationExchangeTypeEnum.XSJL_CLASS);
    }

    @NotNull
    private ResultJson fillWalletBalance(PlanReq planReq, XsUserInfo xsUserInfo, ResultJson oldRes, String saasCampusId,
            MotivationExchangeTypeEnum exchangeTypeEnum) {
        JSONObject result  = (JSONObject) oldRes.getData();
        List<PlanGroupDetailDto.StudentDto> studentList = (List<PlanGroupDetailDto.StudentDto>) result.get("studentList");
        if (CollUtil.isEmpty(studentList)) {
            return oldRes;
        }
        // 新版查询条件不存在直接返回
        String saasClassId = planReq.getSaasClassId();
        Long planId = planReq.getPlanId();
        if (xsUserInfo == null || StrUtil.isEmpty(xsUserInfo.getStaffId())) {
            log.warn("用户信息为空或者兑换类型为空，不进行金币查询查询:xsUserInfo{}", JSON.toJSONString(xsUserInfo));
            return oldRes;
        }
        String staffId = xsUserInfo.getStaffId();
        MotivationStudentWalletReq walletReq = new MotivationStudentWalletReq();
        walletReq.setPlanId(planId);
        walletReq.setStaffId(Long.valueOf(staffId));
        walletReq.setCampusId(Long.valueOf(saasCampusId));
        walletReq.setClassId(Long.valueOf(saasClassId));
        walletReq.setExchangeType(exchangeTypeEnum.getCode());
        walletReq.setProductSource(planReq.getProductSource());
        MotivationStudentWalletDto walletDto = evaluateMotivateService.walletList(walletReq);
        // 查询金币信息
        result.put("selfTarget", walletDto.getSelfTarget());
        Map<String, BigDecimal> studentBlanceMap = CollStreamUtil.toMap(walletDto.getItemList(),
                item->String.valueOf(item.getStudentId()), MotivationStudentWalletItemDto::getBalance);
        studentList.forEach(item->item.setCoinBalance(studentBlanceMap.getOrDefault(item.getStudentId(), BigDecimal.ZERO)));
        return oldRes;
    }

    public ResultJson classStudentList(PlanReq planReq, XsUserInfo xsUserInfo) {

        //判断是否是班主任
        evaluateService.checkHandleTeacher(planReq.getSaasClassId(), planReq.getSaasSchoolId(), xsUserInfo);

        //获取该方案下组详情
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //设置排序方式
        if (xsUserInfo != null) {
            Boolean flushSortCache = Objects.isNull(planReq.getFlushSortCache()) ? Boolean.TRUE : planReq.getFlushSortCache();
            planService.setSortBy(planReq, planGroupDetailDto, xsUserInfo.getUserId(), planListFormat
                    , SortByCodeConst.SCORE, SortOrderConst.DESC, flushSortCache);
        }

        //积分初始值设置
        planService.setInitScoreV2(planGroupDetailDto, 2);
        //进行详细数据积分补充
        planService.setAllClassScoreV2(planGroupDetailDto, planReq, xsUserInfo);

        //进行学生列表数据整理
        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planGroupDetailDto);

        //进行内存排序
        if (Objects.nonNull(planReq.getSortBy())) {
            planService.setStudentSort(studentDtoList, planReq);
        }

        List<PlanGroupDetailDto.StudentDto> lastStudentList = planService.getLastStudentList(studentDtoList);

        //缓存用户最新访问的班级方案 体验班不缓存
        if (xsUserInfo != null && Long.parseLong(planReq.getSaasClassId()) > 0) {
            planService.setBehaviorSelectPlan(planReq.getSaasClassId(), planGroupDetailDto.getPlanType(),
                    xsUserInfo, planReq.getPlanId().intValue());
        }

        JSONObject result = new JSONObject();
        result.put("studentList", lastStudentList);
        result.put("sortBy", planGroupDetailDto.getSortBy());
        return ResultJson.success(result);
    }


    public ResultJson miniStudentList(PlanReq planReq, XsUserInfo xsUserInfo) {

        //获取该方案下组详情
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //需要做场景区分  该场景只需要查询学生分值即可
        Object optSqlCacheValue = redisUtil.get("xssys-optSql-status");
        if (optSqlCacheValue != null) {
            //积分初始值设置
            planService.setInitScoreV2(planGroupDetailDto, 2);
            //进行详细数据积分补充
            planService.setScoreV2(planGroupDetailDto, planReq, xsUserInfo);
        } else {
            //积分初始值设置
            planService.setInitScore(planGroupDetailDto, 2);
            //进行详细数据积分补充
            planService.setScore(planGroupDetailDto, planReq, xsUserInfo);
        }

        // 奖项相关信息补充
        XsAwardRecord xsAwardRecord = awardService.getLastRecord(planReq.getPlanId(), planReq.getSaasClassId());
        if (xsAwardRecord != null) {

            //获取奖项详情
            AwardRecordReq awardRecordReq = new AwardRecordReq();
            awardRecordReq.setPlanId(planReq.getPlanId());
            awardRecordReq.setSaasClassId(planReq.getSaasClassId());
            awardRecordReq.setAwardRecordId(String.valueOf(xsAwardRecord.getAwardRecordId()));
            List<AwardRecordRespDto> awardRecordRespDtoList = awardService.awardRecordDetail(awardRecordReq, xsUserInfo);

            //进行奖项数据组装
            planService.setAwardInfo(planGroupDetailDto, awardRecordRespDtoList);
        }

        //进行学生列表数据整理
        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planGroupDetailDto);

        //进行内存排序
        planService.setStudentSort(studentDtoList, planReq);

        //mini榜单 需要设置排名
        if (planReq.getSortBy().getCode().equals(SortByCodeConst.SCORE) && planReq.getSortBy().getType().equals(SortOrderConst.DESC)) {
            planService.setMiniSortVal(studentDtoList);
        }

        List<PlanGroupDetailDto.StudentDto> lastStudentList = planService.getLastStudentList(studentDtoList);

        JSONObject result = new JSONObject();
        result.put("studentList", lastStudentList);

        return ResultJson.success(result);

    }

    //学生列表的积分数据
    public List<PlanGroupDetailDto.StudentDto> studentScoreList(PlanReq planReq, XsUserInfo xsUserInfo) {

        //获取该方案下组详情
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //积分初始值设置
        planService.setInitScore(planGroupDetailDto, 2);

        //进行详细数据积分补充
        planService.setScore(planGroupDetailDto, planReq, xsUserInfo);

        //进行学生列表数据整理
        return planService.getPlanStudentList(planGroupDetailDto);
    }


    public ResultJson shareStudentList(PlanReq planReq) {


        SharePwdDto sharePwdDto = jwtToken.decodeSharePwd(planReq.getSharePwd());
        if (sharePwdDto == null) {
            throw new BusinessException("密钥异常", ApiCodeEnum.SHARE_ERROR.getCode());
        }

        planReq.setPlanId(sharePwdDto.getPlanId());
        planReq.setSaasClassId(sharePwdDto.getSaasClassId());


        return studentList(planReq, null);
    }


    public ResultJson exit(PlanReq planReq, XsUserInfo xsUserInfo) {


        String lockKey = String.format(planExit, xsUserInfo.getUserId());
        //redis 分布式锁  加锁
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(30, TimeUnit.SECONDS);
        try {
            //获取当前用户参与的方案数，只有大于1才可以删除
            List<PlanDto> planDtoList = classManageInfoService.getPlanByUser(Collections.singletonList(planReq.getSaasClassId()), planReq.getSaasSchoolId(), xsUserInfo);
            int size = planDtoList.size();
            if (size > 1) {
                planService.exit(planReq, xsUserInfo);
            } else {
                if (size == 1) {
                    PlanDto planDto = planDtoList.get(0);
                    if (!planDto.getPlanId().equals(planReq.getPlanId())) {
                        throw new BusinessException("积分板已被删除", ApiCodeEnum.REFRESH_ERROR.getCode());
                    }
                    if (!CollectionUtils.isEmpty(planDto.getPlanUserList())) {
                        int planUserMember = planDto.getPlanUserList().size();
                        if (planUserMember > 1) {
                            throw new BusinessException("必须保留一个积分板，无法退出");
                        } else {
                            throw new BusinessException("必须保留一个积分板，无法删除");
                        }
                    }
                }
            }

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }


        return ResultJson.success(new JSONObject());
    }


    public ResultJson groupEdit(PlanReq planReq, XsUserInfo xsUserInfo) {


        planService.groupEdit(planReq, xsUserInfo);

        return ResultJson.success(new JSONObject());

    }


    public ResultJson getPlanUserList(PlanReq planReq, XsUserInfo xsUserInfo) {


        List<UserDto> userDtoList = planService.getPlanUserList(planReq, xsUserInfo);

        JSONObject result = new JSONObject();
        result.put("userList", userDtoList);

        return ResultJson.success(result);

    }

    public ResultJson auto(PlanReq planReq, XsUserInfo xsUserInfo) {


        List<StudentDto> allStudentList = studentService.getAllStudentList(planReq.getSaasClassId(), xsUserInfo.getSchoolId());

        List<GameReq.StudentDto> reqStudentList = new ArrayList<>();
        for (PlanReq.StudentDto studentDto : planReq.getStudentList()) {
            GameReq.StudentDto studentDto1 = new GameReq.StudentDto();
            studentDto1.setStudentNo(studentDto.getStudentNo());
            studentDto1.setStudentName(studentDto.getStudentName());
            studentDto1.setScore(studentDto.getScore());
            reqStudentList.add(studentDto1);
        }
        //验证传参
        ErrorStudentRespDto errorStudentRespDto = studentService.validStudentList(reqStudentList, allStudentList, null);
        if (errorStudentRespDto != null) {
            //直接返回错误
            throw new BusinessException("studentList 存在异常数据", ApiCodeEnum.STUDENT_ERROR.getCode(), errorStudentRespDto);
        }

        String lockKey = String.format(groupAuto, planReq.getSaasClassId());
        //redis 分布式锁  加锁
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(30, TimeUnit.SECONDS);

        List<PlanGroupDetailDto.GroupDto> groupDtoList = new ArrayList<>();
        List<PlanGroupDetailDto.UnGroupDto> unGroupDtoList = new ArrayList<>();
        try {
            groupDtoList = planService.auto(planReq, xsUserInfo, allStudentList);
            unGroupDtoList = planService.getUnGroupList(groupDtoList, allStudentList);

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }


        JSONObject result = new JSONObject();
        result.put("groupList", groupDtoList);
        result.put("unGroupList", unGroupDtoList);

        return ResultJson.success(result);
    }

    public ResultJson pointBoot(XsUserInfo xsUserInfo) {

        Boolean pointBoot = planService.pointBoot(xsUserInfo);

        JSONObject result = new JSONObject();
        result.put("showPointBoot", pointBoot);

        return ResultJson.success(result);

    }

    public ResultJson studentAvatarEdit(StudentAvatarReq studentAvatarReq,
                                        XsUserInfo xsUserInfo) {

        //学生原头像列表
        List<XsAvatar> oldAvatarList = Lists.newArrayList();
        // 1.参数校验
        studentService.checkStuEditParam(studentAvatarReq, oldAvatarList);
        // 2.学生头像更新
        studentService.studentAvatarEdit(studentAvatarReq, xsUserInfo, oldAvatarList);

        return ResultJson.success(new JSONObject());
    }

    public ResultJson avatarDel(StudentAvatarReq studentAvatarReq, XsUserInfo xsUserInfo) {
        // 1.参数校验
        List<AvatarHistory> avatarHistoryList = planService
                .checkAvatarDelParam(new HashSet<>(studentAvatarReq.getAvatarIds()));
        // 2.头像删除
        planService.avatarDel(avatarHistoryList, xsUserInfo);
        return ResultJson.success(new JSONObject());
    }

    public ResultJson avatarUpload(StudentAvatarReq studentAvatarReq, XsUserInfo xsUserInfo) {
        // 1.参数校验
        planService.checkAvatarUpload(studentAvatarReq);
        // 2.头像保存
        planService.avatarUpload(studentAvatarReq, xsUserInfo);
        return ResultJson.success(new JSONObject());
    }

    public ResultJson studentSeatList(PlanReq planReq, XsUserInfo xsUserInfo) {

        //获取该方案下组详情
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //需要做场景区分  该场景只需要查询学生分值即可
        Object optSqlCacheValue = redisUtil.get("xssys-optSql-status");

        if (optSqlCacheValue != null) {
            //积分初始值设置
            planService.setInitScoreV2(planGroupDetailDto, 2);
            //进行详细数据积分补充
            planService.setScoreV2(planGroupDetailDto, planReq, xsUserInfo);
        } else {
            //积分初始值设置
            planService.setInitScore(planGroupDetailDto, 2);
            //进行详细数据积分补充
            planService.setScore(planGroupDetailDto, planReq, xsUserInfo);
        }

        boolean temperatureEnable = planService.setTemperatureValAndGetEnable(planGroupDetailDto, planReq, xsUserInfo);

        // 奖项相关信息补充
        XsAwardRecord xsAwardRecord = awardService.getLastRecord(planReq.getPlanId(), planReq.getSaasClassId());
        if (xsAwardRecord != null) {

            //获取奖项详情
            AwardRecordReq awardRecordReq = new AwardRecordReq();
            awardRecordReq.setPlanId(planReq.getPlanId());
            awardRecordReq.setSaasClassId(planReq.getSaasClassId());
            awardRecordReq.setAwardRecordId(String.valueOf(xsAwardRecord.getAwardRecordId()));
            List<AwardRecordRespDto> awardRecordRespDtoList = awardService.awardRecordDetail(awardRecordReq, xsUserInfo);

            //进行奖项数据组装
            planService.setAwardInfo(planGroupDetailDto, awardRecordRespDtoList);
        }

        //进行学生列表数据整理
        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planGroupDetailDto);

        List<PlanGroupDetailDto.StudentDto> lastStudentList = planService.getLastStudentList(studentDtoList);
        PlanGroupDetailDto.StudentDto allClassStudent = lastStudentList.get(0);
        allClassStudent.setTemperatureVal(temperatureEnable ? 1 : -1);

        //缓存用户最新访问的班级方案
        if (xsUserInfo != null) {
            planService.setBehaviorSelectPlan(planReq.getSaasClassId(), planGroupDetailDto.getPlanType(),
                    xsUserInfo, planReq.getPlanId().intValue());
        }

        boolean hasSeat = seatService.hasSeat(planReq.getSaasClassId());

        JSONObject result = new JSONObject();
        result.put("studentList", lastStudentList);
        result.put("sortBy", planGroupDetailDto.getSortBy());
        result.put("alreadySetSeat", hasSeat);
        result.put("seatStudentColList", new ArrayList<>());
        result.put("showStudentAvatar", behaviorStorage.getShowStudentAvatar(xsUserInfo));
        result.put("temperatureEnable", temperatureEnable);

        if (!hasSeat) {
            return ResultJson.success(result);
        }
        List<StudentDto> allStudentList = studentService.classStudentList(planReq.getSaasClassId(), planReq.getSaasClassId());
        if (CollUtil.isNotEmpty(allStudentList)) {
            SeatQueryReq req = new SeatQueryReq();
            req.setSaasClassId(planReq.getSaasClassId());
            req.setSaasSchoolId(planReq.getSaasSchoolId());
            SeatArrangeRespDto seatArrangeRespDto = seatService.seatArrangeGetRow(req, allStudentList, xsUserInfo);

            List<List<SeatStudentDto>> seatStudentColList = seatArrangeRespDto.getSeatStudentColList();
            List<List<SeatStudentPlanDto>> seatStudentPlanDtos = seatService.expandPlanInfo(seatStudentColList, lastStudentList);
            result.put("seatStudentColList", seatStudentPlanDtos);
        }
        return ResultJson.success(result);
    }
}

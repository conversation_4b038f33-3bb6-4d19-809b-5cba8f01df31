package com.hailiang.edu.xsjlsys.component.plan;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlsys.consts.AvatarDataTypeConst;
import com.hailiang.edu.xsjlsys.consts.SysConst;
import com.hailiang.edu.xsjlsys.dal.biz.GroupIconStorage;
import com.hailiang.edu.xsjlsys.dto.plan.AutoBaseDto;
import com.hailiang.edu.xsjlsys.dto.plan.AutoLocationDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto.StudentDto;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 自动分组相关组件
 */
@Component
@Slf4j
public class AutoGroupComponent {

    @Resource
    GroupIconStorage groupIconStorage;

    /**
     * 自动分组操作
     * 师徒帮扶分组逻辑：支持每组4、6、8人三种情况，以每组6人为例
     * 1. 根据成绩给学生排名
     * 2. 设置6人每组，于是将学生分为ABC三（每组人数/2）层，并编号, 无法整除，多出来的人员视为第4（每组人数/2+1）层
     * 3. 分组
     *
     * @param studentDtoList 学生列表
     * @param num            每组人数 4,6,8
     */
    public List<PlanGroupDetailDto.GroupDto> doGroup(List<PlanGroupDetailDto.StudentDto> studentDtoList, Integer num, String saasClassId) {

        Comparator<PlanGroupDetailDto.StudentDto> score = Comparator.comparing(PlanGroupDetailDto.StudentDto::getScore);
        ListUtil.sort(studentDtoList, score.reversed());

        int studentSize = studentDtoList.size();
        AutoBaseDto autoBaseDto = setInfo(studentSize, num);

        //填充位置 数据列表
        List<AutoLocationDto> autoLocationDtoList = doAutoLocation(studentDtoList, autoBaseDto);

        //设置父子关系

        log.info("autoLocationDtoList:" + JSONObject.toJSONString(autoLocationDtoList));
        log.info("autoBaseDto:" + JSONObject.toJSONString(autoBaseDto));
        setColumnParent(autoLocationDtoList, autoBaseDto);

        //合并生成组
        List<PlanGroupDetailDto.GroupDto> groupDtoList = genGroup(autoLocationDtoList, autoBaseDto);

        int size = groupDtoList.size();
        int sortVal = 1;
        for (int i = 0; i < size; i++) {
            String groupName = "第 " + (i + 1) + " 组";
            String icon = groupIconStorage.getGroupRandomIcon(saasClassId);
            groupDtoList.get(i).setGroupName(groupName);
            groupDtoList.get(i).setIcon(icon);
            groupDtoList.get(i).setIconType(SysConst.ICON_TYPE_GROUP);
            groupDtoList.get(i).setDataType(AvatarDataTypeConst.SYSTEM);
            groupDtoList.get(i).setSortVal(sortVal);

            sortVal++;
        }

        //头像数据初始化
        groupIconStorage.delIcon(saasClassId);

        Comparator<PlanGroupDetailDto.GroupDto> sortValComparator = Comparator.comparing(PlanGroupDetailDto.GroupDto::getSortVal);
        ListUtil.sort(groupDtoList, sortValComparator);

        for (PlanGroupDetailDto.GroupDto groupDto : groupDtoList) {
            groupDto.setSortVal(null);
        }


        return groupDtoList;
    }
    private static String translateNumber(int number) {
        if (number < 1 || number > 26) {
            return "Unknown"; // 限制范围为 1-26
        }
        // 将数字转换为对应的字母，A 的 ASCII 码为 65
        return String.valueOf((char) (number + 64));
    }

    private List<PlanGroupDetailDto.GroupDto> genGroup(List<AutoLocationDto> autoLocationDtoList, AutoBaseDto autoBaseDto) {

        List<PlanGroupDetailDto.GroupDto> groupDtoList = new ArrayList<>();
        // 提前处理下序号设置
        for (AutoLocationDto autoLocationDto : autoLocationDtoList) {
            Integer xColumn = autoLocationDto.getXColumn();
            Integer yRow = autoLocationDto.getYRow();
            if (xColumn != null && yRow != null) {
                StudentDto studentDto = autoLocationDto.getStudentDto();
                if (studentDto != null) {
                    studentDto.setSequenceNumber(translateNumber(xColumn) + yRow);
                }
            }
        }

        // 计算实际学生数量
        int studentSize = (int) autoLocationDtoList.stream().filter(dto -> dto.getStudentDto() != null).count();
//        int num = autoBaseDto.getInitColumn() * 2; // num = 每组人数
        int num = autoBaseDto.getInitColumn(); // num = 每组人数

        // 特殊情况：学生数量小于每组人数时，只生成 1 组
        if (studentSize <= num) {
            List<PlanGroupDetailDto.StudentDto> studentList = autoLocationDtoList.stream()
                    .filter(dto -> dto.getStudentDto() != null)
                    .map(AutoLocationDto::getStudentDto)
                    .collect(Collectors.toList());

            if (!studentList.isEmpty()) {
                // 设置第一个学生为大组长
                studentList.get(0).setIsGroupLeader(true);
                PlanGroupDetailDto.GroupDto groupDto = new PlanGroupDetailDto.GroupDto();
                groupDto.setStudentList(studentList);
                groupDtoList.add(groupDto);
            }
            return groupDtoList;
        }


        for (int i = 1; i <= autoBaseDto.getMaxRow(); i++) {
            int first = i;
            int last = (autoBaseDto.getMaxRow() + 1) - i;
            if (first > last) {
                break;
            }
            if (first < last) {
                //合并成组
                PlanGroupDetailDto.GroupDto groupDto = doMerge(autoLocationDtoList, first, last);
                if (groupDto != null) {
                    groupDtoList.add(groupDto);
                }
            }
            if (first == last) {
                // 独自成组
                PlanGroupDetailDto.GroupDto groupDto = doOnly(autoLocationDtoList, first);
                if (groupDto != null) {
                    groupDtoList.add(groupDto);
                }
            }

        }

        return groupDtoList;

    }

    private PlanGroupDetailDto.GroupDto doOnly(List<AutoLocationDto> autoLocationDtoList, int index) {
        Comparator<AutoLocationDto> xColumn = Comparator.comparing(AutoLocationDto::getXColumn);

        List<AutoLocationDto> autoLocationDtoList1 = autoLocationDtoList.stream()
                .filter(t -> t.getYRow().equals(index)).collect(Collectors.toList());

        ListUtil.sort(autoLocationDtoList1, xColumn);

        for (AutoLocationDto autoLocationDto : autoLocationDtoList1) {
            if (autoLocationDto.getStudentDto() != null) {
                //设置为大组长
                autoLocationDto.getStudentDto().setIsGroupLeader(true);
                break;
            }
        }


        List<PlanGroupDetailDto.StudentDto> studentList = new ArrayList<>();
        for (AutoLocationDto autoLocationDto : autoLocationDtoList1) {
            if (autoLocationDto.getStudentDto() != null) {
                studentList.add(autoLocationDto.getStudentDto());
            }
        }

        if (!CollectionUtils.isEmpty(studentList)) {
            PlanGroupDetailDto.GroupDto groupDto = new PlanGroupDetailDto.GroupDto();
            groupDto.setStudentList(studentList);

            return groupDto;
        }

        return null;

    }


    /**
     * 合并成组
     *
     * @param autoLocationDtoList
     * @param first
     * @param last
     * @return
     */
    private PlanGroupDetailDto.GroupDto doMerge(List<AutoLocationDto> autoLocationDtoList, int first, int last) {

        Comparator<AutoLocationDto> xColumn = Comparator.comparing(AutoLocationDto::getXColumn);

        List<AutoLocationDto> autoLocationDtoFirstList = autoLocationDtoList.stream()
                .filter(t -> t.getYRow().equals(first)).collect(Collectors.toList());

        ListUtil.sort(autoLocationDtoFirstList, xColumn);

        PlanGroupDetailDto.StudentDto firstStudent = null;
        for (AutoLocationDto autoLocationDto : autoLocationDtoFirstList) {
            if (autoLocationDto.getStudentDto() != null) {
                firstStudent = autoLocationDto.getStudentDto();
                //设置为大组长
                firstStudent.setIsGroupLeader(true);
                break;
            }
        }


        List<AutoLocationDto> autoLocationDtoLastList = autoLocationDtoList.stream()
                .filter(t -> t.getYRow().equals(last)).collect(Collectors.toList());

        ListUtil.sort(autoLocationDtoLastList, xColumn);

        PlanGroupDetailDto.StudentDto lastStudent = null;
        for (AutoLocationDto autoLocationDto : autoLocationDtoLastList) {
            if (autoLocationDto.getStudentDto() != null) {
                lastStudent = autoLocationDto.getStudentDto();
                //设置为小组长
                lastStudent.setIsSmallGroupLeader(true);
                break;
            }
        }

        if (firstStudent != null & lastStudent != null) {

            List<String> parentIds = new ArrayList<>();
            parentIds.add(firstStudent.getStudentId());
            lastStudent.setParentIds(parentIds);
        }


        List<PlanGroupDetailDto.StudentDto> studentList = new ArrayList<>();

        for (AutoLocationDto autoLocationDto : autoLocationDtoFirstList) {
            if (autoLocationDto.getStudentDto() != null) {
                studentList.add(autoLocationDto.getStudentDto());
            }
        }

        for (AutoLocationDto autoLocationDto : autoLocationDtoLastList) {
            if (autoLocationDto.getStudentDto() != null) {
                studentList.add(autoLocationDto.getStudentDto());
            }
        }

        if (!CollectionUtils.isEmpty(studentList)) {
            PlanGroupDetailDto.GroupDto groupDto = new PlanGroupDetailDto.GroupDto();
            groupDto.setStudentList(studentList);

            return groupDto;
        }

        return null;
    }


    /**
     * 设置横向父子关系
     *
     * @param autoLocationDtoList
     * @param autoBaseDto
     */
    private void setColumnParent(List<AutoLocationDto> autoLocationDtoList, AutoBaseDto autoBaseDto) {
        // 特殊情况：当 maxColumn = 1 时，按纵向（行方向）设置父子关系
        if (autoBaseDto.getMaxColumn() == 1) {
            // 获取第 1 列的所有学生，按 yRow 排序
            List<AutoLocationDto> columnOneList = autoLocationDtoList.stream()
                    .filter(t -> t.getXColumn().equals(1))
                    .sorted(Comparator.comparing(AutoLocationDto::getYRow))
                    .collect(Collectors.toList());

            for (int i = 0; i < columnOneList.size() - 1; i++) {
                PlanGroupDetailDto.StudentDto current = columnOneList.get(i).getStudentDto();
                PlanGroupDetailDto.StudentDto next = columnOneList.get(i + 1).getStudentDto();

                if (current != null) {
                    current.setIsGroupLeader(false);
                    current.setIsSmallGroupLeader(false);
                }
                if (next != null) {
                    next.setIsGroupLeader(false);
                    next.setIsSmallGroupLeader(false);
                }

                if (current != null && next != null) {
                    List<String> parentIds = new ArrayList<>();
                    parentIds.add(current.getStudentId());
                    next.setParentIds(parentIds);
                }
            }
            return;
        }
        // 原有逻辑
        for (int i = 1; i <= autoBaseDto.getMaxRow(); i++) {

            //获取横向列表数据
            int finalI = i;
            List<AutoLocationDto> autoColumnList = autoLocationDtoList.stream()
                    .filter(t -> t.getYRow().equals(finalI)).collect(Collectors.toList());

            for (int j = 1; j < autoBaseDto.getMaxColumn(); j++) {

                PlanGroupDetailDto.StudentDto current = null;
                PlanGroupDetailDto.StudentDto next = null;

                int finalJ = j;
                Optional<AutoLocationDto> optionalAutoLocationDto = autoColumnList.stream().filter(t -> t.getXColumn().equals(finalJ)).findFirst();
                if (optionalAutoLocationDto.isPresent()) {
                    current = optionalAutoLocationDto.get().getStudentDto();
                }

                int finalNext = j + 1;
                Optional<AutoLocationDto> optionalAutoLocationDto1 = autoColumnList.stream().filter(t -> t.getXColumn().equals(finalNext)).findFirst();
                if (optionalAutoLocationDto1.isPresent()) {
                    next = optionalAutoLocationDto1.get().getStudentDto();
                }

                if (current != null) {
                    current.setIsGroupLeader(false);
                    current.setIsSmallGroupLeader(false);
                }

                if (next != null) {
                    next.setIsGroupLeader(false);
                    next.setIsSmallGroupLeader(false);
                }


                if (current != null && next != null) {
                    List<String> parentIds = new ArrayList<>();
                    parentIds.add(current.getStudentId());
                    next.setParentIds(parentIds);
                }

            }
        }

    }


    private List<AutoLocationDto> doAutoLocation(List<PlanGroupDetailDto.StudentDto> studentDtoList, AutoBaseDto autoBaseDto) {
        List<AutoLocationDto> autoLocationDtoList = new ArrayList<>();
        int studentSize = studentDtoList.size();

        // 当学生数量小于每组人数时，紧凑分配到第 1 列
//        if (studentSize < autoBaseDto.getInitColumn() * 2) { // num = initColumn * 2
        if (studentSize <= autoBaseDto.getInitColumn()) { // num = initColumn * 2
            for (int i = 0; i < studentSize; i++) {
                AutoLocationDto autoLocationDto = new AutoLocationDto();
                autoLocationDto.setXColumn(1);
                autoLocationDto.setYRow(i + 1);
                autoLocationDto.setStudentDto(studentDtoList.get(i));
                autoLocationDtoList.add(autoLocationDto);
            }
            return autoLocationDtoList;
        }

        // 原有逻辑处理正常情况
        List<PlanGroupDetailDto.StudentDto> subStudentDtoList = new ArrayList<>();
        for (int i = 1; i <= autoBaseDto.getMaxColumn(); i++) {
            if (autoBaseDto.getQuotientMaxRow() == 0 && autoBaseDto.getMod() > 0) {
                if (i == autoBaseDto.getMaxColumn()) {
                    subStudentDtoList = ListUtil.sub(studentDtoList, 0, autoBaseDto.getMod());
                }
            }
            if (autoBaseDto.getQuotientMaxRow() > 0) {
                subStudentDtoList = subStudentList(studentDtoList, i, autoBaseDto.getQuotientMaxRow());
                if (autoBaseDto.getMod() > 0 && i == autoBaseDto.getMaxColumn()) {
                    int end = (i - 1) * autoBaseDto.getQuotientMaxRow();
                    subStudentDtoList = ListUtil.sub(studentDtoList, end, end + autoBaseDto.getMod());
                }
            }
            log.info("subStudentList:{}", JSONObject.toJSONString(subStudentDtoList));
            for (int j = 1; j <= autoBaseDto.getMaxRow(); j++) {
                AutoLocationDto autoLocationDto = new AutoLocationDto();
                autoLocationDto.setXColumn(i);
                autoLocationDto.setYRow(j);
                autoLocationDto.setStudentDto(null);
                if (j <= subStudentDtoList.size()) {
                    autoLocationDto.setStudentDto(subStudentDtoList.get(j - 1));
                }
                autoLocationDtoList.add(autoLocationDto);
            }
        }
        return autoLocationDtoList;
    }


    public List<PlanGroupDetailDto.StudentDto> subStudentList(List<PlanGroupDetailDto.StudentDto> studentDtoList, int page, int pageSize) {
        int start = (page - 1) * pageSize;
        int end = page * pageSize;
        return ListUtil.sub(studentDtoList, start, end);
    }

    /**
     * 设置基础信息，如余数，最大行数，最大列数
     *
     * @param studentSize
     * @param num
     * @return
     */
    private AutoBaseDto setInfo(int studentSize, int num) {
        AutoBaseDto autoGroupBaseDto = new AutoBaseDto();
        int initColumn = Math.floorDiv(num, 2);
        autoGroupBaseDto.setInitColumn(initColumn);

        // 如果学生数量小于每组人数，调整列数和行数
        if (studentSize <= initColumn) {
            autoGroupBaseDto.setMaxColumn(1); // 只用 1 列
            autoGroupBaseDto.setMaxRow(studentSize); // 行数等于学生数
            autoGroupBaseDto.setMod(0); // 无余数
            autoGroupBaseDto.setQuotientMaxRow(studentSize);
        } else {
            autoGroupBaseDto.setMaxColumn(initColumn);
            int mod = Math.floorMod(studentSize, initColumn);
            if (mod > 0) {
                autoGroupBaseDto.setMaxColumn(initColumn + 1);
            }
            autoGroupBaseDto.setMod(mod);
            int initRow = Math.floorDiv(studentSize, initColumn);
            autoGroupBaseDto.setQuotientMaxRow(initRow);
            if (NumberUtil.compare(initRow, mod) < 0) {
                initRow = mod;
            }
            autoGroupBaseDto.setMaxRow(initRow);
        }

        return autoGroupBaseDto;
    }


}

package com.hailiang.edu.xsjlsys.component.nacos;


import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.xxl.job.core.util.IpUtil;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Component
public class NacosComponent {

    @Value("${spring.application.name}")
    private String serverName;
    @Value("${server.port}")
    private String port;

    @Resource
    private NacosServiceManager nacosServiceManager;

    private static final Logger log = LogManager.getLogger(NacosComponent.class);

    public String deRegisterInstance() {

        Instance instance = new Instance();
        instance.setIp(IpUtil.getIp());
        instance.setPort(Integer.parseInt(port));
        instance.setClusterName("DEFAULT");
        instance.setHealthy(false);

        log.info("NacosComponent.deRegisterInstance ip : {}", IpUtil.getIp());

        try {

            // namingService 一定存在, 无需创建, 所以这边properties空对象
            Properties properties = new Properties();
            nacosServiceManager.getNamingService(properties).deregisterInstance(serverName, instance);

            return "ok";
        } catch (NacosException e) {
            e.printStackTrace();
            return e.getErrMsg();
        }

    }


}

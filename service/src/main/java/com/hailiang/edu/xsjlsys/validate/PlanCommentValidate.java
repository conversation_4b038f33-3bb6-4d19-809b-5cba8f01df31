package com.hailiang.edu.xsjlsys.validate;


import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.dto.ResultJson;
import com.hailiang.edu.xsjlsys.emuns.ApiCodeEnum;
import com.hailiang.edu.xsjlsys.reqo.PlanCommentReq;
import net.sf.oval.ConstraintViolation;
import net.sf.oval.Validator;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@SuppressWarnings("all")
public class PlanCommentValidate extends BaseValidate {


    public ResultJson move(PlanCommentReq planCommentReq) {


        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(planCommentReq, "move");
        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        if (!CollectionUtils.isEmpty(planCommentReq.getPlanTagCommentMoveList())) {

            for (PlanCommentReq.PlanTagCommentMoveDto planTagCommentMoveDto : planCommentReq.getPlanTagCommentMoveList()) {
                message = validator.validate(planTagCommentMoveDto, "move");
                if (!message.isEmpty()) {
                    String msg = message.get(0).getMessage();
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                }

            }
        }

        return ResultJson.success(new JSONObject());
    }
}

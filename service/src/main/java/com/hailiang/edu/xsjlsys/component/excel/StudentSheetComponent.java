package com.hailiang.edu.xsjlsys.component.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlsys.convert.plan.PlanGroupDetailDtoConvert;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.excel.TagDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.zongping.OverAllStudentRespDto;
import com.hailiang.edu.xsjlsys.reqo.RecordReq;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class StudentSheetComponent {

    @Resource
    PlanGroupDetailDtoConvert planGroupDetailDtoConvert;

    @Resource
    ValueComponent valueComponent;


    private List<String> getClassStudentHeader(List<TagDto> tagDtoList, List<Long> exceptChannelIds) {

        String allScoreDesc = "总分(包含已兑换积分)";
        if (!CollectionUtils.isEmpty(exceptChannelIds)) {
            allScoreDesc = "总分(不包含已兑换积分)";
        }

        String[] header = {"排行", "姓名", allScoreDesc, "表扬分", "待改进分"};
        List<String> initList = Arrays.stream(header).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tagDtoList)) {
            for (TagDto tagDto : tagDtoList) {
                initList.add(tagDto.getTagName());
            }
        }
        return initList;
    }

    private List<String> getOverallClassStudentHeader(Set<String> classifyNames, boolean addInitialScore) {

        String[] header = {"排行", "姓名", "总分", "表扬分", "待改进分"};
        if (addInitialScore) {
            header = new String[]{"排行", "姓名", "总分", "表扬分", "待改进分", "初始分"};
        }
        List<String> initList = Arrays.stream(header).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(classifyNames)) {
            initList.addAll(classifyNames);
        }
        return initList;
    }

    public void doStudentSheet1(Sheet sheet1, String title, Map<String, CellStyle> mapStyle
            , PlanGroupDetailDto planGroupDetailDto
            , List<XsPointRecord> xsPointRecordList, List<TagDto> tagDtoList, RecordReq recordReq) {
        List<String> headerList = getClassStudentHeader(tagDtoList, recordReq.getExceptChannelIds());

        //excel第一行设置
        Row firstRow = sheet1.createRow(0);
        //设置第二行
        Row secondRow = sheet1.createRow(1);
        int size = headerList.size();
        for (int i = 0; i < size; i++) {
            if (i == 0) {
                valueComponent.setExcelValue(firstRow.createCell(0), title, mapStyle.get("title"));
            } else {
                valueComponent.setExcelValue(firstRow.createCell(i), "", mapStyle.get("title"));
            }
            valueComponent.setExcelValue(secondRow.createCell(i), headerList.get(i), mapStyle.get("header"));
            //亲测有效 但是比例最后再调
            sheet1.setColumnWidth(i, 15 * 256);
        }

        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, size - 1));


        List<PlanGroupDetailDto.StudentDto> studentDtoList = new ArrayList<>();
        for (PlanGroupDetailDto.UnGroupDto unGroupDto : planGroupDetailDto.getUnGroupList()) {
            studentDtoList.addAll(unGroupDto.getStudentList());
        }

        //学生排名设置
        planGroupDetailDtoConvert.setStudentScoreSort(studentDtoList);

        planGroupDetailDtoConvert.setStudentListTagScoreMap(studentDtoList, xsPointRecordList);

        int indexRow = 2;

        for (PlanGroupDetailDto.StudentDto studentDto : studentDtoList) {

            Row groupRow = sheet1.createRow(indexRow);
            valueComponent.setExcelValue(groupRow.createCell(0), valueComponent.getShowCellValue(studentDto.getSortVal(), studentDto.getIsExistPointRecord()), mapStyle.get("cell"));
            valueComponent.setExcelValue(groupRow.createCell(1), studentDto.getStudentName(), mapStyle.get("cell"));
            valueComponent.setExcelValue(groupRow.createCell(2), valueComponent.getShowCellValue(studentDto.getScore(), studentDto.getIsExistPointRecord()), mapStyle.get("cell"));

            //判断不太一样
            long plusCount = xsPointRecordList.stream()
                    .filter(t -> NumberUtil.compare(t.getScore(), 0) >= 0)
                    .filter(t -> !t.getChannel().equals(XsPointRecord.CHANNEL_PRIZE))
                    .filter(t -> t.getStudentId().equals(Long.valueOf(studentDto.getStudentId())))
                    .count();
            valueComponent.setExcelValue(groupRow.createCell(3), valueComponent.getShowCellValue(studentDto.getBehaviorPlusScore()
                    , plusCount > 0), mapStyle.get("cell"));
            //判断不太一样
            long minusCount = xsPointRecordList.stream()
                    .filter(t -> NumberUtil.compare(t.getScore(), 0) < 0)
                    .filter(t -> !t.getChannel().equals(XsPointRecord.CHANNEL_PRIZE))
                    .filter(t -> t.getStudentId().equals(Long.valueOf(studentDto.getStudentId())))
                    .count();
            valueComponent.setExcelValue(groupRow.createCell(4), valueComponent.getShowCellValue(studentDto.getBehaviorMinusScore()
                    , minusCount > 0), mapStyle.get("cell"));

            int next = 5;
            for (TagDto tagDto : tagDtoList) {
                valueComponent.setExcelValue(groupRow.createCell(next), "-", mapStyle.get("cell"));
                if (!CollectionUtils.isEmpty(studentDto.getTagScoreMap())
                        && studentDto.getTagScoreMap().containsKey(tagDto.getTagName())) {
                    valueComponent.setExcelValue(groupRow.createCell(next), studentDto.getTagScoreMap().get(tagDto.getTagName())
                            , mapStyle.get("cell"));
                }
                next++;
            }
            indexRow++;
        }
    }


    public void doOverallStudentSheet1(Sheet sheet1, String title, Map<String, CellStyle> mapStyle
            , List<OverAllStudentRespDto> overAllStudentRespDtos) {

        //取出所有分类名称
        Set<String> classifyNames = overAllStudentRespDtos.stream()
                .flatMap(student -> student.getClassifyDetailList().stream())
                .map(OverAllStudentRespDto.Classify::getClassifyName)
                .collect(Collectors.toSet());


        //增加列
        boolean addInitialScore = false;
        if (overAllStudentRespDtos.get(0).getInitialScore() != null && overAllStudentRespDtos.get(0).getInitialScore() != 0) {
            addInitialScore = true;
        }
        List<String> headerList = getOverallClassStudentHeader(classifyNames, addInitialScore);


        //excel第一行设置
        Row firstRow = sheet1.createRow(0);
        //设置第二行
        Row secondRow = sheet1.createRow(1);
        int size = headerList.size();
        for (int i = 0; i < size; i++) {
            if (i == 0) {
                valueComponent.setExcelValue(firstRow.createCell(0), title, mapStyle.get("title"));
            } else {
                valueComponent.setExcelValue(firstRow.createCell(i), "", mapStyle.get("title"));
            }
            valueComponent.setExcelValue(secondRow.createCell(i), headerList.get(i), mapStyle.get("header"));
            //亲测有效 但是比例最后再调
            sheet1.setColumnWidth(i, 15 * 256);
        }

        //合并单元格
        sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, size - 1));

        int indexRow = 2;

        Integer i = 1;
        for (OverAllStudentRespDto overAllStudentRespDto : overAllStudentRespDtos) {

            Row groupRow = sheet1.createRow(indexRow);
            valueComponent.setExcelValue(groupRow.createCell(0), i, mapStyle.get("cell"));
            valueComponent.setExcelValue(groupRow.createCell(1), overAllStudentRespDto.getName(), mapStyle.get("cell"));
            valueComponent.setExcelValue(groupRow.createCell(2), overAllStudentRespDto.getScore(), mapStyle.get("cell"));
            valueComponent.setExcelValue(groupRow.createCell(3), overAllStudentRespDto.getPraiseScore(), mapStyle.get("cell"));
            valueComponent.setExcelValue(groupRow.createCell(4), overAllStudentRespDto.getImproveScore(), mapStyle.get("cell"));
            int next = 5;

            if (addInitialScore) {
                valueComponent.setExcelValue(groupRow.createCell(5), overAllStudentRespDto.getInitialScore(), mapStyle.get("cell"));
                next = 6;
            }

            for (String tagName : classifyNames) {
                valueComponent.setExcelValue(groupRow.createCell(next), "-", mapStyle.get("cell"));

                if (!CollUtil.isEmpty(overAllStudentRespDto.getClassifyDetailList())) {

                    OverAllStudentRespDto.Classify classify = overAllStudentRespDto.getClassifyDetailList().stream()
                            .filter(t -> t.getClassifyName().equals(tagName)).findFirst().orElse(null);

                    if (classify != null) {
                        valueComponent.setExcelValue(groupRow.createCell(next), classify.getScore()
                                , mapStyle.get("cell"));
                    }
                }

                next++;
            }
            indexRow++;
            i++;
        }
    }

}

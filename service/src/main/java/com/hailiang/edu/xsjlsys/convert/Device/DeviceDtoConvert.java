package com.hailiang.edu.xsjlsys.convert.Device;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.hailiang.edu.xsjlsys.dal.biz.BehaviorStorage;
import com.hailiang.edu.xsjlsys.dal.entity.XsDevice;
import com.hailiang.edu.xsjlsys.dal.entity.XsDeviceLoginAccount;
import com.hailiang.edu.xsjlsys.dto.device.DeviceDto;
import com.hailiang.edu.xsjlsys.dto.device.DeviceUserDto;
import com.hailiang.edu.xsjlsys.dto.saas.req.SchoolIdsQuery;
import com.hailiang.edu.xsjlsys.dto.saas.resp.SchoolBaseVO;
import com.hailiang.edu.xsjlsys.dto.saas.resp.SchoolCampusPojo;
import com.hailiang.edu.xsjlsys.dto.zongping.resp.InternalConfigRespDto;
import com.hailiang.edu.xsjlsys.dto.zongping.resp.ZongPingResultRespDto;
import com.hailiang.edu.xsjlsys.saas.SaasClient;
import com.hailiang.edu.xsjlsys.util.DateUtil;
import com.hailiang.edu.xsjlsys.zongping.feign.ZongPingFeign;
import java.util.Collections;
import java.util.Comparator;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Data: 2023-04-26 14:01:19
 * @Author: RenJY
 */
@Component
public class DeviceDtoConvert {

    //账号过期天数
    private static final long EXPIRY_TIME_DAYS = 7L;

    @Resource
    BehaviorStorage behaviorStorage;
    @Resource
    ZongPingFeign zongPingFeign;
    @Resource
    SaasClient saasClient;



    public List<DeviceUserDto> getDeviceUserDtos(List<XsDeviceLoginAccount> deviceLoginAccountList, Map<Long, String> userIdToName) {

        if (CollUtil.isEmpty(deviceLoginAccountList)) {
            return new ArrayList<>();
        }

        List<DeviceUserDto> deviceUserDtoList = new ArrayList<>();

        // 提取所有schoolId并去重
        List<Long> schoolIds = deviceLoginAccountList.stream()
                .map(account -> {
                    String schoolId = behaviorStorage.getSelectSchool(account.getUserId());
                    return Long.valueOf(schoolId);
                })
                .distinct()
                .collect(Collectors.toList());

        // 批量获取学校信息
        Map<Long, List<SchoolBaseVO>> schoolCampusMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(schoolIds)) {
            SchoolIdsQuery schoolIdsQuery = new SchoolIdsQuery();
            schoolIdsQuery.setSchoolIds(schoolIds);
            List<SchoolBaseVO> schoolSearchVOS = saasClient.querySchoolCampusListByIds(schoolIdsQuery);
            schoolCampusMap = schoolSearchVOS.stream()
                    .collect(Collectors.groupingBy(SchoolBaseVO::getSchoolId));
        }

        for (XsDeviceLoginAccount xsDeviceLoginAccount : deviceLoginAccountList) {
            DeviceUserDto deviceUserDto = new DeviceUserDto();
            deviceUserDto.setUserName(userIdToName.getOrDefault(xsDeviceLoginAccount.getUserId(), ""));
            deviceUserDto.setUserId(xsDeviceLoginAccount.getUserId());
            String schoolId = behaviorStorage.getSelectSchool(xsDeviceLoginAccount.getUserId());
            deviceUserDto.setSelectSaasSchoolId(schoolId);

            // 获取该学校下的所有校区
            List<SchoolBaseVO> campusList = schoolCampusMap.getOrDefault(Long.valueOf(schoolId), Collections.emptyList());
            if (CollUtil.isEmpty(campusList)) {
                deviceUserDto.setIsExpired(true);
                continue;
            }
            SchoolBaseVO schoolBaseVO = campusList.get(0);
            List<SchoolCampusPojo> campusPojoList = schoolBaseVO.getCampusPojoList();
            // 获取所有校区的配置信息
            List<InternalConfigRespDto> internalConfigRespDtos = new ArrayList<>();
            for (SchoolCampusPojo schoolCampusPojo : campusPojoList) {
                ZongPingResultRespDto<InternalConfigRespDto> internalConfig = zongPingFeign.getInternalConfig(
                        schoolCampusPojo.getId().toString());
                if (internalConfig.getStatus().equals(200)) {
                    internalConfigRespDtos.add(internalConfig.getData());
                }
            }

            // 获取internalConfigRespDtos里面quickLoginFlag = 1且quickLoginDays最大的值
            if (CollUtil.isEmpty(internalConfigRespDtos)) {
                deviceUserDto.setIsExpired(true);
            } else {
                List<InternalConfigRespDto> configRespDtoList = internalConfigRespDtos.stream()
                        .filter(s -> Objects.nonNull(s) && Objects.equals(s.getQuickLoginFlag(), 1)
                                && Objects.nonNull(s.getQuickLoginDays()))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(configRespDtoList)) {
                    deviceUserDto.setIsExpired(true);
                } else {
                    // 获取quickLoginDays最大值
                    Integer quickLoginDays = configRespDtoList.stream()
                            .max(Comparator.comparing(InternalConfigRespDto::getQuickLoginDays))
                            .map(InternalConfigRespDto::getQuickLoginDays)
                            .orElse(null);

                    if (quickLoginDays != null) {
                        long daysBetween = DateUtil.getDaysBetween(
                                DateUtil.parseDate(xsDeviceLoginAccount.getUpdateTime()),
                                DateUtil.parseDate(DateUtil.getDate()));
                        deviceUserDto.setIsExpired(daysBetween > quickLoginDays);
                    } else {
                        deviceUserDto.setIsExpired(true);
                    }
                }
            }
            if (Boolean.FALSE.equals(deviceUserDto.getIsExpired())) {
                deviceUserDtoList.add(deviceUserDto);
            }
        }
        return deviceUserDtoList;
    }

    public boolean isAccountExpired(String loginTime) {

        //登录日期和当前日期相差天数
        long daysBetween = DateUtil.getDaysBetween(DateUtil.parseDate(loginTime), DateUtil.parseDate(DateUtil.getDate()));

        return daysBetween > EXPIRY_TIME_DAYS;
    }


    public List<DeviceDto> getDeviceDtos(List<XsDevice> deviceList) {

        if (CollUtil.isEmpty(deviceList)) {
            return new ArrayList<>();
        }

        List<DeviceDto> deviceDtoList = new ArrayList<>();

        for (XsDevice xsDevice : deviceList) {

            DeviceDto deviceDto = new DeviceDto();
            deviceDto.setDeviceId(String.valueOf(xsDevice.getDeviceId()));
            deviceDto.setDeviceName(xsDevice.getDeviceName());

            deviceDtoList.add(deviceDto);
        }
        return deviceDtoList;
    }
}

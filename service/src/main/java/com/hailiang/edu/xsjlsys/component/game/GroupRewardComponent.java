package com.hailiang.edu.xsjlsys.component.game;

import com.hailiang.edu.xsjlsys.dto.game.GroupDto;
import com.hailiang.edu.xsjlsys.dto.game.RuleDto;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 团队奖结果设置帮助类
 */
@Component
public class GroupRewardComponent {

    /**
     * 设置积分争霸小组团队奖励
     *
     * @param groupDtoList
     * @param groupRuleList
     */
    public void setGroupPointFightReward(List<GroupDto> groupDtoList, List<RuleDto> groupRuleList) {

        for (GroupDto groupDto : groupDtoList) {
            Optional<RuleDto> ruleDtoOptional = groupRuleList
                    .stream().filter(ruleDto -> ruleDto.getRank().equals(groupDto.getSortVal())).findFirst();
            ruleDtoOptional.ifPresent(ruleDto -> groupDto.setAllPlusScore(ruleDto.getScore()));
        }
    }
}

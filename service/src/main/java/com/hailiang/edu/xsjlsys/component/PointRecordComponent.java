package com.hailiang.edu.xsjlsys.component;

import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlsys.convert.point.PointRecordConvert;
import com.hailiang.edu.xsjlsys.dal.entity.*;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanTagCommentDto;
import com.hailiang.edu.xsjlsys.dto.point.AiPlanCommentTagDto;
import com.hailiang.edu.xsjlsys.dto.point.PlanCommentTagDto;
import com.hailiang.edu.xsjlsys.dto.point.PlanCommentTagListDto;
import com.hailiang.edu.xsjlsys.dto.point.resp.RecordAddRespDto;
import com.hailiang.edu.xsjlsys.reqo.AiPointReq;
import com.hailiang.edu.xsjlsys.reqo.RecordReq;
import com.hailiang.edu.xsjlsys.reqo.open.OpenReq;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class PointRecordComponent {

    private final String helpDescTemplate = "帮扶积分：因%s%s";

    public String getHelpDesc(String studentName, String planCommentContent) {
        return String.format(helpDescTemplate, studentName, planCommentContent);
    }

    private final String gameGroupDescTemplate = "在%s中获得小组第%s名";

    public String getGameGroupDesc(String gameName, String rank) {
        return String.format(gameGroupDescTemplate, gameName, rank);
    }

    private final String gamePersonalDescTemplate = "在%s中获得%s称号";

    public String getGamePersonalDesc(String gameName, String rank) {
        return String.format(gamePersonalDescTemplate, gameName, rank);
    }


    @Resource
    PointRecordConvert pointRecordConvert;

    /**
     * 获取积分列表
     *
     * @param recordReq
     * @param xsUserInfo
     * @param studentDtoList
     * @param xsPlan
     * @param planCommentTagObj
     * @return
     */
    public List<XsPointRecord> getPointRecordList(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanGroupDetailDto.StudentDto> studentDtoList
            , XsPlan xsPlan, List<PlanCommentTagListDto> planCommentTagObj, Map<String, Long> commentIdToTargetIdMap) {
        if (CollectionUtils.isEmpty(studentDtoList) || CollectionUtils.isEmpty(planCommentTagObj)) {
            return new ArrayList<>();
        }

        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        for (PlanGroupDetailDto.StudentDto studentDto : studentDtoList) {
            List<XsPointRecord> pointRecords = pointRecordConvert.toAddObj(recordReq, xsUserInfo, xsPlan,
                    planCommentTagObj, studentDto, commentIdToTargetIdMap);

            xsPointRecordList.addAll(pointRecords);
        }

        return xsPointRecordList;

    }


    /**
     * 获取父子关联的积分记录列表
     *
     * @param recordReq
     * @param xsUserInfo
     * @param studentDtoList
     * @param xsPlan
     * @param planCommentTagObj
     * @return
     */
    public List<XsPointRecord> getRelPointRecordList(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanGroupDetailDto.StudentDto> studentDtoList
            , XsPlan xsPlan, List<PlanCommentTagListDto> planCommentTagObj, Map<String, Long> commentIdToTargetIdMap) {
        if (CollectionUtils.isEmpty(studentDtoList)) {
            return new ArrayList<>();
        }

        double pointRetailRate = xsPlan.getPointRetailRate();

        List<XsPointRecord> xsPointRecordList = new ArrayList<>();

        for (PlanCommentTagListDto planCommentTagListDto : planCommentTagObj) {

            for (int i = 0; i < planCommentTagListDto.getNum(); i++) {

                Double score = planCommentTagListDto.getPlanCommentTagDto().getScore();
                for (PlanGroupDetailDto.StudentDto parent : studentDtoList) {
                    //设置师傅的得分
                    parent.setScore(NumberUtil.round(score, 7));
                    //设置帮扶积分数据
                    setParentScore(parent, pointRetailRate, recordReq, xsUserInfo, xsPlan, planCommentTagListDto.getPlanCommentTagDto(),
                            xsPointRecordList, parent, commentIdToTargetIdMap);
                }
            }

        }

        return xsPointRecordList;
    }


    public List<XsPointRecord> getOpenRelPointRecordList(List<PlanGroupDetailDto.StudentDto> studentDtoList,
                                                         XsPlan xsPlan, OpenReq openReq) {
        if (CollectionUtils.isEmpty(studentDtoList)) {
            return new ArrayList<>();
        }

        Map<String, PlanGroupDetailDto.StudentDto> studentDtoMap = studentDtoList.stream().collect(Collectors.
                toMap(PlanGroupDetailDto.StudentDto::getStudentId, studentDto -> studentDto, (k1, k2) -> k1));


        double pointRetailRate = xsPlan.getPointRetailRate();

        List<XsPointRecord> xsPointRecordList = new ArrayList<>();

        for (OpenReq.Comment comment : openReq.getCommentList()) {

            Double score = comment.getScore();
            //拿这个学生的父子关系
            for (OpenReq.Student student : comment.getStudentList()) {

                if (!studentDtoMap.containsKey(String.valueOf(student.getStudentId()))) {
                    continue;
                }
                PlanGroupDetailDto.StudentDto parent = studentDtoMap.get(String.valueOf(student.getStudentId()));
                //设置师傅的得分
                parent.setScore(NumberUtil.round(score, 7));
                //设置帮扶积分数据
                setOpenParentScore(parent, pointRetailRate, xsPlan, comment, xsPointRecordList, parent, openReq);
            }
        }
        return xsPointRecordList;
    }


    private void setParentScore(PlanGroupDetailDto.StudentDto parent, double pointRetailRate
            , RecordReq recordReq, XsUserInfo xsUserInfo, XsPlan xsPlan, PlanCommentTagDto planCommentTagDto
            , List<XsPointRecord> xsPointRecordList, PlanGroupDetailDto.StudentDto initStudent, Map<String, Long> commentIdToTargetIdMap) {

        if (CollectionUtils.isEmpty(parent.getParentList())) {
            return;
        }

        for (PlanGroupDetailDto.StudentDto studentDto : parent.getParentList()) {
            double score = NumberUtil.mul(parent.getScore().doubleValue(), pointRetailRate);
            studentDto.setScore(NumberUtil.round(score, 7));

            String content = getHelpDesc(initStudent.getStudentName(), planCommentTagDto.getContent());

            XsPointRecord xsPointRecord = pointRecordConvert.getPointRecordHelpObj(recordReq, xsUserInfo, studentDto, xsPlan,
                    planCommentTagDto, content, commentIdToTargetIdMap);

            xsPointRecordList.add(xsPointRecord);

            setParentScore(studentDto, pointRetailRate, recordReq, xsUserInfo, xsPlan, planCommentTagDto, xsPointRecordList,
                    initStudent, commentIdToTargetIdMap);
        }
    }


    private void setOpenParentScore(PlanGroupDetailDto.StudentDto parent, double pointRetailRate
            , XsPlan xsPlan, OpenReq.Comment comment, List<XsPointRecord> xsPointRecordList,
                                    PlanGroupDetailDto.StudentDto initStudent, OpenReq openReq) {

        if (CollectionUtils.isEmpty(parent.getParentList())) {
            return;
        }

        for (PlanGroupDetailDto.StudentDto studentDto : parent.getParentList()) {
            double score = NumberUtil.mul(parent.getScore().doubleValue(), pointRetailRate);
            studentDto.setScore(NumberUtil.round(score, 7));

            String content = getHelpDesc(initStudent.getStudentName(), comment.getCommentName());

            XsPointRecord xsPointRecord = pointRecordConvert.getOpenPointRecordHelpObj(studentDto, xsPlan, comment, content, openReq);

            xsPointRecordList.add(xsPointRecord);

            setOpenParentScore(studentDto, pointRetailRate, xsPlan, comment, xsPointRecordList, initStudent, openReq);
        }
    }

    public List<RecordAddRespDto.HelpStudentDto> getHelpStudentList(List<XsPointRecord> xsPointRecordHelpList
            , List<XsClassStudent> xsClassStudentList, int scale) {

        if (CollectionUtils.isEmpty(xsPointRecordHelpList)) {
            return new ArrayList<>();
        }

        //按照学生id聚合
        Map<Long, List<XsPointRecord>> xsPointRecordMap = xsPointRecordHelpList.stream()
                .collect(Collectors.groupingBy(XsPointRecord::getStudentId));

        List<RecordAddRespDto.HelpStudentDto> helpStudentDtoList = new ArrayList<>();
        for (Map.Entry<Long, List<XsPointRecord>> longListEntry : xsPointRecordMap.entrySet()) {

            double helpScore = 0.00;
            for (XsPointRecord xsPointRecord : longListEntry.getValue()) {
                helpScore = NumberUtil.add(helpScore, xsPointRecord.getScore().doubleValue());
            }

            RecordAddRespDto.HelpStudentDto helpStudentDto = new RecordAddRespDto.HelpStudentDto();
            helpStudentDto.setStudentId(String.valueOf(longListEntry.getKey()));
            helpStudentDto.setStudentNo("");
            helpStudentDto.setStudentName("");
            helpStudentDto.setHelpScore(NumberUtil.round(helpScore, scale));
            //查询用户信息
            Optional<XsClassStudent> xsClassStudentOptional = xsClassStudentList.stream()
                    .filter(t -> t.getStudentId().equals(longListEntry.getKey())).findFirst();
            if (xsClassStudentOptional.isPresent()) {
                XsStudent xsStudent = xsClassStudentOptional.get().getXsStudent();
                helpStudentDto.setStudentNo(xsStudent.getStudentNo());
                helpStudentDto.setStudentName(xsStudent.getStudentName());
            }

            helpStudentDtoList.add(helpStudentDto);

        }

        return helpStudentDtoList;
    }

    public List<XsPointRecord> getAiRelPointRecordList(List<PlanGroupDetailDto.StudentDto> studentDtoList, XsPlan xsPlan,
                                                       AiPointReq aiPointReq, List<AiPlanCommentTagDto> aiPlanCommentTagDtoList, XsUserInfo xsUserInfo
           ) {
        if (CollectionUtils.isEmpty(studentDtoList)) {
            return new ArrayList<>();
        }

        Map<String, PlanGroupDetailDto.StudentDto> studentDtoMap = studentDtoList.stream().collect(Collectors.
                toMap(PlanGroupDetailDto.StudentDto::getStudentId, studentDto -> studentDto, (k1, k2) -> k1));


        double pointRetailRate = xsPlan.getPointRetailRate();

        List<XsPointRecord> xsPointRecordList = new ArrayList<>();

        for (AiPlanCommentTagDto aiPlanCommentTagDto : aiPlanCommentTagDtoList) {

            Double score = aiPlanCommentTagDto.getScore();
            //拿这个学生的父子关系
            for (String studentId : aiPlanCommentTagDto.getStudentIds()) {

                if (!studentDtoMap.containsKey(studentId)) {
                    continue;
                }
                PlanGroupDetailDto.StudentDto parent = studentDtoMap.get(studentId);
                //设置师傅的得分
                parent.setScore(NumberUtil.round(score, 7));
                //设置帮扶积分数据
                setAiParentScore(parent, pointRetailRate, xsPlan, aiPlanCommentTagDto, xsPointRecordList, parent, aiPointReq, xsUserInfo);

            }
        }
        return xsPointRecordList;
    }

    private void setAiParentScore(PlanGroupDetailDto.StudentDto parent, double pointRetailRate
            , XsPlan xsPlan, AiPlanCommentTagDto aiPlanCommentTagDto, List<XsPointRecord> xsPointRecordList,
                                  PlanGroupDetailDto.StudentDto initStudent, AiPointReq openReq, XsUserInfo xsUserInfo) {

        if (CollectionUtils.isEmpty(parent.getParentList())) {
            return;
        }

        for (PlanGroupDetailDto.StudentDto studentDto : parent.getParentList()) {
            double score = NumberUtil.mul(parent.getScore().doubleValue(), pointRetailRate);
            studentDto.setScore(NumberUtil.round(score, 7));

            String content = getHelpDesc(initStudent.getStudentName(), aiPlanCommentTagDto.getContent());

            XsPointRecord xsPointRecord = pointRecordConvert.getAiPointRecordHelpObj(openReq, xsUserInfo, studentDto
                    , xsPlan, aiPlanCommentTagDto, content);

            xsPointRecordList.add(xsPointRecord);

            setAiParentScore(studentDto, pointRetailRate, xsPlan, aiPlanCommentTagDto, xsPointRecordList, initStudent, openReq, xsUserInfo);
        }
    }

}

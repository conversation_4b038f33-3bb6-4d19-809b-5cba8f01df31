package com.hailiang.edu.xsjlsys.validate;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.dto.ResultJson;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupTemplateTreeDto;
import com.hailiang.edu.xsjlsys.emuns.ApiCodeEnum;
import com.hailiang.edu.xsjlsys.reqo.PlanReq;
import net.sf.oval.ConstraintViolation;
import net.sf.oval.Validator;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Component
@SuppressWarnings("all")
public class PlanValidate extends BaseValidate {


    public ResultJson groupAdd(PlanReq planReq) {
        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(planReq, "groupAdd");
        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        if (CollUtil.isNotEmpty(planReq.getTemplateTree())) {
            List<PlanGroupTemplateTreeDto> templateTree = planReq.getTemplateTree();

            long nodeCount = countAllNodes(templateTree);
            if (nodeCount > 20) {
                return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "分组模板不能超过20个节点");
            }
        }

        //其他验证放后面做
        if (!CollectionUtils.isEmpty(planReq.getGroupList())) {
            //内层验证
            for (PlanReq.GroupDto groupDto : planReq.getGroupList()) {
                message = validator.validate(groupDto, "groupAdd");
                if (!message.isEmpty()) {
                    String msg = message.get(0).getMessage();
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                }

                //验证内层学生
                int groupLeaderCount = 0;
                int smallGroupLeaderCount = 0;
                for (PlanReq.StudentDto studentDto : groupDto.getStudentList()) {
                    message = validator.validate(studentDto, "groupAdd");
                    if (!message.isEmpty()) {
                        String msg = message.get(0).getMessage();
                        return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                    }

                    //学生不能既是组长，又是小组长
                    if (studentDto.getIsGroupLeader() && studentDto.getIsSmallGroupLeader()) {
                        return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "学生数据异常,不能既是组长又是小组长.");
                    }

                    if (studentDto.getIsGroupLeader()) {
                        groupLeaderCount++;
                    }

                    if (studentDto.getIsSmallGroupLeader()) {
                        smallGroupLeaderCount++;
                    }
                }

                if (groupLeaderCount > 1) {
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "最多一个组长。");
                }

                if (smallGroupLeaderCount > 1) {
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "最多一个小组长。");
                }

            }
        }

        return ResultJson.success(new JSONObject());
    }

    private static long countAllNodes(List<PlanGroupTemplateTreeDto> nodesList) {
        if (nodesList == null || nodesList.isEmpty()) {
            return 0;
        }
        // 计算当前层级的节点数
        long currentLevelCount = nodesList.size();

        // 对每个子节点递归调用此方法，累加所有子节点的计数
        for (PlanGroupTemplateTreeDto node : nodesList) {
            if (node.getChildList() != null) {
                currentLevelCount += countAllNodes(node.getChildList());
            }
        }
        return currentLevelCount;
    }

    public ResultJson groupEdit(PlanReq planReq) {

        if (Long.valueOf(planReq.getSaasClassId()) < 0L) {
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "体验模式无法进行此操作");
        }

        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(planReq, "groupEdit");
        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        if (CollUtil.isNotEmpty(planReq.getTemplateTree())) {
            List<PlanGroupTemplateTreeDto> templateTree = planReq.getTemplateTree();

            long nodeCount = countAllNodes(templateTree);
            if (nodeCount > 20) {
                return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "分组模板不能超过20个节点");
            }
        }

        //其他验证放后面做
        if (!CollectionUtils.isEmpty(planReq.getGroupList())) {
            //内层验证

            String exceptionGroupStr = "的组长/小组长不存在，无法保存";
            List<String> exceptionGroupNames = new ArrayList<>();
            Boolean hasExceptionGroup = false;

            for (PlanReq.GroupDto groupDto : planReq.getGroupList()) {
                message = validator.validate(groupDto, "groupEdit");
                if (!message.isEmpty()) {
                    String msg = message.get(0).getMessage();
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                }
                //验证内层学生
                int groupLeaderCount = 0;
                int smallGroupLeaderCount = 0;
                for (PlanReq.StudentDto studentDto : groupDto.getStudentList()) {
                    message = validator.validate(studentDto, "groupEdit");
                    if (!message.isEmpty()) {
                        String msg = message.get(0).getMessage();
                        return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                    }

                    //学生不能既是组长，又是小组长
                    if (studentDto.getIsGroupLeader() && studentDto.getIsSmallGroupLeader()) {
                        return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "学生数据异常,不能既是组长又是小组长.");
                    }

                    if (studentDto.getIsGroupLeader()) {
                        groupLeaderCount++;
                    }

                    if (studentDto.getIsSmallGroupLeader()) {
                        smallGroupLeaderCount++;
                    }

                }

                for (PlanReq.StudentDto studentDto : groupDto.getStudentList()) {
                    //大组长缺失
                    if (!CollUtil.isEmpty(studentDto.getParentIds()) && studentDto.getParentIds().contains(null)) {

                        if (studentDto.getIsSmallGroupLeader()) {
                            hasExceptionGroup = true;
                            exceptionGroupNames.add(groupDto.getGroupName());
                            break;
                        } else {
                            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "studentList.parentsIds不能为空。");
                        }
                    }
                }

                if (groupLeaderCount > 1) {
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "最多一个组长。");
                }

                if (smallGroupLeaderCount > 1) {
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), "最多一个小组长。");
                }

            }

            if (hasExceptionGroup && !CollUtil.isEmpty(exceptionGroupNames)) {
                String errStr = String.join("、", exceptionGroupNames);
                return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), errStr + exceptionGroupStr);
            }

        }

        return ResultJson.success(new JSONObject());
    }

}

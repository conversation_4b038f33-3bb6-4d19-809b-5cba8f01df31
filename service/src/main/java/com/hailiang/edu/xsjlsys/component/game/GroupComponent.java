package com.hailiang.edu.xsjlsys.component.game;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlsys.dto.game.GroupDto;
import com.hailiang.edu.xsjlsys.dto.game.StudentDto;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 组相关操作组件类
 */
@Component
public class GroupComponent {


    /**
     * 设置小组成员分值
     *
     * @param groupDto
     * @param studentDtoList
     */
    public void setGroupStudentScore(GroupDto groupDto, List<StudentDto> studentDtoList) {
        for (StudentDto studentDto1 : groupDto.getStudentList()) {
            Optional<StudentDto> studentDtoOptional = studentDtoList.stream()
                    .filter(studentDto -> studentDto.getStudentId().equals(studentDto1.getStudentId())).findFirst();
            if (studentDtoOptional.isPresent()) {
                StudentDto studentDto11 = studentDtoOptional.get();
                studentDto1.setScore(studentDto11.getScore());
            }
        }
    }


    /**
     * 设置小组预赛平均分
     *
     * @param groupDto
     */
    public void setGroupPrepareAvgScore(GroupDto groupDto) {

        //组员成绩之和/组内考试人数（组员取快照，没有上传成绩视为未参试）；小组均未参加考试时，平均分视为0；
        double allGroupScore = 0.00;
        int num = 0;
        for (StudentDto studentDto1 : groupDto.getStudentList()) {
            if (studentDto1.getScore() != null) {
                allGroupScore = NumberUtil.add(allGroupScore, studentDto1.getScore().doubleValue());
                num++;
            }
        }
        groupDto.setPrepareAvgScore(BigDecimal.valueOf(0));
        if (num > 0) {
            groupDto.setPrepareAvgScore(NumberUtil.round(NumberUtil.div(allGroupScore, num), 1));
        }
    }


    /**
     * 获取小组 平均排名
     *
     * @param groupDto
     */
    public double getAvgSortVal(GroupDto groupDto, List<StudentDto> studentDtoList) {

        double allSortVal = 0.00;
        int num = 0;

        for (StudentDto studentDto1 : groupDto.getStudentList()) {
            Optional<StudentDto> studentDtoOptional = studentDtoList.stream()
                    .filter(studentDto -> studentDto.getStudentId().equals(studentDto1.getStudentId())).findFirst();
            if (studentDtoOptional.isPresent()) {
                StudentDto studentDto11 = studentDtoOptional.get();
                if (studentDto11.getSortVal() != null) {
                    allSortVal = NumberUtil.add(allSortVal, studentDto11.getSortVal().doubleValue());
                    num++;
                }
            }
        }
        if (num > 0) {
            return NumberUtil.round(NumberUtil.div(allSortVal, num), 1).doubleValue();
        }

        return 0.00;

    }


    /**
     * 设置积分争霸小组平均分
     *
     * @param groupDto
     */
    public void setGroupPointFightAvgScore(GroupDto groupDto) {
        //组员成绩之和/组内考试人数（组员取快照，没有上传成绩视为未参试）；小组均未参加考试时，平均分视为0；
        double allGroupScore = 0.00;
        int num = 0;
        for (StudentDto studentDto1 : groupDto.getStudentList()) {
            if (studentDto1.getScore() != null) {
                allGroupScore = NumberUtil.add(allGroupScore, studentDto1.getScore().doubleValue());
                num++;
            }
        }
        groupDto.setFinalAvgScore(NumberUtil.round(0, 1));
        if (num > 0) {
            groupDto.setFinalAvgScore(NumberUtil.round(NumberUtil.div(allGroupScore, num), 1));
        }
    }


    /**
     * 设置积分争霸小组排名
     *
     * @param groupDtoList
     * @param allStudentList
     * @return
     */
    public List<GroupDto> setGroupPointFightSortVal(List<GroupDto> groupDtoList, List<StudentDto> allStudentList) {
        //小组排名：根据测验的小组平均分排名，平均分相同时，比较平均排名，排名值低的获胜；平均排名也相同则并列名次，示例：1、1、3、4…
        //设置小组的平均排名
        for (GroupDto groupDto : groupDtoList) {
            groupDto.setAvgStuRank(NumberUtil.round(getAvgSortVal(groupDto, allStudentList), 1));
        }
        //按照分值从高到底聚合
        List<Map.Entry<BigDecimal, List<GroupDto>>> list = groupDtoList.stream().collect(Collectors.groupingBy(GroupDto::getFinalAvgScore)).entrySet()
                .stream().sorted((s1, s2) -> -Double.compare(s1.getKey().doubleValue(), s2.getKey().doubleValue()))
                .collect(Collectors.toList());
        int index = 1;
        for (Map.Entry<BigDecimal, List<GroupDto>> finalAvgScoreGroup : list) {
            //以代表平均分一致
            //聚合平均排名
            List<Map.Entry<BigDecimal, List<GroupDto>>> stuAvgRankGroup  = finalAvgScoreGroup.getValue().stream().collect(Collectors.groupingBy(GroupDto::getAvgStuRank)).entrySet()
                    .stream().sorted((s1, s2) -> -Double.compare(s2.getKey().doubleValue(), s1.getKey().doubleValue()))
                    .collect(Collectors.toList());
            for (Map.Entry<BigDecimal, List<GroupDto>> bigDecimalListEntry : stuAvgRankGroup) {
                for (GroupDto groupDto : bigDecimalListEntry.getValue()) {
                    //代表 平均排名也一致，此时该组下所有的排序字段相同
                    groupDto.setSortVal(index);
                }
                index = index + bigDecimalListEntry.getValue().size();
            }

            //聚合平均排名
/*            List<Map.Entry<BigDecimal, List<GroupDto>>> list2 = finalAvgScoreGroup.getValue().stream().collect(Collectors.groupingBy(GroupDto::getAvgStuRank)).entrySet()
                    .stream().sorted((s1, s2) -> -Double.compare(s1.getKey().doubleValue(), s2.getKey().doubleValue()))
                    .collect(Collectors.toList());
            for (Map.Entry<BigDecimal, List<GroupDto>> bigDecimalListEntry1 : list2) {
                int size = finalAvgScoreGroup.getValue().size();
                for (GroupDto groupDto : bigDecimalListEntry1.getValue()) {
                    groupDto.setSortVal(index);
                }
                index = index + size;
            }*/
        }
        Comparator<GroupDto> sortVal = Comparator.comparing(GroupDto::getSortVal);
        return ListUtil.sort(groupDtoList, sortVal);

    }
}

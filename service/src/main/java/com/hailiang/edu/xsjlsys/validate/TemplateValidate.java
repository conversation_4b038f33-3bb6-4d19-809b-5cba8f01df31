package com.hailiang.edu.xsjlsys.validate;


import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.dto.ResultJson;
import com.hailiang.edu.xsjlsys.emuns.ApiCodeEnum;
import com.hailiang.edu.xsjlsys.reqo.PlanCommentReq;
import com.hailiang.edu.xsjlsys.reqo.TemplateReq;
import net.sf.oval.ConstraintViolation;
import net.sf.oval.Validator;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@SuppressWarnings("all")
public class TemplateValidate extends BaseValidate {


    public ResultJson add(TemplateReq templateReq, String scene) {


        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(templateReq, scene);
        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        ResultJson NORMAL_ERROR1 = validateCommentTag(templateReq, validator, scene);
        if (NORMAL_ERROR1 != null) return NORMAL_ERROR1;

        ResultJson NORMAL_ERROR = volidateApplyScope(templateReq, validator, scene);
        if (NORMAL_ERROR != null) return NORMAL_ERROR;

        return ResultJson.success(new JSONObject());
    }

    public ResultJson edit(TemplateReq templateReq,String scene) {

        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(templateReq, scene);
        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        ResultJson NORMAL_ERROR = volidateApplyScope(templateReq, validator, scene);
        if (NORMAL_ERROR != null) return NORMAL_ERROR;


        return ResultJson.success(new JSONObject());
    }

    public ResultJson commentTagEdit(TemplateReq templateReq,String scene) {

        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(templateReq, scene);
        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        ResultJson NORMAL_ERROR1 = validateCommentTag(templateReq, validator, scene);
        if (NORMAL_ERROR1 != null) return NORMAL_ERROR1;


        return ResultJson.success(new JSONObject());
    }

    @Nullable
    private ResultJson validateCommentTag(TemplateReq templateReq, Validator validator, String scene) {
        List<ConstraintViolation> message;
        if (!CollectionUtils.isEmpty(templateReq.getCommentTagList())) {

            for (TemplateReq.CommentTagDto commentTagDto : templateReq.getCommentTagList()) {
                message = validator.validate(commentTagDto, scene);
                if (!message.isEmpty()) {
                    String msg = message.get(0).getMessage();
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                }

                if (!CollectionUtils.isEmpty(commentTagDto.getCommentList())) {
                    for (TemplateReq.CommentDto commentDto : commentTagDto.getCommentList()) {
                        message = validator.validate(commentDto, scene);
                        if (!message.isEmpty()) {
                            String msg = message.get(0).getMessage();
                            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                        }

                    }
                }

            }
        }
        return null;
    }


    @Nullable
    private ResultJson volidateApplyScope(TemplateReq templateReq, Validator validator, String scene) {
        List<ConstraintViolation> message;
        if (!CollectionUtils.isEmpty(templateReq.getApplyScope())) {

            for (TemplateReq.ApplyScope applyScope : templateReq.getApplyScope()) {
                message = validator.validate(applyScope, scene);
                if (!message.isEmpty()) {
                    String msg = message.get(0).getMessage();
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                }

                if (!CollectionUtils.isEmpty(applyScope.getApplyGradeList())) {
                    for (TemplateReq.Grade grade : applyScope.getApplyGradeList()) {
                        message = validator.validate(grade, scene);
                        if (!message.isEmpty()) {
                            String msg = message.get(0).getMessage();
                            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                        }

                    }

                }
            }
        }
        return null;
    }

}

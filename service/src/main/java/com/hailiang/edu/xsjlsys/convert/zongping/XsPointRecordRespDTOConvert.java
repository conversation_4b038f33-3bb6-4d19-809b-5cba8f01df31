package com.hailiang.edu.xsjlsys.convert.zongping;

import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.zongping.resp.XsPointRecordRespDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Component
public class XsPointRecordRespDTOConvert {

    public List<XsPointRecord> toXsPointRecordList(List<XsPointRecordRespDTO> xsPointRecordRespDTOList) {
        if (CollectionUtils.isEmpty(xsPointRecordRespDTOList)) {
            return new ArrayList<>();
        }

        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        for (XsPointRecordRespDTO dto : xsPointRecordRespDTOList) {
            XsPointRecord xsPointRecord = new XsPointRecord();
            xsPointRecord.setId(dto.getId());
            xsPointRecord.setStudentId(dto.getStudentId());
            xsPointRecord.setScore(dto.getScore());
            xsPointRecord.setPlanTagName(dto.getPlanTagName());
            // 设置其他必要字段的默认值
//            xsPointRecord.setChannel(XsPointRecord.CHANNEL_COMMENT);
//            xsPointRecord.setScene(XsPointRecord.SCENE_PERSONAL);
            xsPointRecordList.add(xsPointRecord);
        }
        return xsPointRecordList;
    }
}
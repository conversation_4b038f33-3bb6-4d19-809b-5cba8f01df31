package com.hailiang.edu.xsjlsys.convert.plan;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.BehaviorInfoRespDto;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class BehaviorInfoRespDtoConvert {

    /**
     * 进行保留小数处理
     *
     * @param behaviorDto
     */
    public void setRound(BehaviorInfoRespDto.BehaviorInfoDto.BehaviorDto behaviorDto) {


        behaviorDto.setAllScore(NumberUtil.round(behaviorDto.getAllScore(), 2).doubleValue());
        behaviorDto.setCompareScore(NumberUtil.round(behaviorDto.getCompareScore(), 2).doubleValue());
        behaviorDto.setBeforePeriodScore(NumberUtil.round(behaviorDto.getBeforePeriodScore(), 2).doubleValue());


        for (BehaviorInfoRespDto.TagDto tagDto : behaviorDto.getTagList()) {
            tagDto.setScore(NumberUtil.round(tagDto.getScore(), 2).doubleValue());
        }
    }


    public BehaviorInfoRespDto getToObj(List<PlanGroupDetailDto.StudentDto> studentDtoList
            , List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList) {

        BehaviorInfoRespDto behaviorInfoRespDto = new BehaviorInfoRespDto();
        behaviorInfoRespDto.setIsExistRecord(false);
        behaviorInfoRespDto.setIsExistPointRecord(false);

        //只取点评数据
        List<XsPointRecord> xsPointRecords = xsPointRecordList.stream().filter(t -> ObjectUtil.equals(t.getChannel(), XsPointRecord.CHANNEL_COMMENT)).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(xsPointRecordList)) {
            behaviorInfoRespDto.setIsExistRecord(true);
        }
        if (!CollectionUtils.isEmpty(xsPointRecords)) {
            behaviorInfoRespDto.setIsExistPointRecord(true);
        }

        behaviorInfoRespDto.setBehaviorInfo(getToObj(xsPointRecordList, beforeXsPointRecordList));

        behaviorInfoRespDto.setUnCommentStudentList(getUnCommentStudentList(studentDtoList, xsPointRecords));

        return behaviorInfoRespDto;
    }
    public BehaviorInfoRespDto getToObjV2(List<PlanGroupDetailDto.StudentDto> studentDtoList
            , List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList) {

        BehaviorInfoRespDto behaviorInfoRespDto = new BehaviorInfoRespDto();
        behaviorInfoRespDto.setIsExistRecord(false);
        behaviorInfoRespDto.setIsExistPointRecord(false);

        //只取点评数据
        if (!CollectionUtils.isEmpty(xsPointRecordList)) {
            behaviorInfoRespDto.setIsExistRecord(true);
        }
        if (!CollectionUtils.isEmpty(xsPointRecordList)) {
            behaviorInfoRespDto.setIsExistPointRecord(true);
        }

        behaviorInfoRespDto.setBehaviorInfo(getToObjV2(xsPointRecordList, beforeXsPointRecordList));

        behaviorInfoRespDto.setUnCommentStudentList(getUnCommentStudentList(studentDtoList, xsPointRecordList));

        return behaviorInfoRespDto;
    }
    /**
     * 获取表现对象信息
     *
     * @param xsPointRecordList
     * @return
     */
    public BehaviorInfoRespDto.BehaviorInfoDto getToObj(List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList) {

        BehaviorInfoRespDto.BehaviorInfoDto behaviorInfoDto = new BehaviorInfoRespDto.BehaviorInfoDto();

        List<XsPointRecord> behaviorPointRecordList = xsPointRecordList.stream()
                .filter(t -> ObjectUtil.equals(t.getChannel(), XsPointRecord.CHANNEL_COMMENT) || ObjectUtil.equals(t.getChannel(), XsPointRecord.CHANNEL_GAME))
                .collect(Collectors.toList());

        List<XsPointRecord> beforeBehaviorPointRecordList = beforeXsPointRecordList.stream()
                .filter(t -> ObjectUtil.equals(t.getChannel(), XsPointRecord.CHANNEL_COMMENT) || ObjectUtil.equals(t.getChannel(), XsPointRecord.CHANNEL_GAME))
                .collect(Collectors.toList());


        behaviorInfoDto.setAllCount((long) behaviorPointRecordList.size());

        //点评记录总分
        double sum = behaviorPointRecordList.stream().mapToDouble(XsPointRecord::getScore).sum();
        //点评记录绝对值之和
        double absAllScoreSum = 0.0;
        for (XsPointRecord xsPointRecord : behaviorPointRecordList) {
            absAllScoreSum += Math.abs(xsPointRecord.getScore());
        }

        String compareDesc;
        int compare = NumberUtil.compare(sum, 0);
        if (compare >= 0) {
            compareDesc = "+";
        } else {
            compareDesc = "-";
        }
        behaviorInfoDto.setAllScore(NumberUtil.round(sum, 2).doubleValue());

        behaviorInfoDto.setCompareDesc(compareDesc);

        behaviorInfoDto.setBehaviorPlusInfo(getBehaviorPlusInfo(behaviorPointRecordList, beforeBehaviorPointRecordList, absAllScoreSum));

        behaviorInfoDto.setBehaviorMinusInfo(getBehaviorMinusInfo(behaviorPointRecordList, beforeBehaviorPointRecordList, absAllScoreSum));

        return behaviorInfoDto;
    }
    public BehaviorInfoRespDto.BehaviorInfoDto getToObjV2(List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList) {

        BehaviorInfoRespDto.BehaviorInfoDto behaviorInfoDto = new BehaviorInfoRespDto.BehaviorInfoDto();

        behaviorInfoDto.setAllCount((long) xsPointRecordList.size());

        //点评记录总分
        double sum = xsPointRecordList.stream().mapToDouble(XsPointRecord::getScore).sum();
        //点评记录绝对值之和
        double absAllScoreSum = 0.0;
        for (XsPointRecord xsPointRecord : xsPointRecordList) {
            absAllScoreSum += Math.abs(xsPointRecord.getScore());
        }

        String compareDesc;
        int compare = NumberUtil.compare(sum, 0);
        if (compare >= 0) {
            compareDesc = "+";
        } else {
            compareDesc = "-";
        }
        behaviorInfoDto.setAllScore(NumberUtil.round(sum, 2).doubleValue());

        behaviorInfoDto.setCompareDesc(compareDesc);

        behaviorInfoDto.setBehaviorPlusInfo(getBehaviorPlusInfo(xsPointRecordList, beforeXsPointRecordList, absAllScoreSum));

        behaviorInfoDto.setBehaviorMinusInfo(getBehaviorMinusInfo(xsPointRecordList, beforeXsPointRecordList, absAllScoreSum));

        return behaviorInfoDto;
    }

    public BehaviorInfoRespDto.BehaviorInfoDto.BehaviorDto getBehaviorPlusInfo(List<XsPointRecord> behaviorPointRecordList, List<XsPointRecord> beforeBehaviorPointRecordList, double absAllScoreSum) {

        BehaviorInfoRespDto.BehaviorInfoDto.BehaviorDto behaviorDto = new BehaviorInfoRespDto.BehaviorInfoDto.BehaviorDto();

        long allCount = 0L;
        double allScore = 0.00;
        for (XsPointRecord xsPointRecord : behaviorPointRecordList) {
            if (NumberUtil.compare(xsPointRecord.getScore(), 0) >= 0) {
                allCount++;
                allScore = NumberUtil.add(allScore, xsPointRecord.getScore().doubleValue());
            }
        }

        setColumns(absAllScoreSum, behaviorDto, allCount, allScore);

        List<XsPointRecord> tagPointRecordList = behaviorPointRecordList.stream()
                .filter(t -> NumberUtil.compare(t.getScore(), 0) >= 0).collect(Collectors.toList());

        behaviorDto.setTagList(getTagDtoList(tagPointRecordList, true));

        boolean isExistBefore = false;
        double beforeAllScore = 0.00;
        for (XsPointRecord xsPointRecord : beforeBehaviorPointRecordList) {
            isExistBefore = true;
            if (NumberUtil.compare(xsPointRecord.getScore(), 0) >= 0) {
                beforeAllScore = NumberUtil.add(beforeAllScore, xsPointRecord.getScore().doubleValue());
            }
        }
        behaviorDto.setBeforePeriodScore(beforeAllScore);
        behaviorDto.setBeforePeriodIsExistPointRecord(isExistBefore);
        if (isExistBefore) {
            //上期存在积分记录
            double compareScore = NumberUtil.sub(behaviorDto.getAllScore(), behaviorDto.getBeforePeriodScore());
            behaviorDto.setCompareScore(compareScore);
        } else {
            behaviorDto.setCompareScore(0.0);
        }

        return behaviorDto;
    }


    public BehaviorInfoRespDto.BehaviorInfoDto.BehaviorDto getBehaviorMinusInfo(List<XsPointRecord> behaviorPointRecordList, List<XsPointRecord> beforeBehaviorPointRecordList, double absAllScoreSum) {

        BehaviorInfoRespDto.BehaviorInfoDto.BehaviorDto behaviorDto = new BehaviorInfoRespDto.BehaviorInfoDto.BehaviorDto();

        long allCount = 0L;
        double allScore = 0.00;
        for (XsPointRecord xsPointRecord : behaviorPointRecordList) {
            if (NumberUtil.compare(xsPointRecord.getScore(), 0) < 0) {
                allCount++;
                allScore = NumberUtil.add(allScore, xsPointRecord.getScore().doubleValue());
            }
        }

        setColumns(absAllScoreSum, behaviorDto, allCount, allScore);

        List<XsPointRecord> tagPointRecordList = behaviorPointRecordList.stream()
                .filter(t -> NumberUtil.compare(t.getScore(), 0) < 0).collect(Collectors.toList());


        behaviorDto.setTagList(getTagDtoList(tagPointRecordList, false));

        boolean isExistBefore = false;
        double beforeAllScore = 0.00;
        for (XsPointRecord xsPointRecord : beforeBehaviorPointRecordList) {
            isExistBefore = true;
            if (NumberUtil.compare(xsPointRecord.getScore(), 0) < 0) {
                beforeAllScore = NumberUtil.add(beforeAllScore, xsPointRecord.getScore().doubleValue());
            }
        }
        behaviorDto.setBeforePeriodScore(beforeAllScore);
        behaviorDto.setBeforePeriodIsExistPointRecord(isExistBefore);
        if (isExistBefore) {
            //上期存在积分记录
            double compareScore = NumberUtil.sub(behaviorDto.getAllScore(), behaviorDto.getBeforePeriodScore());
            behaviorDto.setCompareScore(compareScore);
        } else {
            behaviorDto.setCompareScore(0.0);
        }

        return behaviorDto;
    }

    private void setColumns(double absAllScoreSum, BehaviorInfoRespDto.BehaviorInfoDto.BehaviorDto behaviorDto, long allCount, double allScore) {
        behaviorDto.setAllCount(allCount);
        behaviorDto.setAllScore(allScore);

        BigDecimal rate1 = BigDecimal.valueOf(0);
        if (absAllScoreSum > 0) {
            rate1 = NumberUtil.round(NumberUtil.div(Math.abs(allScore), absAllScoreSum), 4);
        }

        behaviorDto.setPercentage(rate1);
    }


    public List<BehaviorInfoRespDto.TagDto> getTagDtoList(List<XsPointRecord> behaviorPointRecordList, Boolean isDesc) {

        List<BehaviorInfoRespDto.TagDto> tagDtoList = new ArrayList<>();

        Map<String, List<XsPointRecord>> listMapPointRecord = behaviorPointRecordList.stream()
                .collect(Collectors.groupingBy(XsPointRecord::getPlanTagName));

        //分类绝对值总分
        double absSumScore = 0L;
        for (XsPointRecord xsPointRecord : behaviorPointRecordList) {
            absSumScore += Math.abs(xsPointRecord.getScore());
        }

        if (!CollectionUtils.isEmpty(listMapPointRecord)) {
            for (Map.Entry<String, List<XsPointRecord>> stringListEntry : listMapPointRecord.entrySet()) {
                BehaviorInfoRespDto.TagDto tagDto = new BehaviorInfoRespDto.TagDto();
                tagDto.setTagName(stringListEntry.getKey());
                long count = 0L;
                double allScore = 0.00;
                for (XsPointRecord xsPointRecord : stringListEntry.getValue()) {
                    allScore = NumberUtil.add(allScore, xsPointRecord.getScore().doubleValue());
                    count++;
                }
                tagDto.setCount(count);
                tagDto.setScore(allScore);

                tagDtoList.add(tagDto);
            }
        }

        //设置比例
        int size = tagDtoList.size();
        //已计算的百分比
        double haveRate = 0.00;
        for (int i = 0; i < size; i++) {
            BehaviorInfoRespDto.TagDto tagDto = tagDtoList.get(i);
            if (i == size - 1) {
                //最后一组
                double last = NumberUtil.sub(1, haveRate);
                if (NumberUtil.compare(last, 0) < 0) {
                    last = 0.00;
                }

                tagDto.setPercentage(NumberUtil.round(last, 4));
            } else {
                BigDecimal rate = NumberUtil.round(NumberUtil.div(Math.abs(tagDto.getScore()), absSumScore), 4);

                haveRate = NumberUtil.add(haveRate, rate.doubleValue());

                tagDto.setPercentage(NumberUtil.round(rate, 4));
            }
        }


        Comparator<BehaviorInfoRespDto.TagDto> scoreVal = Comparator.comparing(BehaviorInfoRespDto.TagDto::getScore);

        if (isDesc) {
            return ListUtil.sort(tagDtoList, scoreVal.reversed());
        } else {
            return ListUtil.sort(tagDtoList, scoreVal);
        }


    }

    /**
     * 获取未点评学生列表数据
     *
     * @param studentDtoList
     * @return
     */
    public List<BehaviorInfoRespDto.UnCommentStudentDto> getUnCommentStudentList(List<PlanGroupDetailDto.StudentDto> studentDtoList, List<XsPointRecord> xsPointRecords) {

        List<BehaviorInfoRespDto.UnCommentStudentDto> unCommentStudentDtoList = new ArrayList<>();

        //有点评记录的学生ids
        Set<Long> pointStudentIds = xsPointRecords.stream().map(XsPointRecord::getStudentId).collect(Collectors.toSet());

        //没有点评记录 则所有学生都未点评
        for (PlanGroupDetailDto.StudentDto studentDto : studentDtoList) {

            //代表未点评
            if (!pointStudentIds.contains(Long.valueOf(studentDto.getStudentId()))) {
                BehaviorInfoRespDto.UnCommentStudentDto unCommentStudentDto = new BehaviorInfoRespDto.UnCommentStudentDto();
                unCommentStudentDto.setStudentId(studentDto.getStudentId());
                unCommentStudentDto.setStudentNo(studentDto.getStudentNo());
                unCommentStudentDto.setStudentName(studentDto.getStudentName());
                unCommentStudentDtoList.add(unCommentStudentDto);
            }
        }

        return unCommentStudentDtoList;
    }

}

package com.hailiang.edu.xsjlsys.component.excel;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.zongping.OverAllRecordRespDto;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class PointSheetComponent {

    @Resource
    ValueComponent valueComponent;


    public List<String> getPointHeader() {
        String[] header = {"学生姓名", "积分", "类型", "分类", "积分描述", "点评时间", "教师姓名"};
        return Arrays.stream(header).collect(Collectors.toList());
    }


    public void doStudentSheet2(Sheet sheet2, Map<String, CellStyle> mapStyle
            , PlanGroupDetailDto planGroupDetailDto, List<XsPointRecord> xsPointRecordList) {
        List<String> headerList = getPointHeader();
        //excel第一行设置
        Row firstRow = sheet2.createRow(0);
        int size = headerList.size();
        for (int i = 0; i < size; i++) {
            valueComponent.setExcelValue(firstRow.createCell(i), headerList.get(i), mapStyle.get("header"));
            //亲测有效 但是比例最后再调

            if (i == 4) {
                sheet2.setColumnWidth(i, 42 * 256);
            } else {
                sheet2.setColumnWidth(i, 15 * 256);
            }
        }

        if (CollectionUtils.isEmpty(xsPointRecordList)) {
            return;
        }

        List<PlanGroupDetailDto.StudentDto> studentDtoList = new ArrayList<>();

        for (PlanGroupDetailDto.GroupDto groupDto : planGroupDetailDto.getGroupList()) {
            studentDtoList.addAll(groupDto.getStudentList());
        }
        for (PlanGroupDetailDto.UnGroupDto unGroupDto : planGroupDetailDto.getUnGroupList()) {
            studentDtoList.addAll(unGroupDto.getStudentList());
        }

        Map<String, String> studentNameMap = studentDtoList.stream().collect(Collectors.toMap(PlanGroupDetailDto.StudentDto::getStudentId
                , PlanGroupDetailDto.StudentDto::getStudentName, (p1, p2) -> p1));


        Map<Long, List<XsPointRecord>> longListMap = xsPointRecordList.stream()
                .filter(t -> !t.getChannel().equals(XsPointRecord.CHANNEL_PRIZE))
                .collect(Collectors.groupingBy(XsPointRecord::getStudentId));

        int indexRow = 1;
        Comparator<XsPointRecord> id = Comparator.comparing(XsPointRecord::getId);
        for (Map.Entry<Long, List<XsPointRecord>> longListEntry : longListMap.entrySet()) {

            List<XsPointRecord> currentList = longListEntry.getValue();
            ListUtil.sort(currentList, id.reversed());
            for (XsPointRecord xsPointRecord : currentList) {

                Row groupRow = sheet2.createRow(indexRow);
                String studentName = "";
                if (studentNameMap.containsKey(String.valueOf(xsPointRecord.getStudentId()))) {
                    studentName = studentNameMap.get(String.valueOf(xsPointRecord.getStudentId()));
                }

                valueComponent.setExcelValue(groupRow.createCell(0), studentName, mapStyle.get("cell"));
                valueComponent.setExcelValue(groupRow.createCell(1), BigDecimal.valueOf(xsPointRecord.getScore())
                        , mapStyle.get("cell"));

                int compare = NumberUtil.compare(xsPointRecord.getScore(), 0);
                if (compare >= 0) {
                    valueComponent.setExcelValue(groupRow.createCell(2), "表扬", mapStyle.get("cell"));
                } else {
                    valueComponent.setExcelValue(groupRow.createCell(2), "待改进", mapStyle.get("cell"));
                }

                valueComponent.setExcelValue(groupRow.createCell(3), xsPointRecord.getPlanTagName(), mapStyle.get("cell"));
                valueComponent.setExcelValue(groupRow.createCell(4), xsPointRecord.getContent(), mapStyle.get("cell"));

                Date createTime = DateUtil.parse(xsPointRecord.getCreateTime());
                valueComponent.setExcelValue(groupRow.createCell(5), DateUtil.format(createTime, "yyyy/MM/dd"), mapStyle.get("cell"));
                valueComponent.setExcelValue(groupRow.createCell(6), xsPointRecord.getAccountName(), mapStyle.get("cell"));

                indexRow++;
            }
        }
    }


    public void doOverallStudentSheet2(Sheet sheet2, Map<String, CellStyle> mapStyle
            , List<OverAllRecordRespDto.Record> records) {
        List<String> headerList = getPointHeader();
        //excel第一行设置
        Row firstRow = sheet2.createRow(0);
        int size = headerList.size();
        for (int i = 0; i < size; i++) {
            valueComponent.setExcelValue(firstRow.createCell(i), headerList.get(i), mapStyle.get("header"));
            //亲测有效 但是比例最后再调

            if (i == 4) {
                sheet2.setColumnWidth(i, 42 * 256);
            } else {
                sheet2.setColumnWidth(i, 15 * 256);
            }
        }

        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        int indexRow = 1;
        for (OverAllRecordRespDto.Record record : records) {

            Row groupRow = sheet2.createRow(indexRow);

            valueComponent.setExcelValue(groupRow.createCell(0), record.getName(), mapStyle.get("cell"));

            Double score = 0.0;
            if (record.getScore() != null) {
                score = record.getScore();
            }

            valueComponent.setExcelValue(groupRow.createCell(1), NumberUtil.round(score, 2)
                    , mapStyle.get("cell"));

            int compare = NumberUtil.compare(score, 0);
            if (compare >= 0) {
                valueComponent.setExcelValue(groupRow.createCell(2), "表扬", mapStyle.get("cell"));
            } else {
                valueComponent.setExcelValue(groupRow.createCell(2), "待改进", mapStyle.get("cell"));
            }

            valueComponent.setExcelValue(groupRow.createCell(3), record.getClassifyName(), mapStyle.get("cell"));
            valueComponent.setExcelValue(groupRow.createCell(4), record.getClassifyDetailName(), mapStyle.get("cell"));

            Date createTime = DateUtil.parse(record.getSubmitTime());
            valueComponent.setExcelValue(groupRow.createCell(5), DateUtil.format(createTime, "yyyy/MM/dd"), mapStyle.get("cell"));
            valueComponent.setExcelValue(groupRow.createCell(6), record.getAppraisalName(), mapStyle.get("cell"));

            indexRow++;
        }
    }
}

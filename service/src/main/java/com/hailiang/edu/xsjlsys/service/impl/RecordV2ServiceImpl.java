package com.hailiang.edu.xsjlsys.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.dto.Pagination;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.component.ExcelComponent;
import com.hailiang.edu.xsjlsys.component.PointRecordComponent;
import com.hailiang.edu.xsjlsys.component.ScoreComponent;
import com.hailiang.edu.xsjlsys.component.ScoreV2Component;
import com.hailiang.edu.xsjlsys.component.TimeComponent;
import com.hailiang.edu.xsjlsys.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlsys.component.mq.point.PointProducer;
import com.hailiang.edu.xsjlsys.component.rsa.RsaHaiComponent;
import com.hailiang.edu.xsjlsys.consts.CommonConst;
import com.hailiang.edu.xsjlsys.consts.DataRangeConst;
import com.hailiang.edu.xsjlsys.consts.DeletedConst;
import com.hailiang.edu.xsjlsys.consts.PlanTypeConst;
import com.hailiang.edu.xsjlsys.consts.ScoreTypeConst;
import com.hailiang.edu.xsjlsys.consts.TimeTypeConst;
import com.hailiang.edu.xsjlsys.convert.callname.RangeLevelRespDtoConvert;
import com.hailiang.edu.xsjlsys.convert.plan.BehaviorInfoRespDtoConvert;
import com.hailiang.edu.xsjlsys.convert.plan.PlanGroupDetailDtoConvert;
import com.hailiang.edu.xsjlsys.convert.plan.StuPointRecordDtoConvert;
import com.hailiang.edu.xsjlsys.convert.plan.TagDtoConvert;
import com.hailiang.edu.xsjlsys.convert.plan.tag.PlanTagConvert;
import com.hailiang.edu.xsjlsys.convert.point.PointRecordConvert;
import com.hailiang.edu.xsjlsys.convert.rank.RankGroupScoreDtoConvert;
import com.hailiang.edu.xsjlsys.convert.rank.RankSingleScoreDtoConvert;
import com.hailiang.edu.xsjlsys.convert.rank.RankStudentScoreDtoConvert;
import com.hailiang.edu.xsjlsys.convert.report.DevelopmentDtoConvert;
import com.hailiang.edu.xsjlsys.convert.report.PointPerformanceDtoConvert;
import com.hailiang.edu.xsjlsys.convert.report.SmsReportExtDtoConvert;
import com.hailiang.edu.xsjlsys.convert.sms.SmsReportDetailDtoConvert;
import com.hailiang.edu.xsjlsys.convert.sms.SmsReportDtoConvert;
import com.hailiang.edu.xsjlsys.convert.zongping.XsPointRecordRespDTOConvert;
import com.hailiang.edu.xsjlsys.dal.biz.SmsLinkSignStorage;
import com.hailiang.edu.xsjlsys.dal.biz.StatStorage;
import com.hailiang.edu.xsjlsys.dal.biz.mongodb.SmsReportOtherStorage;
import com.hailiang.edu.xsjlsys.dal.biz.mongodb.SmsReportPointStorage;
import com.hailiang.edu.xsjlsys.dal.biz.mongodb.SmsReportStudentEvaluateStorage;
import com.hailiang.edu.xsjlsys.dal.dao.XsAiPointRecordMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsAvatarMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsClassStudentMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsPlanTagMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsPlanUserMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsPointRecordMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsSmsReportMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsSmsReportStudentMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsStudentMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsStudentMasterSlaveMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsTrustLoginMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsUserMapper;
import com.hailiang.edu.xsjlsys.dal.entity.XsAvatar;
import com.hailiang.edu.xsjlsys.dal.entity.XsClassStudent;
import com.hailiang.edu.xsjlsys.dal.entity.XsPlan;
import com.hailiang.edu.xsjlsys.dal.entity.XsPlanTag;
import com.hailiang.edu.xsjlsys.dal.entity.XsPlanUser;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlsys.dal.entity.XsSmsReportStudent;
import com.hailiang.edu.xsjlsys.dal.entity.XsSmsRule;
import com.hailiang.edu.xsjlsys.dal.entity.XsStudent;
import com.hailiang.edu.xsjlsys.dal.entity.XsStudentMasterSlave;
import com.hailiang.edu.xsjlsys.dal.entity.XsTrustLogin;
import com.hailiang.edu.xsjlsys.dal.entity.XsUser;
import com.hailiang.edu.xsjlsys.dal.entity.mongodb.SmsReportOther;
import com.hailiang.edu.xsjlsys.dal.entity.mongodb.SmsReportPoint;
import com.hailiang.edu.xsjlsys.dal.entity.mongodb.SmsReportStudentEvaluate;
import com.hailiang.edu.xsjlsys.dto.UserDto;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.behavior.BehaviorReportDetailDto;
import com.hailiang.edu.xsjlsys.dto.callName.RangeLevelRespDto;
import com.hailiang.edu.xsjlsys.dto.excel.TagDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanTagCommentDto;
import com.hailiang.edu.xsjlsys.dto.point.PlanCommentTagDto;
import com.hailiang.edu.xsjlsys.dto.point.PlanCommentTagListDto;
import com.hailiang.edu.xsjlsys.dto.point.PointTaskDto;
import com.hailiang.edu.xsjlsys.dto.point.StuPointRecordDto;
import com.hailiang.edu.xsjlsys.dto.point.resp.RecordAddRespDto;
import com.hailiang.edu.xsjlsys.dto.point.resp.StudentReportRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.RankGroupScoreDto;
import com.hailiang.edu.xsjlsys.dto.rank.RankSingleScoreDto;
import com.hailiang.edu.xsjlsys.dto.rank.RankStudentScoreDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.BehaviorInfoRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.RankGroupScoreRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.RankStudentScoreRespDto;
import com.hailiang.edu.xsjlsys.dto.report.SelectOptionDto;
import com.hailiang.edu.xsjlsys.dto.saas.resp.FClassRespDto;
import com.hailiang.edu.xsjlsys.dto.sms.SmsLinkDto;
import com.hailiang.edu.xsjlsys.dto.sms.SmsReportDetailDto;
import com.hailiang.edu.xsjlsys.dto.sms.SmsReportDto;
import com.hailiang.edu.xsjlsys.dto.sms.StudentDto;
import com.hailiang.edu.xsjlsys.dto.zongping.resp.XsPointRecordRespDTO;
import com.hailiang.edu.xsjlsys.emuns.ApiCodeEnum;
import com.hailiang.edu.xsjlsys.emuns.AvatarBusinessTypeEnum;
import com.hailiang.edu.xsjlsys.emuns.RoleEnum;
import com.hailiang.edu.xsjlsys.emuns.SelectOptionEnum;
import com.hailiang.edu.xsjlsys.emuns.StatisticTypeEnum;
import com.hailiang.edu.xsjlsys.query.plan.PlanTagQuery;
import com.hailiang.edu.xsjlsys.query.plan.PlanUserQuery;
import com.hailiang.edu.xsjlsys.query.plan.XsAvatarQuery;
import com.hailiang.edu.xsjlsys.query.point.PointRecordQuery;
import com.hailiang.edu.xsjlsys.query.point.ScoreQuery;
import com.hailiang.edu.xsjlsys.query.point.ScoreV2Query;
import com.hailiang.edu.xsjlsys.query.sms.SmsReportStudentQuery;
import com.hailiang.edu.xsjlsys.query.user.StudentQuery;
import com.hailiang.edu.xsjlsys.query.user.TrustLoginQuery;
import com.hailiang.edu.xsjlsys.reqo.PlanReq;
import com.hailiang.edu.xsjlsys.reqo.RecordReq;
import com.hailiang.edu.xsjlsys.saas.SaasClient;
import com.hailiang.edu.xsjlsys.service.CallNameService;
import com.hailiang.edu.xsjlsys.service.PlanCommentService;
import com.hailiang.edu.xsjlsys.service.PlanService;
import com.hailiang.edu.xsjlsys.service.PointService;
import com.hailiang.edu.xsjlsys.service.RecordService;
import com.hailiang.edu.xsjlsys.service.RecordV2Service;
import com.hailiang.edu.xsjlsys.service.RoleService;
import com.hailiang.edu.xsjlsys.service.SaasService;
import com.hailiang.edu.xsjlsys.service.ZongPingService;
import com.hailiang.edu.xsjlsys.util.XsListUtil;
import com.hailiang.edu.xsjlsys.zongping.ZongPingClient;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
@RefreshScope
public class RecordV2ServiceImpl implements RecordV2Service {
    @Resource
    XsPlanTagMapper xsPlanTagMapper;
    @Resource
    XsClassStudentMapper xsClassStudentMapper;
    @Resource
    XsPointRecordMapper xsPointRecordMapper;
    @Resource
    XsStudentMasterSlaveMapper xsStudentMasterSlaveMapper;
    @Resource
    PlanGroupDetailDtoConvert planGroupDetailDtoConvert;
    @Resource
    PointRecordComponent pointRecordComponent;
    @Resource
    PlanCommentService planCommentService;
    @Resource
    ScoreV2Component scoreV2Component;
    @Resource
    TimeComponent timeComponent;
    @Resource
    RankGroupScoreDtoConvert rankGroupScoreDtoConvert;
    @Resource
    RankStudentScoreDtoConvert rankStudentScoreDtoConvert;
    @Resource
    DevelopmentDtoConvert developmentDtoConvert;
    @Resource
    PointPerformanceDtoConvert pointPerformanceDtoConvert;
    @Resource
    PlanService planService;
    @Resource
    StuPointRecordDtoConvert stuPointRecordDtoConvert;
    @Resource
    TagDtoConvert tagDtoConvert;
    @Resource
    ExcelComponent excelComponent;
    @Resource
    PointService pointService;
    @Resource
    BehaviorInfoRespDtoConvert behaviorInfoRespDtoConvert;
    @Resource
    RsaHaiComponent rsaHaiComponent;
    @Resource
    RoleService roleService;
    @Resource
    XsStudentMapper xsStudentMapper;
    @Resource
    XsSmsReportStudentMapper xsSmsReportStudentMapper;
    @Resource
    SmsReportOtherStorage smsReportOtherStorage;
    @Resource
    SmsReportPointStorage smsReportPointStorage;
    @Resource
    SmsReportDtoConvert smsReportDtoConvert;
    @Resource
    XsUserMapper xsUserMapper;
    @Resource
    SmsReportDetailDtoConvert smsReportDetailDtoConvert;
    @Resource
    PlanTagConvert planTagConvert;
    @Resource
    PointRecordConvert pointRecordConvert;
    @Resource
    XsPlanUserMapper xsPlanUserMapper;
    @Resource
    SmsLinkSignStorage smsLinkSignStorage;
    @Resource
    XsSmsReportMapper xsSmsReportMapper;
    @Resource
    SaasService saasService;
    @Resource
    SaasClient saasClient;
    @Resource
    SmsReportExtDtoConvert smsReportExtDtoConvert;
    @Resource
    RankSingleScoreDtoConvert rankSingleScoreDtoConvert;
    @Resource
    XsAvatarMapper xsAvatarMapper;
    @Resource
    StatStorage statStorage;
    @Resource
    SmsReportStudentEvaluateStorage smsReportStudentEvaluateStorage;
    @Resource
    ZongPingService zongPingService;
    @Resource
    CallNameService callNameService;
    @Resource
    RangeLevelRespDtoConvert rangeLevelRespDtoConvert;
    @Resource
    PointProducer pointProducer;
    @Resource
    IdManageComponent idManageComponent;
    @Resource
    ZongPingClient zongPingClient;
    @Resource
    XsPointRecordRespDTOConvert xsPointRecordRespDTOConvert;
    @Resource
    private XsTrustLoginMapper xsTrustLoginMapper;

    @Value("${saas.special.aeskey}")
    private String specialAesKey;
    /**
     * 海思谷特殊入口秘钥前缀
     */
    private static final String SAAS_STUDENT_SECRET_PREFIX = "cmcc-";


    protected void validateStudentIds(RecordReq recordReq, List<XsClassStudent> xsClassStudentList) throws BusinessException {
        if (CollectionUtils.isEmpty(xsClassStudentList)) {
            throw new BusinessException("该班级下无学生信息");
        }

        Set<Long> studentIds = xsClassStudentList.stream().map(XsClassStudent::getStudentId).collect(Collectors.toSet());
        for (String studentId : recordReq.getStudentIds()) {
            if (!studentIds.contains(Long.valueOf(studentId))) {

                log.info("存在无效学生id:" + studentId);

                throw new BusinessException("学生不存在，请刷新重试");
            }
        }
    }

    @Override
    public RecordAddRespDto add(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanTagCommentDto> schoolTagList,
                                List<PlanCommentTagDto> detailObjList, List<PlanTagCommentDto.PlanComment> schoolPlanCommentList) throws BusinessException {

        XsPlanUser xsPlanUser = planService.validateJoinPlan(recordReq.getPlanId(), xsUserInfo.getUserId(), recordReq.getSaasClassId());

        XsPlan xsPlan = xsPlanUser.getXsPlan();
        //提交点评项对象包装
        List<PlanCommentTagListDto> planCommentTagObj = planCommentService.getPlanCommentTagObj(recordReq, xsUserInfo, schoolTagList, detailObjList);

        //查询该班级下所有学生列表
        List<XsClassStudent> xsClassStudentList = xsClassStudentMapper.getListByClassId(recordReq.getSaasClassId(), xsUserInfo.getSchoolId());
        //验证学生ids集合的有效性
        validateStudentIds(recordReq, xsClassStudentList);

        //学生模型数据组装
        List<PlanGroupDetailDto.StudentDto> studentDtoList = new ArrayList<>();
        for (String studentId : recordReq.getStudentIds()) {
            PlanGroupDetailDto.StudentDto studentDto = new PlanGroupDetailDto.StudentDto();
            studentDto.setStudentId(studentId);
            studentDto.setStudentName("");
            Optional<XsClassStudent> xsClassStudentOptional = xsClassStudentList.stream()
                    .filter(xsClassStudent -> xsClassStudent.getStudentId().equals(Long.valueOf(studentId)))
                    .findFirst();
            if (xsClassStudentOptional.isPresent()) {
                XsClassStudent xsClassStudent = xsClassStudentOptional.get();
                studentDto.setStudentName(xsClassStudent.getXsStudent().getStudentName());
                studentDtoList.add(studentDto);
            }
        }

        //校级分类 id
        Set<String> schoolTagIds = new HashSet<>();
        if (!CollUtil.isEmpty(schoolTagList)) {
            schoolTagIds = schoolTagList.stream().map(PlanTagCommentDto::getPlanTagId).collect(Collectors.toSet());
        }

        //校级分类的 commentId to targetId Map
        Map<String, Long> commentIdToTargetIdMap = new HashMap<>();
        if (!CollUtil.isEmpty(schoolPlanCommentList)) {
            commentIdToTargetIdMap = schoolPlanCommentList.stream().collect(Collectors.toMap(PlanTagCommentDto.PlanComment::getPlanCommentId,
                    PlanTagCommentDto.PlanComment::getXwlTargetId, (v1, v2) -> v1));
        }

        //拼装得分记录
        List<XsPointRecord> xsPointRecordList = pointRecordComponent.getPointRecordList(recordReq, xsUserInfo,
                studentDtoList, xsPlan, planCommentTagObj, commentIdToTargetIdMap);
        List<XsPointRecord> xsPointRecordHelpList = new ArrayList<>();


        //方案下积分比例大于0时，才进行帮扶积分的运算
        if (NumberUtil.compare(xsPlan.getPointRetailRate(), 0) > 0) {
            // 需要计算父子关系的积分生成
            xsPointRecordHelpList = getRelPointRecordList(recordReq, xsUserInfo, studentDtoList, xsClassStudentList,
                    xsPlan, planCommentTagObj, commentIdToTargetIdMap);

            xsPointRecordList.addAll(xsPointRecordHelpList);
        }


        RecordAddRespDto recordAddRespDto = new RecordAddRespDto();

        //要返回插入的id
        List<String> recordIds = new ArrayList<>();
        List<RecordAddRespDto.HelpStudentDto> helpStudentDtoList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(xsPointRecordList)) {

            //获取帮扶积分的学生信息
            helpStudentDtoList = pointRecordComponent.getHelpStudentList(xsPointRecordHelpList, xsClassStudentList, 1);

            //插入积分
            pointService.doInsert(xsPointRecordList, xsPlanUser.getUserId(), xsUserInfo, schoolTagIds, commentIdToTargetIdMap, true);

            for (XsPointRecord xsPointRecord : xsPointRecordList) {
                recordIds.add(String.valueOf(xsPointRecord.getId()));
            }


        }

        recordAddRespDto.setRecordIds(recordIds);
        recordAddRespDto.setHelpStudentList(helpStudentDtoList);

        return recordAddRespDto;
    }

    /**
     * 获取帮扶积分待插入待积分列表
     *
     * @param recordReq
     * @param xsClassStudentList
     */
    private List<XsPointRecord> getRelPointRecordList(RecordReq recordReq, XsUserInfo xsUserInfo
            , List<PlanGroupDetailDto.StudentDto> studentDtoList, List<XsClassStudent> xsClassStudentList
            , XsPlan xsPlan, List<PlanCommentTagListDto> planCommentTagObj, Map<String, Long> commentIdToTargetIdMap) {

        //查询该方案下父子关系情况
        List<XsStudentMasterSlave> xsStudentMasterSlaveList = xsStudentMasterSlaveMapper.getListByPlanId(recordReq.getPlanId());

        //进行学生列表父子关系设置
        planGroupDetailDtoConvert.doRelStudentList(studentDtoList, xsStudentMasterSlaveList, xsClassStudentList);

        //进行帮扶积分列表组装
        return pointRecordComponent.getRelPointRecordList(recordReq, xsUserInfo, studentDtoList, xsPlan, planCommentTagObj
                , commentIdToTargetIdMap);
    }

    @Override
    public BehaviorInfoRespDto classInfo(RecordReq recordReq, XsUserInfo xsUserInfo, PlanGroupDetailDto planGroupDetailDto, List<PlanGroupDetailDto.StudentDto> studentDtoList
            , List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList) {

        //设置分值 详情和学生列表都会设置好
        scoreV2Component.setDetailScoreV2(planGroupDetailDto, xsPointRecordList, recordReq.getSaasCampusId());

        //开始进行数据模型转换
        BehaviorInfoRespDto behaviorInfoRespDto = behaviorInfoRespDtoConvert.getToObj(studentDtoList, xsPointRecordList, beforeXsPointRecordList);

        //进行保留小数处理
        behaviorInfoRespDtoConvert.setRound(behaviorInfoRespDto.getBehaviorInfo().getBehaviorPlusInfo());
        behaviorInfoRespDtoConvert.setRound(behaviorInfoRespDto.getBehaviorInfo().getBehaviorMinusInfo());


        return behaviorInfoRespDto;

    }
    @Override
    public void setCurrentAndBeforePointRecordListV2(RecordReq recordReq, XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList) {
        Integer statisticType = recordReq.getStatisticType();

        if (!StatisticTypeEnum.isValidType(statisticType)) {
            throw new BusinessException("请选择统计类型");
        }
        //进行查询积分数据条件
        ScoreV2Query currentScoreQuery = groupSearchConditionV2(recordReq);
        List<XsPointRecord> xsPointRecordListSub = new ArrayList<>(512);
        List<XsPointRecord> beforeXsPointRecordListSub = new ArrayList<>(512);

        // 按点评分统计
        if (statisticType == StatisticTypeEnum.SCORE.getType()) {
            xsPointRecordListSub = xsPointRecordMapper.getListByScoreQueryV2(currentScoreQuery);

            //过去时间段的数据查询条件
            ScoreV2Query beforeScoreQuery = JSONObject.parseObject(JSONObject.toJSONString(currentScoreQuery), ScoreV2Query.class);
            timeComponent.setMomDateRange(beforeScoreQuery);
            beforeXsPointRecordListSub = xsPointRecordMapper.getListByScoreQueryV2(beforeScoreQuery);
        }
        // 按金币统计
        if (statisticType == StatisticTypeEnum.COIN.getType()) {
            // userId转换成教职工id
            this.convertStaffIdByUserId(xsUserInfo.getTenantId(), currentScoreQuery);

            List<XsPointRecordRespDTO> xsPointRecordRespCoinDTOS = zongPingClient.listByScoreQuery(currentScoreQuery);
            this.fillPlanTagName(xsPointRecordRespCoinDTOS);
            xsPointRecordListSub = xsPointRecordRespDTOConvert.toXsPointRecordList(xsPointRecordRespCoinDTOS);

            //过去时间段的数据查询条件
            ScoreV2Query beforeScoreQuery = JSONObject.parseObject(JSONObject.toJSONString(currentScoreQuery), ScoreV2Query.class);
            timeComponent.setMomDateRange(beforeScoreQuery);
            List<XsPointRecordRespDTO> beforeXsPointRecordRespCoinDTOS = zongPingClient.listByScoreQuery(beforeScoreQuery);
            this.fillPlanTagName(xsPointRecordRespCoinDTOS);
            beforeXsPointRecordListSub = xsPointRecordRespDTOConvert.toXsPointRecordList(beforeXsPointRecordRespCoinDTOS);}

        xsPointRecordList.addAll(xsPointRecordListSub);
        beforeXsPointRecordList.addAll(beforeXsPointRecordListSub);

    }

    private void fillPlanTagName(List<XsPointRecordRespDTO> xsPointRecordRespCoinDTOS) {

    }

    private void convertStaffIdByUserId(String tenantId, ScoreV2Query currentScoreQuery) {
        Long userId = currentScoreQuery.getUserId();
        Set<Long> userIds = currentScoreQuery.getUserIds();
        if (CollUtil.isEmpty(userIds) && null == userId) {
            return;
        }
        // 把userId加到userIds里面
        if (null != userId) {
            userIds = CollectionUtils.isEmpty(userIds) ? new HashSet<>() : userIds;
            userIds.add(userId);
        }
        List<XsTrustLogin> xsTrustLoginList = xsTrustLoginMapper.getListByUserIds(userIds);
        Map<Long, Long> userIdWithOpenIdMap = xsTrustLoginList.stream()
                .collect(Collectors.toMap(XsTrustLogin::getUserId, input -> Long.valueOf(input.getOpenId()), (p1, p2) -> p2));

        Map<Long, Long> userIdWithStaffIdMap = saasClient.getUserIdMapStaffId(userIdWithOpenIdMap, tenantId);
        if (CollUtil.isEmpty(userIdWithStaffIdMap)){
            return;
        }
        if (!CollectionUtils.isEmpty(userIdWithStaffIdMap)) {
            int size = userIdWithStaffIdMap.size();
            if (size == 1) {
                currentScoreQuery.setStaffId(userIdWithStaffIdMap.values().stream().findFirst().get());
                log.info("排行榜，重新设置staffId:{}", currentScoreQuery.getStaffId());
            } else {
                Set<Long> staffIds = new HashSet<>();
                for (Map.Entry<Long, Long> entry : userIdWithStaffIdMap.entrySet()) {
                    staffIds.add(entry.getValue());
                }
                currentScoreQuery.setStaffIds(staffIds);
                log.info("排行榜，重新设置staffIds:{}", staffIds);
            }
        }
    }

    @Override
    public RankGroupScoreRespDto group(RecordReq recordReq, XsUserInfo xsUserInfo, PlanGroupDetailDto planGroupDetailDto
            , List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList, Boolean includeMinusData) {

        RankGroupScoreRespDto rankGroupScoreRespDto = new RankGroupScoreRespDto();
        //如果该方案下没有组信息 则抛异常
        if (CollectionUtils.isEmpty(planGroupDetailDto.getGroupList())) {

            rankGroupScoreRespDto.setGroupList(new ArrayList<>());
            rankGroupScoreRespDto.setIsExistRecord(false);

            return rankGroupScoreRespDto;
        }

        boolean isExistRecord = false;
        boolean isDefault = false;

        if (!CollectionUtils.isEmpty(xsPointRecordList)) {
            isExistRecord = true;
        } else {
            isDefault = true;
        }


        //当下时间段分组排行榜信息
        List<RankGroupScoreDto> rankGroupScoreDtoList = getRankGroupList(xsPointRecordList, planGroupDetailDto);

        rankGroupScoreDtoList = setAvgScoreSort(rankGroupScoreDtoList, isDefault);


        //过去时间段的数据查询条件
        PlanGroupDetailDto beforePlanGroupDetailDto = JSONObject.parseObject(JSONObject.toJSONString(planGroupDetailDto), PlanGroupDetailDto.class);

        //过去时间段分组排行榜信息
        List<RankGroupScoreDto> beforeRankGroupScoreDtoList = getRankGroupList(beforeXsPointRecordList, beforePlanGroupDetailDto);

        boolean isBeforeDefault = CollectionUtils.isEmpty(beforeXsPointRecordList);
        beforeRankGroupScoreDtoList = setAvgScoreSort(beforeRankGroupScoreDtoList, isBeforeDefault);

        //浮动排名设置
        rankGroupScoreDtoConvert.compareSortVal(rankGroupScoreDtoList, beforeRankGroupScoreDtoList);


        //进行组下学生排序，大组长放最前面，小组长放第二，其他按照分数排序
        rankGroupScoreDtoConvert.doStudentListSort(rankGroupScoreDtoList);

        //过滤负分数据
        if (!includeMinusData) {
            rankGroupScoreDtoList = rankGroupScoreDtoList.stream().filter(t -> t.getAvgScore()
                    .compareTo(BigDecimal.valueOf(0)) > 0).collect(Collectors.toList());
        }

        rankGroupScoreRespDto.setGroupList(rankGroupScoreDtoList);
        rankGroupScoreRespDto.setIsExistRecord(isExistRecord);

        return rankGroupScoreRespDto;
    }

    /**
     * 按照平均分进行排序
     *
     * @param rankGroupScoreDtoList
     * @param isDefault
     * @return
     */
    public List<RankGroupScoreDto> setAvgScoreSort(List<RankGroupScoreDto> rankGroupScoreDtoList, Boolean isDefault) {

        //按照分值从高到底聚合
        List<Map.Entry<BigDecimal, List<RankGroupScoreDto>>> list = rankGroupScoreDtoList.stream().collect(Collectors.groupingBy(RankGroupScoreDto::getAvgScore)).entrySet()
                .stream().sorted((s1, s2) -> -Double.compare(s1.getKey().doubleValue(), s2.getKey().doubleValue()))
                .collect(Collectors.toList());
        int index = 1;
        for (Map.Entry<BigDecimal, List<RankGroupScoreDto>> bigDecimalListEntry : list) {
            int size = bigDecimalListEntry.getValue().size();
            for (RankGroupScoreDto rankGroupScoreDto : bigDecimalListEntry.getValue()) {
                if (isDefault) {
                    rankGroupScoreDto.setSortVal(1);
                } else {
                    rankGroupScoreDto.setSortVal(index);
                }
            }

            index = index + size;
        }

        Comparator<RankGroupScoreDto> sortVal = Comparator.comparing(RankGroupScoreDto::getSortVal);

        return ListUtil.sort(rankGroupScoreDtoList, sortVal);

    }


    private List<RankGroupScoreDto> getRankGroupList(List<XsPointRecord> xsPointRecordList, PlanGroupDetailDto planGroupDetailDto) {

        //进行分组排行榜统计
        scoreV2Component.setGroupRecordDetailScore(planGroupDetailDto, xsPointRecordList);

        //四舍五入 保留2位小数
        planService.round(planGroupDetailDto, 2);

        //进行数据组装
        return rankGroupScoreDtoConvert.doTransList(planGroupDetailDto);

    }

    @Override
    public ScoreQuery groupSearchCondition(RecordReq recordReq) throws BusinessException {
        ScoreQuery scoreQuery = new ScoreQuery();
        if (recordReq.getTimeType().equals(TimeTypeConst.DEFINE)) {
            if (!(StrUtil.isNotEmpty(recordReq.getStartTime()) && StrUtil.isNotEmpty(recordReq.getEndTime()))) {
                throw new BusinessException("自定义类型时间段必须设置");
            }
            scoreQuery.setStartTime(recordReq.getStartTime());
            scoreQuery.setEndTime(recordReq.getEndTime());
        }

        scoreQuery.setPlanId(recordReq.getPlanId());
        scoreQuery.setSaasClassId(recordReq.getSaasClassId());
        scoreQuery.setTimeType(recordReq.getTimeType());
        //时间段设置
        scoreV2Component.setCurTimeFrame(scoreQuery);
        if (null != recordReq.getIsExceptMinusScore()) {
            scoreQuery.setIsExceptMinusScore(recordReq.getIsExceptMinusScore());
        }


        if (!CollectionUtils.isEmpty(recordReq.getUserIds())) {
            scoreQuery.setUserIds(recordReq.getUserIds());
        }

        if (!CollectionUtils.isEmpty(recordReq.getPlanTagIds())) {
            scoreQuery.setPlanTagIds(recordReq.getPlanTagIds());
        }

        if (!CollectionUtils.isEmpty(recordReq.getExceptChannelIds())) {
            scoreQuery.setExceptChannelIds(recordReq.getExceptChannelIds());
        }


        return scoreQuery;
    }


    /**
     * 排行榜-个人榜
     *
     * @param recordReq
     * @param xsUserInfo
     * @throws BusinessException
     */
    @Override
    public RankStudentScoreRespDto personal(RecordReq recordReq, XsUserInfo xsUserInfo
            , List<XsPointRecord> xsPointRecordList, List<XsPointRecord> beforeXsPointRecordList, Boolean includeMinusData) {


        boolean isExistRecord = false;


        //获取班级下所有学生
        List<XsClassStudent> xsClassStudentList = xsClassStudentMapper.getListByClassId(recordReq.getSaasClassId(), xsUserInfo.getSchoolId());

        //查询学生头像信息
        XsAvatarQuery xsAvatarQuery = new XsAvatarQuery();
        xsAvatarQuery.setSaasClassId(recordReq.getSaasClassId());
        xsAvatarQuery.setBusinessType(AvatarBusinessTypeEnum.STUDENT.getType());
        List<XsAvatar> studentAvatarList = xsAvatarMapper.getListByCondition(xsAvatarQuery);
        //重新填充学生头像值
        planGroupDetailDtoConvert.overwriteStudentIcon(xsClassStudentList, studentAvatarList);

        List<PlanGroupDetailDto.StudentDto> studentDtoList = planGroupDetailDtoConvert.transToList(xsClassStudentList, studentAvatarList);

        //查询积分
        //进行查询积分数据条件
        //当下时间段个人排行榜信息
        boolean isDefault = false;
        if (!CollectionUtils.isEmpty(xsPointRecordList)) {
            isExistRecord = true;
        } else {
            isDefault = true;
        }


        scoreV2Component.setStudentRecordDetailScore(studentDtoList, xsPointRecordList);

        List<RankStudentScoreDto> rankStudentScoreDtoList = rankStudentScoreDtoConvert.doTransList(studentDtoList);
        //进行四舍五入
        for (RankStudentScoreDto rankStudentScoreDto : rankStudentScoreDtoList) {
            rankStudentScoreDto.setScore(NumberUtil.round(rankStudentScoreDto.getScore(), 2));
            rankStudentScoreDto.setBeforePeriodScore(NumberUtil.round(rankStudentScoreDto.getBeforePeriodScore(), 2));
        }

        rankStudentScoreDtoList = setScoreSort(rankStudentScoreDtoList, isDefault);


        //过去时间段的数据查询条件
        List<PlanGroupDetailDto.StudentDto> beforeStudentDtoList = planGroupDetailDtoConvert.transToList(xsClassStudentList, studentAvatarList);
        scoreV2Component.setStudentRecordDetailScore(beforeStudentDtoList, beforeXsPointRecordList);

        List<RankStudentScoreDto> beforeRankStudentScoreDtoList = rankStudentScoreDtoConvert.doTransList(beforeStudentDtoList);
        //进行四舍五入
        for (RankStudentScoreDto rankStudentScoreDto : beforeRankStudentScoreDtoList) {
            rankStudentScoreDto.setScore(NumberUtil.round(rankStudentScoreDto.getScore(), 2));
            rankStudentScoreDto.setBeforePeriodScore(NumberUtil.round(rankStudentScoreDto.getBeforePeriodScore(), 2));
        }

        boolean isBeforeDefault = false;
        if (CollectionUtils.isEmpty(beforeXsPointRecordList)) {
            isBeforeDefault = true;
        }

        beforeRankStudentScoreDtoList = setScoreSort(beforeRankStudentScoreDtoList, isBeforeDefault);

        //设置之前周期分字段
        rankStudentScoreDtoConvert.setBeforePeriodScore(rankStudentScoreDtoList, beforeRankStudentScoreDtoList);

        //浮动排名设置
        rankStudentScoreDtoConvert.compareSortVal(rankStudentScoreDtoList, beforeRankStudentScoreDtoList);

        //过滤负分数据
        if (!includeMinusData) {
            rankStudentScoreDtoList = rankStudentScoreDtoList.stream().filter(t -> t.getScore()
                    .compareTo(BigDecimal.valueOf(0)) > 0).collect(Collectors.toList());
        }

        RankStudentScoreRespDto rankStudentScoreRespDto = new RankStudentScoreRespDto();
        rankStudentScoreRespDto.setStudentList(rankStudentScoreDtoList);
        rankStudentScoreRespDto.setIsExistRecord(isExistRecord);


        return rankStudentScoreRespDto;
    }

    /**
     * 学生按照总分排名
     *
     * @param rankStudentScoreDtoList
     * @return
     */
    public List<RankStudentScoreDto> setScoreSort(List<RankStudentScoreDto> rankStudentScoreDtoList, Boolean isDefault) {

        //按照分值从高到底聚合
        List<Map.Entry<BigDecimal, List<RankStudentScoreDto>>> list = rankStudentScoreDtoList.stream().collect(Collectors.groupingBy(RankStudentScoreDto::getScore)).entrySet()
                .stream().sorted((s1, s2) -> -Double.compare(s1.getKey().doubleValue(), s2.getKey().doubleValue()))
                .collect(Collectors.toList());


        int index = 1;
        for (Map.Entry<BigDecimal, List<RankStudentScoreDto>> bigDecimalListEntry : list) {
            int size = bigDecimalListEntry.getValue().size();
            for (RankStudentScoreDto rankStudentScoreDto : bigDecimalListEntry.getValue()) {
                if (isDefault) {
                    rankStudentScoreDto.setSortVal(1);
                } else {
                    rankStudentScoreDto.setSortVal(index);
                }
            }

            index = index + size;
        }
        Comparator<RankStudentScoreDto> sortVal = Comparator.comparing(RankStudentScoreDto::getSortVal);

        return ListUtil.sort(rankStudentScoreDtoList, sortVal);
    }


    @Override
    public List<RankStudentScoreDto> progress(List<RankStudentScoreDto> rankStudentScoreDtoList, Boolean includeMinusData) {


        List<RankStudentScoreDto> exitRankStudentScoreDtoList = new ArrayList<>();
        List<RankStudentScoreDto> unExitRankStudentScoreDtoList = new ArrayList<>();

        for (RankStudentScoreDto rankStudentScoreDto : rankStudentScoreDtoList) {
            if (rankStudentScoreDto.getBeforePeriodIsExistPointRecord()) {
                BigDecimal subInfo = NumberUtil.sub(rankStudentScoreDto.getScore(), rankStudentScoreDto.getBeforePeriodScore());
                rankStudentScoreDto.setCompareScore(NumberUtil.round(subInfo.doubleValue(), 2));

                exitRankStudentScoreDtoList.add(rankStudentScoreDto);
            } else {
                rankStudentScoreDto.setCompareScore(BigDecimal.valueOf(0));
                unExitRankStudentScoreDtoList.add(rankStudentScoreDto);
            }
        }

        int index = 1;

        if (!CollectionUtils.isEmpty(exitRankStudentScoreDtoList)) {
            //逻辑调整根据 积分分差进行排名
            List<Map.Entry<BigDecimal, List<RankStudentScoreDto>>> list = exitRankStudentScoreDtoList.stream()
                    .collect(Collectors.groupingBy(RankStudentScoreDto::getCompareScore)).entrySet()
                    .stream().sorted((s1, s2) -> -Double.compare(s1.getKey().doubleValue(), s2.getKey().doubleValue()))
                    .collect(Collectors.toList());

            for (Map.Entry<BigDecimal, List<RankStudentScoreDto>> bigDecimalListEntry : list) {
                int size = bigDecimalListEntry.getValue().size();
                for (RankStudentScoreDto rankStudentScoreDto : bigDecimalListEntry.getValue()) {
                    rankStudentScoreDto.setSortVal(index);
                }
                index = index + size;
            }
        }

        if (!CollectionUtils.isEmpty(unExitRankStudentScoreDtoList)) {
            for (RankStudentScoreDto rankStudentScoreDto : unExitRankStudentScoreDtoList) {
                rankStudentScoreDto.setSortVal(index);
            }
        }

        exitRankStudentScoreDtoList.addAll(unExitRankStudentScoreDtoList);

        if (!includeMinusData) {
            exitRankStudentScoreDtoList = exitRankStudentScoreDtoList.stream().filter(t -> t.getCompareScore()
                    .compareTo(BigDecimal.valueOf(0)) > 0).collect(Collectors.toList());
        }

        Comparator<RankStudentScoreDto> sortVal = Comparator.comparing(RankStudentScoreDto::getSortVal);
        return ListUtil.sort(exitRankStudentScoreDtoList, sortVal);
    }

    @Override
    public void export(RecordReq recordReq, XsUserInfo xsUserInfo
            , PlanGroupDetailDto planGroupDetailDto, List<PlanGroupDetailDto.StudentDto> studentDtoList, HttpServletResponse response) throws BusinessException, IOException {

        //设置查询搜索条件
        PointRecordQuery pointRecordQuery = getSearchCondition(recordReq, xsUserInfo, studentDtoList);

        List<XsPointRecord> xsPointRecordList = xsPointRecordMapper.getListByPointRecordQuery(pointRecordQuery);

        //设置分值
        scoreV2Component.setDetailScore(planGroupDetailDto, xsPointRecordList);

        //获取该方案下所有分类，用于分类的排序
        PlanTagQuery planTagQuery = new PlanTagQuery();
        planTagQuery.setPlanId(recordReq.getPlanId());
        List<XsPlanTag> xsPlanTagList = xsPlanTagMapper.getListByCondition(planTagQuery);


        boolean justSheet2 = !CollectionUtils.isEmpty(recordReq.getStudentIds());

        //进行excel 导出逻辑
        excelComponent.doClassInfo(recordReq, planGroupDetailDto, xsPointRecordList, xsPlanTagList
                , response, justSheet2);

    }

    private PointRecordQuery getSearchCondition(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanGroupDetailDto.StudentDto> studentDtoList) {

        PointRecordQuery pointRecordQuery = new PointRecordQuery();
        pointRecordQuery.setPlanId(recordReq.getPlanId());
        pointRecordQuery.setSaasClassId(recordReq.getSaasClassId());


        if (!StringUtils.isEmpty(recordReq.getStartTime())) {
            DateTime startTime = DateUtil.parse(recordReq.getStartTime());
            pointRecordQuery.setStartTime(DateUtil.beginOfDay(startTime).toString());
        }

        if (!StringUtils.isEmpty(recordReq.getEndTime())) {
            DateTime endTime = DateUtil.parse(recordReq.getEndTime());
            pointRecordQuery.setEndTime(DateUtil.endOfDay(endTime).toString());
        } else {
            // 更好的使用索引
            Date now = new Date();
            DateTime dateTime = DateUtil.offsetHour(now, 1);
            pointRecordQuery.setEndTime(DateUtil.formatDateTime(dateTime));
        }

        if (!CollectionUtils.isEmpty(recordReq.getUserIds())) {
            pointRecordQuery.setUserIds(recordReq.getUserIds());
        }

        if (!CollectionUtils.isEmpty(recordReq.getStudentIds())) {

            Set<Long> studentIds = new HashSet<>();
            for (String studentId : recordReq.getStudentIds()) {
                studentIds.add(Long.valueOf(studentId));
            }

            pointRecordQuery.setStudentIds(studentIds);
        }


        if (!CollectionUtils.isEmpty(recordReq.getTagNameList())) {
            pointRecordQuery.setTagNameList(recordReq.getTagNameList());
        }


        // 正负分
        if (!StrUtil.isEmpty(recordReq.getScoreType())) {

            if (ScoreTypeConst.PLUS.equals(recordReq.getScoreType())) {
                pointRecordQuery.setIsPlusScore(true);
            }

            if (ScoreTypeConst.MINUS.equals(recordReq.getScoreType())) {
                pointRecordQuery.setIsMinusScore(true);
            }
        }

        if (!CollectionUtils.isEmpty(recordReq.getExceptChannelIds())) {
            pointRecordQuery.setExceptChannelIds(recordReq.getExceptChannelIds());
        }


        return pointRecordQuery;
    }


    @Override
    public JSONObject getList(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanGroupDetailDto.StudentDto> studentDtoList) {

        Integer page = recordReq.getPage();
        Integer pageSize = recordReq.getPageSize();
        Integer offset = (page - 1) * pageSize;

        PointRecordQuery pointRecordQuery = getSearchCondition(recordReq, xsUserInfo, studentDtoList);


        int allCount = xsPointRecordMapper.getListCount(pointRecordQuery);

        Pagination pagination = new Pagination();
        pagination.setAllCount(allCount);
        pagination.setPage(page);
        pagination.setPageSize(pageSize);
        pagination.setAllPage(allCount, pageSize);
        List<StuPointRecordDto> stuPointRecordDtoList = new ArrayList<>();
        if (allCount > 0) {
            List<XsPointRecord> xsPointRecordList = xsPointRecordMapper.getList(pointRecordQuery, offset, pageSize);

            stuPointRecordDtoList = stuPointRecordDtoConvert.transToList(xsPointRecordList, studentDtoList);
        }
        JSONObject result = new JSONObject();
        result.put("pagination", pagination);
        result.put("recordList", stuPointRecordDtoList);
        return result;
    }

    @Override
    public List<TagDto> tagGetList(RecordReq recordReq, XsUserInfo xsUserInfo) throws BusinessException {

        //获取所有方案下的分类
        PlanTagQuery planTagQuery = new PlanTagQuery();
        planTagQuery.setPlanId(recordReq.getPlanId());
        List<XsPlanTag> xsPlanTagList = xsPlanTagMapper.getListByCondition(planTagQuery);

        //获取校级的分类
        List<PlanTagCommentDto> schoolTagList = zongPingService.getSchoolTagList(recordReq.getSaasCampusId(),
                xsUserInfo.getSchoolId(), Long.valueOf(xsUserInfo.getStaffId()));

        if (!CollUtil.isEmpty(schoolTagList)) {
            for (PlanTagCommentDto planTagCommentDto : schoolTagList) {
                XsPlanTag xsPlanTag = new XsPlanTag();
                xsPlanTag.setPlanTagId(Long.valueOf(planTagCommentDto.getPlanTagId()));
                xsPlanTag.setPlanTagName(planTagCommentDto.getPlanTagName());
                xsPlanTag.setPlanId(recordReq.getPlanId());
                xsPlanTag.setSortVal(planTagCommentDto.getSortVal());
                xsPlanTag.setApplyLevel(planTagCommentDto.getApplyLevel());
                xsPlanTagList.add(xsPlanTag);
            }
        }

        PointRecordQuery pointRecordQuery = new PointRecordQuery();
        pointRecordQuery.setPlanId(recordReq.getPlanId());
        pointRecordQuery.setSaasClassId(recordReq.getSaasClassId());
        pointRecordQuery.setChannel(XsPointRecord.CHANNEL_COMMENT);
        List<String> xsTagNameList = xsPointRecordMapper.getTagNamesByPointRecord(pointRecordQuery);

        List<TagDto> tagDtoList = tagDtoConvert.getTagListV2(xsTagNameList, xsPlanTagList);
        for (TagDto tagDto : tagDtoList) {
            tagDto.setSortVal(null);
        }

        return tagDtoList;

    }


    @Override
    public void del(RecordReq recordReq, XsUserInfo xsUserInfo) throws BusinessException {
        //删除操作数据不会多 慢慢删吧
        if (!CollectionUtils.isEmpty(recordReq.getRecordIds())) {

            if (recordReq.getRecordIds().size() > CommonConst.DEL_POINT_THRESHOLD_VALUE) {
                // 删除数量大于阈值 属于点评撤销操作，
                // 因此 statStorage.setStatDaysFormat可以执行，定时任务默认会计算前一天的统计数据
                // 对数据进行切片 每次一千条 推送队列  进行删除防止一次删除太多 导致 SQL 超时。

                List<List<Long>> lists = XsListUtil.sliceList(new ArrayList<>(recordReq.getRecordIds()), CommonConst.SLICE_SIZE);
                for (List<Long> longList : lists) {
                    PointTaskDto pointTaskDto = new PointTaskDto();
                    pointTaskDto.setTaskId(idManageComponent.nextId());
                    pointTaskDto.setPointRecordIds(longList);
                    pointTaskDto.setXsUserInfo(xsUserInfo);
                    pointProducer.send(pointTaskDto);
                }

            } else {
                //删除数量少 业务直接实现。 查询待删除
                List<XsPointRecord> xsPointRecordList = xsPointRecordMapper.getListByIds(recordReq.getRecordIds());
                for (XsPointRecord xsPointRecord : xsPointRecordList) {
                    xsPointRecord.setIsDeleted(DeletedConst.YES);
                    xsPointRecord.setUpdateTime(com.hailiang.edu.xsjlsys.util.DateUtil.getDateTime());
                }
                pointService.doDel(xsPointRecordList, xsUserInfo);

                //可能是删除的历史数据 因此需要获取 待重新执行统计的日期
                if (!CollUtil.isEmpty(xsPointRecordList)) {

                    Set<String> dateSet = new HashSet<>();
                    for (XsPointRecord record : xsPointRecordList) {
                        Date createTime = DateUtil.parse(record.getCreateTime());
                        Date nextDay = DateUtil.offsetDay(createTime, 1);

                        dateSet.add(DateUtil.format(nextDay, "yyyy-MM-dd 00:00:00"));
                    }
                    statStorage.setStatDaysFormat(dateSet);
                }
            }

        }
    }


    @Override
    public StudentReportRespDto report(RecordReq recordReq, XsUserInfo xsUserInfo, List<PlanGroupDetailDto.GroupDto> groupList
            , List<PlanGroupDetailDto.StudentDto> studentDtoList) throws BusinessException {

        //验证学生id的有效性
        Optional<PlanGroupDetailDto.StudentDto> studentDtoOptional = studentDtoList.stream()
                .filter(t -> t.getStudentId().equals(recordReq.getStudentId())).findFirst();
        if (!studentDtoOptional.isPresent()) {
            throw new BusinessException("学生不在本班级", ApiCodeEnum.REFRESH_ERROR.getCode());
        }


        ScoreQuery scoreQuery = new ScoreQuery();
        scoreQuery.setStartTime(recordReq.getStartTime());
        scoreQuery.setEndTime(recordReq.getEndTime());
        scoreQuery.setSaasClassId(recordReq.getSaasClassId());
        if (recordReq.getOnlyMe() != null && recordReq.getOnlyMe()) {
            scoreQuery.setUserId(xsUserInfo.getUserId());
        }

        timeComponent.setDefineTimeFrame(scoreQuery);
        scoreQuery.setOrderByCreateTime(true);
        if (!CollectionUtils.isEmpty(recordReq.getExceptChannelIds())) {
            scoreQuery.setExceptChannelIds(recordReq.getExceptChannelIds());
        }

        if (recordReq.getPlanId() != null) {
            scoreQuery.setPlanId(recordReq.getPlanId());
        } else {
            //代表全班
            PlanUserQuery planUserQuery = new PlanUserQuery();
            planUserQuery.setSaasClassId(recordReq.getSaasClassId());
            List<XsPlanUser> xsPlanUserList = xsPlanUserMapper.getListByCondition(planUserQuery);
            if (!CollectionUtils.isEmpty(xsPlanUserList)) {
                Set<Long> planIds = xsPlanUserList.stream().map(XsPlanUser::getPlanId).collect(Collectors.toSet());
                scoreQuery.setPlanIds(planIds);
            } else {
                //设置一个查不到的值
                scoreQuery.setSaasClassId("-9999");
            }
        }


        List<XsPointRecord> xsPointRecordList = xsPointRecordMapper.getListByScoreQuery(scoreQuery);

        //过滤一层
        xsPointRecordList = filterIsNotExistStudent(xsPointRecordList, studentDtoList);

        boolean isExistRecord = false;

        if (!CollectionUtils.isEmpty(xsPointRecordList)) {
            isExistRecord = true;
        }


        //过去时间段的数据查询条件
        ScoreQuery beforeScoreQuery = JSONObject.parseObject(JSONObject.toJSONString(scoreQuery), ScoreQuery.class);
        timeComponent.setMomDateRange(beforeScoreQuery);
        List<XsPointRecord> beforeXsPointRecordList = xsPointRecordMapper.getListByScoreQuery(beforeScoreQuery);

        //过滤一层
        beforeXsPointRecordList = filterIsNotExistStudent(beforeXsPointRecordList, studentDtoList);

        //获取方案配置的分类内容列表
        List<XsPlanTag> xsPlanTagList = new ArrayList<>();
        if (recordReq.getPlanId() != null) {
            PlanTagQuery planTagQuery = new PlanTagQuery();
            planTagQuery.setPlanId(recordReq.getPlanId());
            xsPlanTagList = xsPlanTagMapper.getListByCondition(planTagQuery);
        }

        //进行报告对象数据生成
        StudentReportRespDto studentReportRespDto = new StudentReportRespDto();
        studentReportRespDto.setIsExistRecord(isExistRecord);
        //发展情况
        studentReportRespDto.setDevelopment(developmentDtoConvert.doTransObj(xsPlanTagList, recordReq.getStudentId(), xsPointRecordList, beforeXsPointRecordList));
        //积分表现
        studentReportRespDto.setPointPerformance(pointPerformanceDtoConvert.doTransObj(groupList, studentDtoList, recordReq.getStudentId(), xsPointRecordList, beforeXsPointRecordList));


        return studentReportRespDto;
    }


    private List<XsPointRecord> filterIsNotExistStudent(List<XsPointRecord> xsPointRecordList, List<PlanGroupDetailDto.StudentDto> studentDtoList) {

        // 还需要对点评明细进行过滤 (当前不在该班级下的学生的点评记录需要过滤) 后面再说
        Set<Long> studentIds = new HashSet<>();
        for (PlanGroupDetailDto.StudentDto studentDto : studentDtoList) {
            studentIds.add(Long.parseLong(studentDto.getStudentId()));
        }

        List<XsPointRecord> xsPointRecordList1 = new ArrayList<>();

        for (XsPointRecord xsPointRecord : xsPointRecordList) {
            if (studentIds.contains(xsPointRecord.getStudentId())) {
                xsPointRecordList1.add(xsPointRecord);
            }
        }

        return xsPointRecordList1;

    }


    /**
     * 根据身份和是否协同2个因素，展示下拉选项：
     * <p>
     * 1）班主任：
     * 协同：展示1、2、3这3个选项
     * 未协同：展示1、3这2个选项
     * 2）任课老师：
     * 协同：展示2、3这2个选项
     * 未协同：不展示
     *
     * @param recordReq
     * @param xsUserInfo
     * @param userDtoList
     * @return
     */
    @Override
    public List<SelectOptionDto> selectOptionLists(RecordReq recordReq, XsUserInfo xsUserInfo, List<UserDto> userDtoList) {

        //获取系统定义角色code
        String roleCode = roleService.getRoleCode(Long.valueOf(recordReq.getSaasClassId()), Long.valueOf(recordReq.getSaasSchoolId())
                , Long.valueOf(xsUserInfo.getStaffId()));

        if (roleCode.equals(RoleEnum.NONE.getCode())) {
            return new ArrayList<>();
        }

        List<SelectOptionDto> selectOptionDtoList = new ArrayList<>();
        if (roleCode.equals(RoleEnum.HEAD_TEACHER.getCode())) {
            if (userDtoList.size() > 1) {
                //代表有协同
                for (SelectOptionEnum value : SelectOptionEnum.values()) {
                    SelectOptionDto selectOptionDto = new SelectOptionDto();
                    selectOptionDto.setSelectOptionCode(value.getCode());
                    selectOptionDto.setSelectOptionDesc(value.getDesc());
                    selectOptionDtoList.add(selectOptionDto);
                }
            } else {
                SelectOptionDto selectOptionDto = new SelectOptionDto();
                selectOptionDto.setSelectOptionCode(SelectOptionEnum.CLASS.getCode());
                selectOptionDto.setSelectOptionDesc(SelectOptionEnum.CLASS.getDesc());
                selectOptionDtoList.add(selectOptionDto);

                SelectOptionDto selectOptionDto1 = new SelectOptionDto();
                selectOptionDto1.setSelectOptionCode(SelectOptionEnum.PLAN_ONLYME.getCode());
                selectOptionDto1.setSelectOptionDesc(SelectOptionEnum.PLAN_ONLYME.getDesc());
                selectOptionDtoList.add(selectOptionDto1);
            }
        }

        if (roleCode.equals(RoleEnum.GENERAL_TEACHER.getCode())) {
            if (userDtoList.size() > 1) {
                SelectOptionDto selectOptionDto = new SelectOptionDto();
                selectOptionDto.setSelectOptionCode(SelectOptionEnum.PLAN.getCode());
                selectOptionDto.setSelectOptionDesc(SelectOptionEnum.PLAN.getDesc());
                selectOptionDtoList.add(selectOptionDto);

                SelectOptionDto selectOptionDto1 = new SelectOptionDto();
                selectOptionDto1.setSelectOptionCode(SelectOptionEnum.PLAN_ONLYME.getCode());
                selectOptionDto1.setSelectOptionDesc(SelectOptionEnum.PLAN_ONLYME.getDesc());
                selectOptionDtoList.add(selectOptionDto1);
            }
        }


        return selectOptionDtoList;

    }


    @Override
    public Long getStudent(RecordReq recordReq) {
        log.info("获取学生id入参数据：{}", JSONUtil.toJsonStr(recordReq));
//        String msgJson;
        StudentDto studentDto = new StudentDto();
        String secretInfo = recordReq.getSecretInfo();
        if (StrUtil.startWith(secretInfo, SAAS_STUDENT_SECRET_PREFIX)) {
            log.info("海思谷特殊入口逻辑：{}", secretInfo);
            secretInfo = StrUtil.removePrefix(secretInfo, SAAS_STUDENT_SECRET_PREFIX);
            String studentId = SecureUtil.aes(specialAesKey.getBytes(StandardCharsets.UTF_8)).decryptStr(secretInfo);
            if (StrUtil.isEmpty(studentId)) {
                throw new BusinessException("密文非法");
            }
            log.info("海思谷特殊入口解析后字符串：{}", studentId);
            studentDto.setSaasStudentId(Convert.toLong(studentId));
        } else {
            String msgJson = rsaHaiComponent.decrypt(recordReq.getSecretInfo());
            if (StrUtil.isEmpty(msgJson)) {
                throw new BusinessException("密文非法");
            }
            studentDto = JSONArray.parseObject(msgJson, StudentDto.class);
        }
        StudentQuery studentQuery = new StudentQuery();
        studentQuery.setSaasStudentId(String.valueOf(studentDto.getSaasStudentId()));
        List<XsStudent> xsStudentList = xsStudentMapper.getListByCondition(studentQuery);
        if (CollectionUtils.isEmpty(xsStudentList)) {

            log.info("学生信息不存在 学生id:" + JSONObject.toJSONString(studentDto.getSaasStudentId()));

            //返回一个不存在的学生id
            return -99999L;
        }

        XsStudent defaultStudent = xsStudentList.get(0);
        return defaultStudent.getStudentId();
    }


    @Override
    public List<SmsReportDto> reportGetLists(RecordReq recordReq) {

        SmsReportStudentQuery smsReportStudentQuery = new SmsReportStudentQuery();
        smsReportStudentQuery.setLastSmsReportId(Long.valueOf(recordReq.getLastSmsReportId()));
        smsReportStudentQuery.setStudentId(Long.valueOf(recordReq.getStudentId()));
        smsReportStudentQuery.setPageSize(recordReq.getPageSize());
        smsReportStudentQuery.setSortCriteria("sms_report_id desc");


        List<XsSmsReportStudent> xsSmsReportStudentList = xsSmsReportStudentMapper.getListByCondition(smsReportStudentQuery);
        if (CollectionUtils.isEmpty(xsSmsReportStudentList)) {
            return new ArrayList<>();
        }

        List<Long> userIds = xsSmsReportStudentList.stream().map(t -> t.getXsSmsReport().getUserId()).collect(Collectors.toList());
        List<XsUser> xsUserList = xsUserMapper.getListByIds(userIds);

        Map<Long, String> userIdWithAccountNameMap = xsUserList.stream()
                .collect(Collectors.toMap(XsUser::getUserId, XsUser::getAccountName, (p1, p2) -> p2));

        Set<Long> smsReportIds = xsSmsReportStudentList.stream().map(XsSmsReportStudent::getSmsReportId).collect(Collectors.toSet());
        List<SmsReportPoint> smsReportPointList = smsReportPointStorage.getList(smsReportIds);
        List<SmsReportOther> smsReportOtherList = smsReportOtherStorage.getList(smsReportIds);

        //获取学生信息
        XsStudent xsStudent = xsStudentMapper.selectById(recordReq.getStudentId());

        return smsReportDtoConvert.toObjList(xsSmsReportStudentList, userIdWithAccountNameMap, smsReportPointList, smsReportOtherList, xsStudent);


    }

    @Override
    public void setReportDetailReqParam(RecordReq recordReq) {

        if (!StrUtil.isEmpty(recordReq.getLinkSign())) {

            SmsLinkDto smsLinkDto = smsLinkSignStorage.getSmsLink(recordReq.getLinkSign());
            if (smsLinkDto == null) {
                throw new BusinessException("linkSign 无效");
            }

            recordReq.setStudentId(String.valueOf(smsLinkDto.getStudentId()));
            recordReq.setSmsReportId(String.valueOf(smsLinkDto.getSmsReportId()));

        } else {

            if (StrUtil.isEmpty(recordReq.getStudentId()) || StrUtil.isEmpty(recordReq.getSmsReportId())) {
                throw new BusinessException("参数非法");
            }

        }


    }

    @Override
    public List<SmsReportDetailDto> smsReportDetail(RecordReq recordReq, XsSmsReportStudent xsSmsReportStudent) {

        Set<Long> smsReportIds = new HashSet<>();
        smsReportIds.add(xsSmsReportStudent.getSmsReportId());
        List<SmsReportPoint> smsReportPointList = smsReportPointStorage.getList(smsReportIds);
        List<SmsReportOther> smsReportOtherList = smsReportOtherStorage.getList(smsReportIds);
        SmsReportOther smsReportOther = smsReportOtherList.get(0);
        List<SmsReportStudentEvaluate> smsReportStudentEvaluateList = smsReportStudentEvaluateStorage.getList(smsReportIds);

        List<SmsReportOther.PlanTagDto> planTagDtoList = smsReportOther.getPlanTagList();
        List<XsPlanTag> xsPlanTagList = planTagConvert.toObjList(planTagDtoList);

        List<XsPointRecord> xsPointRecordList = pointRecordConvert.toObjList(smsReportPointList);

        if (!CollectionUtils.isEmpty(xsPointRecordList)) {

            Comparator<XsPointRecord> createTimeSort = Comparator.comparing(XsPointRecord::getCreateTime);
            ListUtil.sort(xsPointRecordList, createTimeSort.reversed());
        }

        //获取发展状况
        StudentReportRespDto.DevelopmentDto developmentDto = developmentDtoConvert.doTransObj(xsPlanTagList, recordReq.getStudentId(), xsPointRecordList, new ArrayList<>());
        SmsReportDetailDto smsReportDetailDto = smsReportDetailDtoConvert.toObj(recordReq, xsSmsReportStudent, smsReportPointList, smsReportOther);
        smsReportDetailDto.setDevelopment(smsReportDetailDtoConvert.toDevelopmentObj(developmentDto));

        List<PlanGroupDetailDto.StudentDto> studentDtos = planGroupDetailDtoConvert.toObjList(smsReportOther.getStudentList());
        //积分表现
        StudentReportRespDto.PointPerformanceDto pointPerformanceDto = pointPerformanceDtoConvert.doTransObj(new ArrayList<>(), studentDtos, recordReq.getStudentId()
                , xsPointRecordList, new ArrayList<>());
        smsReportDetailDto.setPointPerformance(smsReportDetailDtoConvert.toPointPerformance(pointPerformanceDto, smsReportPointList));

        //奖状记录ids
        smsReportDetailDto.setAwardRecordIds(new HashSet<>());
        if (smsReportOther.getAwardRecordIds() != null && !CollUtil.isEmpty(smsReportOther.getAwardRecordIds())) {
            smsReportDetailDto.setAwardRecordIds(smsReportOther.getAwardRecordIds());
        }

        List<SmsReportDetailDto.EvaluateDto> evaluateDtos = smsReportDetailDtoConvert.toEvaluateDtos(smsReportStudentEvaluateList);
        if (!CollectionUtils.isEmpty(evaluateDtos)) {
            smsReportDetailDto.setEvaluates(evaluateDtos);
        }

        List<SmsReportDetailDto> smsReportDetailDtoList = new ArrayList<>();
        smsReportDetailDtoList.add(smsReportDetailDto);
        return smsReportDetailDtoList;
    }

    @Override
    public XsSmsReportStudent validateSmsReportStudent(RecordReq recordReq) {

        SmsReportStudentQuery smsReportStudentQuery = new SmsReportStudentQuery();
        smsReportStudentQuery.setSmsReportId(Long.valueOf(recordReq.getSmsReportId()));
        smsReportStudentQuery.setStudentId(Long.valueOf(recordReq.getStudentId()));
        XsSmsReportStudent xsSmsReportStudent = xsSmsReportStudentMapper.getRowByCondition(smsReportStudentQuery);
        if (xsSmsReportStudent == null) {
            throw new BusinessException("报告不存在", ApiCodeEnum.CODE_ERROR_REFRESH_CURRENT_PAGE.getCode());
        }
        return xsSmsReportStudent;
    }

    @Override
    public BehaviorReportDetailDto getSmsReportExt(XsSmsReportStudent xsSmsReportStudent) {

        //报告数据
        XsSmsReport xsSmsReport = xsSmsReportMapper.selectById(xsSmsReportStudent.getSmsReportId());
        //学生数据
        XsStudent xsStudent = xsStudentMapper.selectById(xsSmsReportStudent.getStudentId());

        //用户数据
        Long userId = 0L;
        String userName = "";
        if (xsSmsReport != null) {
            XsUser rowByUserId = xsUserMapper.getRowByUserId(xsSmsReport.getUserId());
            userId = rowByUserId.getUserId();
            userName = rowByUserId.getAccountName();
        }

        //班级数据
        FClassRespDto fClassRespDto = saasClient.getClassInfoByClassId(xsSmsReportStudent.getSaasClassId());
        //学校数据
        String schoolName = saasService.getSchoolName(xsSmsReportStudent.getSaasSchoolId());

        return smsReportExtDtoConvert.toObj(xsSmsReportStudent, xsStudent, userId, userName, fClassRespDto, schoolName);

    }

    @Override
    public boolean setXsPointRecordList(RecordReq recordReq, XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList) {
        //进行查询积分数据条件
        ScoreQuery currentScoreQuery = groupSearchCondition(recordReq);
        List<XsPointRecord> xsPointRecordList1 = xsPointRecordMapper.getListByScoreQuery(currentScoreQuery);
        xsPointRecordList.addAll(xsPointRecordList1);

        boolean isExistRecord = false;

        if (!CollUtil.isEmpty(xsPointRecordList1)) {
            isExistRecord = true;
        }
        return isExistRecord;
    }

    @Override
    public List<RankSingleScoreDto> single(RecordReq recordReq, XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList, Integer subIndex, Boolean includeEmptyTag) {

        planService.validateJoinPlan(recordReq.getPlanId(), xsUserInfo.getUserId(), recordReq.getSaasClassId());

        //单项榜只取分类点评数据
        xsPointRecordList = xsPointRecordList.stream().filter(t -> ObjectUtil.equals(t.getChannel(), XsPointRecord.CHANNEL_COMMENT)).collect(Collectors.toList());

        if (CollUtil.isEmpty(xsPointRecordList)) {
            return new ArrayList<>();
        }

        List<XsClassStudent> listByClassId = xsClassStudentMapper.getListByClassId(recordReq.getSaasClassId(), xsUserInfo.getSchoolId());

        //查询学生头像信息
        XsAvatarQuery xsAvatarQuery = new XsAvatarQuery();
        xsAvatarQuery.setSaasClassId(recordReq.getSaasClassId());
        xsAvatarQuery.setBusinessType(AvatarBusinessTypeEnum.STUDENT.getType());
        List<XsAvatar> studentAvatarList = xsAvatarMapper.getListByCondition(xsAvatarQuery);
        //重新填充学生头像值
        planGroupDetailDtoConvert.overwriteStudentIcon(listByClassId, studentAvatarList);

        Map<Long, XsClassStudent> studentIdToMap = listByClassId.stream().collect(Collectors.toMap(XsClassStudent::getStudentId, input -> input, (p1, p2) -> p1));

        //数据转换并过滤负分数据
        List<RankSingleScoreDto> rankSingleScoreDtos = rankSingleScoreDtoConvert.doTransList(xsPointRecordList, studentIdToMap, studentAvatarList, includeEmptyTag);

        //分类排序 与上榜学生排序
        rankSingleScoreDtoConvert.singleRangSort(rankSingleScoreDtos, subIndex);

        return rankSingleScoreDtos;
    }

    @Override
    public List<RangeLevelRespDto> hierarchy(RecordReq recordReq, XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList) {

        //积分板验证
        planService.validateJoinPlan(recordReq.getPlanId(), xsUserInfo.getUserId(), recordReq.getSaasClassId());

        PlanReq planReq = new PlanReq();
        planReq.setPlanId(recordReq.getPlanId());
        planReq.setSaasClassId(recordReq.getSaasClassId());
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //非师徒分组直接跳过  没有积分记录直接跳过
        if (!planGroupDetailDto.getPlanType().equals(PlanTypeConst.MASTER_GROUP) || CollUtil.isEmpty(xsPointRecordList)) {
            return new ArrayList<>();
        }

        //生成树
        planService.setStudentTree(planGroupDetailDto);

        //设置树分层范围
        planService.doTreeStudentRangeLevel(planGroupDetailDto);

        //获取全班学生
        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planGroupDetailDto);

        //学生分值
        Map<Long, Double> totalScoresByStudent = new HashMap<>();

        if (!CollUtil.isEmpty(xsPointRecordList)) {
            totalScoresByStudent = xsPointRecordList.stream()
                    .collect(Collectors.groupingBy(XsPointRecord::getStudentId,
                            Collectors.summingDouble(XsPointRecord::getScore)
                    ));
        }
        //设置分层数据
        return callNameService.getLevelStudentList(studentDtoList, planGroupDetailDto, totalScoresByStudent);

    }


    @Override
    public void setAwardDetailReqParam(RecordReq recordReq, XsSmsReportStudent xsSmsReportStudent) {

        XsSmsRule xsSmsRule = JSONObject.parseObject(xsSmsReportStudent.getXsSmsReport().getSmsRuleMetaData(), XsSmsRule.class);

        if (xsSmsRule.getDataRange().equals(DataRangeConst.PLAN)) {
            recordReq.setPlanId(xsSmsRule.getPlanId());
        }
        recordReq.setStartTime(xsSmsReportStudent.getXsSmsReport().getStartTime());
        recordReq.setEndTime(xsSmsReportStudent.getXsSmsReport().getEndTime());
        recordReq.setSaasClassId(xsSmsReportStudent.getSaasClassId());
    }
    private ScoreV2Query groupSearchConditionV2(RecordReq recordReq) throws BusinessException {
        ScoreV2Query scoreQuery = new ScoreV2Query();
        if (recordReq.getTimeType().equals(TimeTypeConst.DEFINE)) {
            if (!(StrUtil.isNotEmpty(recordReq.getStartTime()) && StrUtil.isNotEmpty(recordReq.getEndTime()))) {
                throw new BusinessException("自定义类型时间段必须设置");
            }
            scoreQuery.setStartTime(recordReq.getStartTime());
            scoreQuery.setEndTime(recordReq.getEndTime());
        }

        scoreQuery.setPlanId(recordReq.getPlanId());
        scoreQuery.setSaasClassId(recordReq.getSaasClassId());
        scoreQuery.setTimeType(recordReq.getTimeType());
        //时间段设置
        scoreV2Component.setCurTimeFrame(scoreQuery);
        if (null != recordReq.getIsExceptMinusScore()) {
            scoreQuery.setIsExceptMinusScore(recordReq.getIsExceptMinusScore());
        }


        if (!CollectionUtils.isEmpty(recordReq.getUserIds())) {
            scoreQuery.setUserIds(recordReq.getUserIds());
        }

        if (!CollectionUtils.isEmpty(recordReq.getPlanTagIds())) {
            scoreQuery.setPlanTagIds(recordReq.getPlanTagIds());
        }

        if (!CollectionUtils.isEmpty(recordReq.getExceptChannelIds())) {
            scoreQuery.setExceptChannelIds(recordReq.getExceptChannelIds());
        }
        scoreQuery.setIsIncludeMinus(recordReq.getIsIncludeMinus());
        scoreQuery.setIsIncludeExchange(recordReq.getIsIncludeExchange());


        return scoreQuery;
    }
    public static void main(String[] args) {
        String str = "yXVUkR45PFz0UfpbDB8/ew==";
        log.info(SecureUtil.aes("1234567890123456".getBytes(StandardCharsets.UTF_8)).decryptStr(str));
    }
}

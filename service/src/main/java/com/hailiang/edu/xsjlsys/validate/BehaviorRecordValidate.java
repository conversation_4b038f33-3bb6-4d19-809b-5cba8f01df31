package com.hailiang.edu.xsjlsys.validate;

import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.dto.ResultJson;
import com.hailiang.edu.xsjlsys.emuns.ApiCodeEnum;
import com.hailiang.edu.xsjlsys.reqo.BehaviorRecordReq;
import com.hailiang.edu.xsjlsys.reqo.PlanReq;
import net.sf.oval.ConstraintViolation;
import net.sf.oval.Validator;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@SuppressWarnings("all")
public class BehaviorRecordValidate extends BaseValidate {

    public ResultJson recordLog(BehaviorRecordReq behaviorRecordReq) {
        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(behaviorRecordReq, "recordLog");
        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        //其他验证放后面做
        if (!CollectionUtils.isEmpty(behaviorRecordReq.getRecordList())) {

            for (BehaviorRecordReq.RecordLogDto recordLogDto : behaviorRecordReq.getRecordList()) {
                message = validator.validate(recordLogDto, "recordLog");
                if (!message.isEmpty()) {
                    String msg = message.get(0).getMessage();
                    return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
                }

            }
        }

        return ResultJson.success(new JSONObject());
    }
}

package com.hailiang.edu.xsjlsys.component.plan;

import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlsys.consts.GroupTypeConst;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.emuns.RangeLevelEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class TreeStudentComponent {


    private void setChildInitRangeLevel(PlanGroupDetailDto.StudentDto parent) {
        if (parent == null) {
            return;
        }
        if (CollectionUtils.isEmpty(parent.getChildList())) {
            return;
        }
        for (PlanGroupDetailDto.StudentDto studentDto : parent.getChildList()) {
            studentDto.setRangeLevel(parent.getRangeLevel() + 1);
            setChildInitRangeLevel(studentDto);
        }
    }

    private void setRangeLevel(PlanGroupDetailDto.StudentDto parent, Boolean isSmallLeaderChild) {
        if (parent == null) {
            return;
        }
        //如果当前是小组长，则上升一层
        if (parent.getIsSmallGroupLeader()) {
            parent.setRangeLevel(parent.getRangeLevel() - 1);

            isSmallLeaderChild = true;
        }

        if (CollectionUtils.isEmpty(parent.getChildList())) {
            return;
        }
        for (PlanGroupDetailDto.StudentDto studentDto : parent.getChildList()) {

//            if (parent.getIsSmallGroupLeader()) {
//                studentDto.setRangeLevel(studentDto.getRangeLevel() - 1);
//            }

            if (isSmallLeaderChild) {
                studentDto.setRangeLevel(studentDto.getRangeLevel() - 1);
            }

            setRangeLevel(studentDto, isSmallLeaderChild);
        }
    }

    /**
     * 设置自动分组的层级
     *
     * @param treeStudentList 树列表
     * @param groupNum        该方案下几人组
     */
    public void setRangeLevel(List<PlanGroupDetailDto.StudentDto> treeStudentList, Integer groupNum, String groupType) {


        int maxLevel = groupType.equals(GroupTypeConst.AUTO) ? Math.floorDiv(groupNum, 2) : 7;

        if (CollectionUtils.isEmpty(treeStudentList) || maxLevel == 0) {
            return;
        }

        //设置树的初始层级
        for (PlanGroupDetailDto.StudentDto root : treeStudentList) {
            //遍历树 进行相关操作 根目录为第一层
            root.setRangeLevel(RangeLevelEnum.A.getId());
            setChildInitRangeLevel(root);
        }

        //层级递减
        for (PlanGroupDetailDto.StudentDto root : treeStudentList) {
            //根目录是大队长，层级不动
            setRangeLevel(root, false);
        }

        //层级最大值判定
        for (PlanGroupDetailDto.StudentDto root : treeStudentList) {

            int compare = NumberUtil.compare(root.getRangeLevel(), maxLevel);
            if (compare > 0) {
                root.setRangeLevel(maxLevel);
            }

            setCompareMaxRangeLevel(root, maxLevel);

        }

    }

    private void setCompareMaxRangeLevel(PlanGroupDetailDto.StudentDto parent, Integer maxLevel) {

        if (parent == null) {
            return;
        }
        if (CollectionUtils.isEmpty(parent.getChildList())) {
            return;
        }
        for (PlanGroupDetailDto.StudentDto studentDto : parent.getChildList()) {

            int compare = NumberUtil.compare(studentDto.getRangeLevel(), maxLevel);
            if (compare > 0) {
                studentDto.setRangeLevel(maxLevel);
            }

            setCompareMaxRangeLevel(studentDto, maxLevel);
        }
    }


}

package com.hailiang.edu.xsjlsys.component.game;


import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlsys.dto.game.*;
import com.hailiang.edu.xsjlsys.emuns.GameRuleEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 个人奖结果设置帮助类
 */
@Component
@Slf4j
public class PersonalRewardComponent {

    @Resource
    ClassComponent classComponent;

    /**
     * 积分争霸 mvp 奖设置 个人成绩全班第一，允许多人并列
     *
     * @param allStudentList
     * @param mvpRuleDto
     * @return
     */
    public PersonalResultDto getPointFightMvpReward(List<StudentDto> allStudentList, RuleDto mvpRuleDto) {
        PersonalResultDto personalResultDto = new PersonalResultDto();
        personalResultDto.setRuleCode(mvpRuleDto.getRuleCode());
        personalResultDto.setRuleName(mvpRuleDto.getRuleName());
        personalResultDto.setScore(mvpRuleDto.getScore());
        personalResultDto.setIcon(GameRuleEnum.MVP.getIcon());

        List<StudentDto> studentList = new ArrayList<>();
        for (StudentDto studentDto1 : allStudentList) {
            //1代表第一
            if (null != studentDto1.getSortVal()) {
                if (studentDto1.getSortVal().equals(1)) {
                    StudentDto studentDto11 = new StudentDto();
                    studentDto11.setStudentId(studentDto1.getStudentId());
                    studentDto11.setStudentNo(studentDto1.getStudentNo());
                    studentDto11.setStudentName(studentDto1.getStudentName());
                    studentDto11.setScore(studentDto1.getScore());
                    studentDto11.setParentList(studentDto1.getParentList());
                    studentList.add(studentDto11);
                }
            }
        }

        personalResultDto.setStudentList(studentList);

//        if(!CollectionUtils.isEmpty(studentList)){
//            return personalResultDto;
//        }

        return personalResultDto;
    }


    /**
     * 积分争霸  不在五行 奖设置 获得满分时加分；
     *
     * @param allStudentList
     * @param noneRuleDto
     * @param fullMark
     */
    public PersonalResultDto getPointFightNoneReward(List<StudentDto> allStudentList, RuleDto noneRuleDto, BigDecimal fullMark) {
        PersonalResultDto personalResultDto = new PersonalResultDto();
        personalResultDto.setRuleCode(noneRuleDto.getRuleCode());
        personalResultDto.setRuleName(noneRuleDto.getRuleName());
        personalResultDto.setScore(noneRuleDto.getScore());
        personalResultDto.setIcon(GameRuleEnum.NONE.getIcon());

        List<StudentDto> studentList = new ArrayList<>();
        for (StudentDto studentDto1 : allStudentList) {
            if (null != studentDto1.getScore()) {
                if (NumberUtil.compare(fullMark.doubleValue(), studentDto1.getScore().doubleValue()) == 0) {
                    StudentDto studentDto11 = new StudentDto();
                    studentDto11.setStudentId(studentDto1.getStudentId());
                    studentDto11.setStudentNo(studentDto1.getStudentNo());
                    studentDto11.setStudentName(studentDto1.getStudentName());
                    studentDto11.setScore(studentDto1.getScore());
                    studentDto11.setParentList(studentDto1.getParentList());
                    studentList.add(studentDto11);
                }
            }
        }
        personalResultDto.setStudentList(studentList);


        /*if(!CollectionUtils.isEmpty(studentList)){
            return personalResultDto;
        }*/

        return personalResultDto;

    }


    /**
     * 积分争霸 不屈之魂 奖设置 团队在后50%，但个人排名在全班前5；
     *
     * @param allStudentList
     * @param noneRuleDto
     * @param groupDtoList
     * @return
     */
    public PersonalResultDto getPointFightUnyieldReward(List<StudentDto> allStudentList, RuleDto noneRuleDto, List<GroupDto> groupDtoList) {
        //团队在后50%，但个人排名在全班前5；
        PersonalResultDto personalResultDto = new PersonalResultDto();
        personalResultDto.setRuleCode(noneRuleDto.getRuleCode());
        personalResultDto.setRuleName(noneRuleDto.getRuleName());
        personalResultDto.setScore(noneRuleDto.getScore());
        personalResultDto.setIcon(GameRuleEnum.UNYIELD.getIcon());

        int lastSortVal = 0;
        Optional<GroupDto> groupDtoOptional = groupDtoList.stream().filter(Objects::nonNull).max(Comparator.comparingInt(GroupDto::getSortVal));
        if (groupDtoOptional.isPresent()) {
            GroupDto groupDto = groupDtoOptional.get();
            lastSortVal = NumberUtil.round(Math.floor(groupDto.getSortVal().doubleValue() / 2), 0).intValue();
        }

        List<StudentDto> studentList = new ArrayList<>();
        for (StudentDto studentDto1 : allStudentList) {
            if (null != studentDto1.getSortVal() && studentDto1.getSortVal() <= 5) {
                //代表学生是前5名
                //判断学生所在团队是否是 排名在后50%
                if (lastSortVal != 0) {
                    for (GroupDto groupDto : groupDtoList) {
                        boolean isExist = groupDto.getStudentList().stream().anyMatch(t -> t.getStudentId().equals(studentDto1.getStudentId()));
                        if (isExist) {
                            //找到对应组 判断团队所属排名是否在后百分之50
                            if (groupDto.getSortVal() > lastSortVal) {
                                //代表满足
                                StudentDto studentDto11 = new StudentDto();
                                studentDto11.setStudentId(studentDto1.getStudentId());
                                studentDto11.setStudentNo(studentDto1.getStudentNo());
                                studentDto11.setStudentName(studentDto1.getStudentName());
                                studentDto11.setScore(studentDto1.getScore());
                                studentDto11.setParentList(studentDto1.getParentList());
                                studentList.add(studentDto11);
                            }
                        }
                    }
                }
            }
        }

        personalResultDto.setStudentList(studentList);

        /*if(!CollectionUtils.isEmpty(studentList)){
            return personalResultDto;
        }*/

        return personalResultDto;

    }


    /**
     * 双轮pk赛 mvp 奖设置 两次测验排名数值相加 最小的人
     *
     * @return
     */
    public PersonalResultDto getTwoWheelMvpReward(TwoWheelStageDto twoWheelStageDto, List<StudentDto> classAllStudentList,
                                                  RuleDto ruleDto) {

        PersonalResultDto personalResultDto = new PersonalResultDto();

        List<StudentDto> studentList = new ArrayList<>();

        personalResultDto.setRuleCode(ruleDto.getRuleCode());
        personalResultDto.setRuleName(ruleDto.getRuleName());
        personalResultDto.setScore(ruleDto.getScore());
        personalResultDto.setIcon(GameRuleEnum.MVP.getIcon());
        personalResultDto.setRank(GameRuleEnum.MVP.getRank());
        personalResultDto.setStudentList(studentList);

        //暂时处理
        List<StudentDto> prepareStudentListDto = twoWheelStageDto.getPrepareResult().getStudentList();
        List<StudentDto> finalStudentListDto = twoWheelStageDto.getFinalResult().getStudentList();
        if (CollectionUtils.isEmpty(prepareStudentListDto) || CollectionUtils.isEmpty(finalStudentListDto)) {
            return personalResultDto;
        }

        // 学生预赛成绩列表 拷贝对象
        List<StudentDto> prepareStudentList = JSONObject.parseArray(JSONObject.toJSONString(prepareStudentListDto), StudentDto.class);
        int prepareSize = prepareStudentList.size();
        //设置预赛成绩的排序值
        classComponent.setStudentScoreSortVal(prepareStudentList);
        Map<String, StudentDto> studentDtoPrepareMap = prepareStudentList.stream().collect(Collectors.toMap(StudentDto::getStudentId, studentDto -> studentDto));


        // 学生决赛成绩列表 拷贝对象
        List<StudentDto> finalStudentList = JSONObject.parseArray(JSONObject.toJSONString(finalStudentListDto), StudentDto.class);
        int finalSize = finalStudentList.size();
        //设置决赛成绩的排序值
        classComponent.setStudentScoreSortVal(finalStudentList);
        Map<String, StudentDto> studentDtoFinalMap = finalStudentList.stream().collect(Collectors.toMap(StudentDto::getStudentId, studentDto -> studentDto));

        List<StudentDto> allStudentList = JSONObject.parseArray(JSONObject.toJSONString(classAllStudentList), StudentDto.class);

        for (StudentDto studentDto : allStudentList) {
            if (studentDtoPrepareMap.containsKey(studentDto.getStudentId())) {
                //代表有上传预赛成绩
                studentDto.setSortVal(studentDtoPrepareMap.get(studentDto.getStudentId()).getSortVal());
            } else {
                studentDto.setSortVal(prepareSize + 1);
            }

            if (studentDtoFinalMap.containsKey(studentDto.getStudentId())) {
                //代表有上传预赛成绩
                int finalSortVal = studentDtoFinalMap.get(studentDto.getStudentId()).getSortVal();
                studentDto.setSortVal(studentDto.getSortVal() + finalSortVal);
            } else {
                studentDto.setSortVal(studentDto.getSortVal() + finalSize + 1);
            }
        }
        //最后排序 获取名单
        log.info("mvp allStudentList:" + JSONObject.toJSONString(allStudentList));


        //按照排名从低到高聚合
        List<Map.Entry<Integer, List<StudentDto>>> list = allStudentList.stream().collect(Collectors.groupingBy(StudentDto::getSortVal)).entrySet()
                .stream().sorted(Comparator.comparingInt(Map.Entry::getKey))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(list)) {
            //获取第一行
            List<StudentDto> studentDtoList = list.get(0).getValue();
            for (StudentDto studentDto : studentDtoList) {
                StudentDto studentDto11 = new StudentDto();
                studentDto11.setStudentId(studentDto.getStudentId());
                studentDto11.setStudentNo(studentDto.getStudentNo());
                studentDto11.setStudentName(studentDto.getStudentName());
                studentDto11.setParentList(studentDto.getParentList());
                studentList.add(studentDto11);
            }

            personalResultDto.setStudentList(studentList);
        }


        return personalResultDto;

    }


}

package com.hailiang.edu.xsjlsys.component;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.component.game.ClassComponent;
import com.hailiang.edu.xsjlsys.component.game.GroupComponent;
import com.hailiang.edu.xsjlsys.component.game.GroupRewardComponent;
import com.hailiang.edu.xsjlsys.component.game.PersonalRewardComponent;
import com.hailiang.edu.xsjlsys.consts.DeletedConst;
import com.hailiang.edu.xsjlsys.consts.GameConst;
import com.hailiang.edu.xsjlsys.consts.SysConst;
import com.hailiang.edu.xsjlsys.convert.point.PointRecordConvert;
import com.hailiang.edu.xsjlsys.dal.dao.XsClassStudentMapper;
import com.hailiang.edu.xsjlsys.dal.dao.XsPointRecordMapper;
import com.hailiang.edu.xsjlsys.dal.entity.XsClassStudent;
import com.hailiang.edu.xsjlsys.dal.entity.XsGameRecord;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.game.*;
import com.hailiang.edu.xsjlsys.dto.sys.SysIconDto;
import com.hailiang.edu.xsjlsys.emuns.GameRuleEnum;
import com.hailiang.edu.xsjlsys.query.point.PointRecordQuery;
import com.hailiang.edu.xsjlsys.reqo.GameReq;
import com.hailiang.edu.xsjlsys.service.ClassManageInfoService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class GameComponent {

    @Resource
    PersonalRewardComponent personalRewardComponent;
    @Resource
    ClassComponent classComponent;
    @Resource
    GroupComponent groupComponent;
    @Resource
    GroupRewardComponent groupRewardComponent;
    @Resource
    XsPointRecordMapper xsPointRecordMapper;
    @Resource
    ClassManageInfoService classManageInfoService;
    @Resource
    XsClassStudentMapper xsClassStudentMapper;
    @Resource
    PointRecordConvert pointRecordConvert;


    /**
     * 积分争霸赛 揭榜
     *
     * @param fightStageDto
     * @param planMeteDataDto
     * @param ruleMetaDataDto
     */
    public FinalResultDto doPointFightResult(FightStageDto fightStageDto, PlanMeteDataDto planMeteDataDto, RuleMetaDataDto ruleMetaDataDto) {
        // 1、小组排名：根据测验的小组平均分排名，平均分相同时，比较平均排名，排名值低的获胜；平均排名也相同则并列名次，示例：1、1、3、4…
        // 2、个人奖：仅颁发规则中勾选了的个人奖
        // 1）MVP：个人成绩全班第一，允许多人并列；
        // 2）不屈之魂：团队在后50%，但个人排名在全班前5；
        // 3）不在五行：获得满分时加分；
        // 颁奖时，若有获奖学生已不在本班，榜单照样展示；
        //积分争霸赛第一阶段对象
        FinalResultDto finalResultDto = fightStageDto.getFinalResult();
        //学生成绩列表
        List<StudentDto> finalStudentDtoList = finalResultDto.getStudentList();
        //获取班级下所有学生
        List<StudentDto> allStudentList = classComponent.getClassAllStudentList(planMeteDataDto);

        //获取全班平均分并设置学生分值
        double classPrepareAvgScore = classComponent.getClassAvgScoreWithSetScore(allStudentList, finalStudentDtoList);

        //设置班级小组成员的排名
        classComponent.setStudentScoreSortVal(allStudentList);

        List<GroupDto> groupDtoList = planMeteDataDto.getGroupList();
        //设置小组 平均分值
        for (GroupDto groupDto : groupDtoList) {
            groupComponent.setGroupPointFightAvgScore(groupDto);
        }
        //设置小组排名
        groupDtoList = groupComponent.setGroupPointFightSortVal(groupDtoList, allStudentList);
        //设置小组奖励模式
        //1.设置团队奖
        groupRewardComponent.setGroupPointFightReward(groupDtoList, ruleMetaDataDto.getGroupRuleList());
        finalResultDto.setGroupList(groupDtoList);

        //2.设置个人奖
        List<PersonalResultDto> personalResultDtoList = getPersonalPointFightReward(allStudentList, finalResultDto.getFullMark()
                , groupDtoList, ruleMetaDataDto.getPersonalRuleList());

        finalResultDto.setPersonalResultList(personalResultDtoList);


        return finalResultDto;
    }


    /**
     * 获取积分争霸个人奖励
     *
     * @param allStudentList
     * @param fullMark
     * @param groupDtoList
     * @param personalRuleList
     * @return
     */
    private List<PersonalResultDto> getPersonalPointFightReward(List<StudentDto> allStudentList, BigDecimal fullMark
            , List<GroupDto> groupDtoList, List<RuleDto> personalRuleList) {

        List<PersonalResultDto> personalResultDtoList = new ArrayList<>();

        for (RuleDto ruleDto : personalRuleList) {
            //设置mvp奖
            if (GameRuleEnum.MVP.getCode().equals(ruleDto.getRuleCode())) {
                if (ruleDto.getIsUsed()) {
                    PersonalResultDto personalResultDto = personalRewardComponent.getPointFightMvpReward(allStudentList, ruleDto);
                    if (null != personalResultDto) {
                        personalResultDtoList.add(personalResultDto);
                    }
                }
            }

            //不屈之魂：团队在后50%，但个人排名在全班前5；
            if (GameRuleEnum.UNYIELD.getCode().equals(ruleDto.getRuleCode())) {
                if (ruleDto.getIsUsed()) {
                    PersonalResultDto personalResultDto = personalRewardComponent.getPointFightUnyieldReward(allStudentList, ruleDto, groupDtoList);
                    if (null != personalResultDto) {
                        personalResultDtoList.add(personalResultDto);
                    }
                }
            }

            //设置不在五行奖
            if (GameRuleEnum.NONE.getCode().equals(ruleDto.getRuleCode())) {
                if (ruleDto.getIsUsed()) {

                    PersonalResultDto personalResultDto = personalRewardComponent.getPointFightNoneReward(allStudentList, ruleDto, fullMark);

                    if (null != personalResultDto) {
                        personalResultDtoList.add(personalResultDto);
                    }
                }
            }

        }
        return personalResultDtoList;
    }


    /**
     * 双轮大冒险预赛揭榜
     *
     * @param twoWheelStageDto
     */
    public TwoWheelStageDto doPrepareResult(TwoWheelStageDto twoWheelStageDto, List<StudentDto> allStudentList) {

        // 1、小组平均分=组员成绩之和/组内考试人数（组员取快照，没有上传成绩视为未参试）；小组均未参加考试时，平均分视为0；
        // 2、全班平均分=全班成绩之和/班级参加考试人数；
        // 3、结果判定：
        // 1）2组平均分一高一低：则高的获胜，进入上位区；
        // 2）2组平均分相同，则比较平均排名（小组成员排名之和/小组参试人数），排名值更低的胜出；若平均排名相同，则和全班平均分比，大于或等于全班平均分两队均进入上位区；
        List<MatchDto> matchDtoList = JSONObject.parseArray(JSONObject.toJSONString(twoWheelStageDto.getMatchList()), MatchDto.class);
        //预赛第二阶段对象
        PrepareResultDto prepareResultDto = twoWheelStageDto.getPrepareResult();
        //预赛学生成绩列表
        List<StudentDto> prepareStudentDtoList = prepareResultDto.getStudentList();

        //获取全班平均分
        double classPrepareAvgScore = classComponent.getClassAvgScoreWithSetScore(allStudentList, prepareStudentDtoList);

        //设置班级小组成员的排名
        classComponent.setStudentScoreSortVal(allStudentList);

        //设置匹配小组 学生分值
        for (MatchDto matchDto : matchDtoList) {
            groupComponent.setGroupStudentScore(matchDto.getLeft(), prepareStudentDtoList);
            groupComponent.setGroupStudentScore(matchDto.getRight(), prepareStudentDtoList);
        }
        //计算匹配小组的平均分
        for (MatchDto matchDto : matchDtoList) {
            groupComponent.setGroupPrepareAvgScore(matchDto.getLeft());
            groupComponent.setGroupPrepareAvgScore(matchDto.getRight());
        }

        //匹配小组 上下位区划分
        for (MatchDto matchDto : matchDtoList) {
            GroupDto leftGroup = matchDto.getLeft();
            GroupDto rightGroup = matchDto.getRight();

            int compare = NumberUtil.compare(leftGroup.getPrepareAvgScore().doubleValue(), rightGroup.getPrepareAvgScore().doubleValue());
            if (compare > 0) {
                //左边组大于右边组 则左为上位区,右为下位区
                leftGroup.setLocation(GameConst.LOCATION_UP);
                rightGroup.setLocation(GameConst.LOCATION_DOWN);
            }
            if (compare == 0) {
                //相等 2组平均分相同，则比较平均排名（小组成员排名之和/小组参试人数），排名值更低的胜出；若平均排名相同，则和全班平均分比，大于或等于全班平均分两队均进入上位区；
                compareAvgScoreEqual(leftGroup, rightGroup, classPrepareAvgScore, allStudentList);
            }
            if (compare < 0) {
                leftGroup.setLocation(GameConst.LOCATION_DOWN);
                rightGroup.setLocation(GameConst.LOCATION_UP);
            }
        }

        prepareResultDto.setMatchList(matchDtoList);

        //设置上下位区
        List<GroupDto> upList = new ArrayList<>();
        List<GroupDto> downList = new ArrayList<>();
        for (MatchDto matchDto : matchDtoList) {
            GroupDto leftGroup = matchDto.getLeft();
            GroupDto rightGroup = matchDto.getRight();
            //过滤全班
            if (leftGroup.getGroupId() > 0) {
                if (leftGroup.getLocation().equals(GameConst.LOCATION_UP)) {
                    upList.add(leftGroup);
                } else {
                    downList.add(leftGroup);
                }
            }

            if (rightGroup.getGroupId() > 0) {
                if (rightGroup.getLocation().equals(GameConst.LOCATION_UP)) {
                    upList.add(rightGroup);
                } else {
                    downList.add(rightGroup);
                }
            }
        }

        prepareResultDto.setUpList(upList);
        prepareResultDto.setDownList(downList);

        //设置小阶段初始值
        prepareResultDto.setSmallStage(1);
        return twoWheelStageDto;
    }

    /**
     * 组平均分相同，则比较平均排名（小组成员排名之和/小组参试人数），排名值更低的胜出；若平均排名相同，则和全班平均分比，大于或等于全班平均分两队均进入上位区；
     *
     * @param leftGroup
     * @param rightGroup
     * @param classAvgScore
     */
    private void compareAvgScoreEqual(GroupDto leftGroup, GroupDto rightGroup, Double classAvgScore, List<StudentDto> allStudentList) {


        double leftGroupAvgSortVal = groupComponent.getAvgSortVal(leftGroup, allStudentList);
        double rightGroupAvgSortVal = groupComponent.getAvgSortVal(rightGroup, allStudentList);

        if (leftGroupAvgSortVal == 0) {
            //代表该组未传成绩 设置一个无穷值
            leftGroupAvgSortVal = 9999;
        }

        if (rightGroupAvgSortVal == 0) {
            //代表该组未传成绩 设置一个无穷值
            rightGroupAvgSortVal = 9999;
        }


        int compare = NumberUtil.compare(leftGroupAvgSortVal, rightGroupAvgSortVal);
        if (compare < 0) {
            //左边组小于于右边组 则左为上位区,右为下位区
            leftGroup.setLocation(GameConst.LOCATION_UP);
            rightGroup.setLocation(GameConst.LOCATION_DOWN);
        }
        if (compare == 0) {
            //若平均排名相同，则和全班平均分比，大于或等于全班平均分两队均进入上位区；
            if (NumberUtil.compare(leftGroup.getPrepareAvgScore().doubleValue(), classAvgScore) >= 0) {
                leftGroup.setLocation(GameConst.LOCATION_UP);
                rightGroup.setLocation(GameConst.LOCATION_UP);
            } else {
                leftGroup.setLocation(GameConst.LOCATION_DOWN);
                rightGroup.setLocation(GameConst.LOCATION_DOWN);
            }
        }
        if (compare > 0) {
            leftGroup.setLocation(GameConst.LOCATION_DOWN);
            rightGroup.setLocation(GameConst.LOCATION_UP);
        }
    }

    /**
     * 积分争霸赛成绩结果数据生成
     *
     * @param gameReq
     * @param planMeteDataDto
     * @return
     */
    public FinalResultDto genFightFinalScore(GameReq gameReq, PlanMeteDataDto planMeteDataDto) throws BusinessException {

        //全班学生
        List<StudentDto> allStudentList = classComponent.getClassAllStudentList(planMeteDataDto);

        FinalResultDto finalResultDto = new FinalResultDto();
        finalResultDto.setFullMark(NumberUtil.round(gameReq.getFullMark(), 1));

        List<StudentDto> studentList = new ArrayList<>();
        for (GameReq.StudentDto studentDto : gameReq.getStudentList()) {
            if (!StringUtils.isEmpty(studentDto.getStudentNo())) {
                //匹配学号以及学生姓名
                Optional<StudentDto> studentDtoOptional = allStudentList
                        .stream().filter(t -> t.getStudentNo().equals(studentDto.getStudentNo()) && t.getStudentName().equals(studentDto.getStudentName()))
                        .findFirst();
                if (studentDtoOptional.isPresent()) {
                    StudentDto searchObj = studentDtoOptional.get();

                    StudentDto studentDto1 = new StudentDto();
                    studentDto1.setStudentId(searchObj.getStudentId());
                    studentDto1.setStudentNo(searchObj.getStudentNo());
                    studentDto1.setStudentName(searchObj.getStudentName());
                    studentDto1.setParentList(searchObj.getParentList());
                    studentDto1.setScore(NumberUtil.round(studentDto.getScore(), 1));
                    studentList.add(studentDto1);
                }
            } else {
                //匹配姓名
                Optional<StudentDto> studentDtoOptional = allStudentList
                        .stream().filter(t -> t.getStudentName().equals(studentDto.getStudentName()))
                        .findFirst();
                if (studentDtoOptional.isPresent()) {
                    StudentDto searchObj = studentDtoOptional.get();
                    StudentDto studentDto1 = new StudentDto();
                    studentDto1.setStudentId(searchObj.getStudentId());
                    studentDto1.setStudentNo(searchObj.getStudentNo());
                    studentDto1.setStudentName(searchObj.getStudentName());
                    studentDto1.setParentList(searchObj.getParentList());
                    studentDto1.setScore(NumberUtil.round(studentDto.getScore(), 1));
                    studentList.add(studentDto1);
                }
            }
        }
        finalResultDto.setStudentList(studentList);

        finalResultDto.setGroupDisPlayList(setBigIcon(planMeteDataDto.getGroupList()));
        return finalResultDto;
    }

    private List<GroupDto> setBigIcon(List<GroupDto> groupList) throws BusinessException {
        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(groupList)) {
            return groupList;
        }

        //获取系统所有头像列表
        List<SysIconDto> resultList = classManageInfoService.getIconList(SysConst.ICON_TYPE_GROUP);
        Map<String, String> iconMap = new HashMap<>();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(resultList)) {
            iconMap = resultList.stream().collect(Collectors.toMap(SysIconDto::getUrl, SysIconDto::getUrlBig));
        }
        for (GroupDto dto : groupList) {
            if (iconMap.containsKey(dto.getIcon())) {
                dto.setIconBig(iconMap.get(dto.getIcon()));
            }
        }
        return groupList;
    }


    /**
     * 双轮大冒险预赛成绩结果数据生成
     *
     * @param gameReq
     * @param planMeteDataDto
     * @return
     */
    public PrepareResultDto genPrepareScore(GameReq gameReq, PlanMeteDataDto planMeteDataDto) {

        //全班学生
        List<StudentDto> allStudentList = classComponent.getClassAllStudentList(planMeteDataDto);

        PrepareResultDto prepareResultDto = new PrepareResultDto();
        prepareResultDto.setFullMark(NumberUtil.round(gameReq.getFullMark(), 1));

        List<StudentDto> studentList = new ArrayList<>();
        for (GameReq.StudentDto studentDto : gameReq.getStudentList()) {
            if (!StringUtils.isEmpty(studentDto.getStudentNo())) {
                //匹配学号以及学生姓名
                Optional<StudentDto> studentDtoOptional = allStudentList
                        .stream().filter(t -> t.getStudentNo().equals(studentDto.getStudentNo()) && t.getStudentName().equals(studentDto.getStudentName()))
                        .findFirst();
                if (studentDtoOptional.isPresent()) {
                    StudentDto searchObj = studentDtoOptional.get();

                    StudentDto studentDto1 = new StudentDto();
                    studentDto1.setStudentId(searchObj.getStudentId());
                    studentDto1.setStudentNo(searchObj.getStudentNo());
                    studentDto1.setStudentName(searchObj.getStudentName());
                    studentDto1.setParentList(searchObj.getParentList());
                    studentDto1.setScore(NumberUtil.round(studentDto.getScore(), 1));
                    studentList.add(studentDto1);
                }
            } else {
                //匹配姓名
                Optional<StudentDto> studentDtoOptional = allStudentList
                        .stream().filter(t -> t.getStudentName().equals(studentDto.getStudentName()))
                        .findFirst();
                if (studentDtoOptional.isPresent()) {
                    StudentDto searchObj = studentDtoOptional.get();
                    StudentDto studentDto1 = new StudentDto();
                    studentDto1.setStudentId(searchObj.getStudentId());
                    studentDto1.setStudentNo(searchObj.getStudentNo());
                    studentDto1.setStudentName(searchObj.getStudentName());
                    studentDto1.setParentList(searchObj.getParentList());
                    studentDto1.setScore(NumberUtil.round(studentDto.getScore(), 1));
                    studentList.add(studentDto1);
                }
            }
        }
        prepareResultDto.setStudentList(studentList);


        return prepareResultDto;
    }

    /**
     * 双轮大冒险预赛匹配结果数据生成
     *
     * @param planMeteDataDto
     */
    public TwoWheelStageDto genPrepareMatchResult(PlanMeteDataDto planMeteDataDto) {
        TwoWheelStageDto twoWheelStageDto = new TwoWheelStageDto();

        boolean doSort = false;
        List<GroupDto> groupList = JSONObject.parseArray(JSONObject.toJSONString(planMeteDataDto.getGroupList()), GroupDto.class);
        int size = planMeteDataDto.getGroupList().size();
        if (size % 2 != 0) {
            //奇数 还需要添加一个全班的组
            //1.拼装全班的学生模型列表
            List<StudentDto> allStudentList = classComponent.getClassAllStudentList(planMeteDataDto);

            GroupDto groupDto = new GroupDto();
            groupDto.setGroupId(0L);
            groupDto.setGroupName("全班");
            //全班图标
            groupDto.setIcon("https://static-inspire-stu.hailiangedu.com/system/group/img/allclass.png");
            groupDto.setStudentList(allStudentList);
            groupList.add(groupDto);

            doSort = true;
        }

        //进行组与组匹配逻辑
        List<MatchDto> matchDtoList = doPrepareMatch(groupList);

        if (doSort) {
            //包含全班的匹配对象放到列表最底部
            int len = matchDtoList.size();
            MatchDto containAllClass = new MatchDto();
            for (int i = 0; i < len; i++) {
                MatchDto matchDto = matchDtoList.get(i);
                if (matchDto.getLeft().getGroupId().equals(0L) || matchDto.getRight().getGroupId().equals(0L)) {
                    containAllClass = matchDto;
                    //将全班放置右边
                    if (containAllClass.getLeft().getGroupId().equals(0L)) {
                        //左边组为全班，2个数据互换
                        GroupDto mid = containAllClass.getLeft();
                        containAllClass.setLeft(containAllClass.getRight());
                        containAllClass.setRight(mid);
                    }

                    matchDtoList.remove(i);
                    break;
                }
            }
            matchDtoList.add(containAllClass);

        }


        twoWheelStageDto.setMatchList(matchDtoList);

        return twoWheelStageDto;
    }


    private List<MatchDto> doPrepareMatch(List<GroupDto> groupList) {

        if (!CollectionUtils.isEmpty(groupList)) {
            int length = (int) Math.floor(NumberUtil.div(groupList.size(), 2));

            List<MatchDto> matchDtoList = new ArrayList<>();
            for (int i = 0; i < length; i++) {
                Random random = new Random();
                int index = random.nextInt(groupList.size());
                GroupDto groupDto = groupList.get(index);
                groupList.remove(index);
                MatchDto matchDto = new MatchDto();
                matchDto.setLeft(groupDto);

                index = random.nextInt(groupList.size());
                GroupDto groupDto2 = groupList.get(index);
                groupList.remove(index);
                matchDto.setRight(groupDto2);
                matchDtoList.add(matchDto);
            }
            return matchDtoList;
        }
        return new ArrayList<>();
    }


    /**
     * 双轮大冒险决赛成绩结果数据生成
     *
     * @param gameReq
     * @param planMeteDataDto
     * @return
     */
    public FinalResultDto genFinalScore(GameReq gameReq, PlanMeteDataDto planMeteDataDto) {

        //全班学生
        List<StudentDto> allStudentList = classComponent.getClassAllStudentList(planMeteDataDto);

        FinalResultDto finalResultDto = new FinalResultDto();
        finalResultDto.setFullMark(NumberUtil.round(gameReq.getFullMark(), 1));

        List<StudentDto> studentList = new ArrayList<>();
        for (GameReq.StudentDto studentDto : gameReq.getStudentList()) {
            if (!StringUtils.isEmpty(studentDto.getStudentNo())) {

                //匹配学号以及学生姓名
                Optional<StudentDto> studentDtoOptional = allStudentList
                        .stream().filter(t -> t.getStudentNo().equals(studentDto.getStudentNo()) && t.getStudentName().equals(studentDto.getStudentName()))
                        .findFirst();
                if (studentDtoOptional.isPresent()) {
                    StudentDto searchObj = studentDtoOptional.get();

                    StudentDto studentDto1 = new StudentDto();
                    studentDto1.setStudentId(searchObj.getStudentId());
                    studentDto1.setStudentNo(searchObj.getStudentNo());
                    studentDto1.setStudentName(searchObj.getStudentName());
                    studentDto1.setParentList(searchObj.getParentList());
                    studentDto1.setScore(NumberUtil.round(studentDto.getScore(), 1));
                    studentList.add(studentDto1);
                }
            } else {
                //匹配姓名
                Optional<StudentDto> studentDtoOptional = allStudentList
                        .stream().filter(t -> t.getStudentName().equals(studentDto.getStudentName()))
                        .findFirst();
                if (studentDtoOptional.isPresent()) {
                    StudentDto searchObj = studentDtoOptional.get();
                    StudentDto studentDto1 = new StudentDto();
                    studentDto1.setStudentId(searchObj.getStudentId());
                    studentDto1.setStudentNo(searchObj.getStudentNo());
                    studentDto1.setStudentName(searchObj.getStudentName());
                    studentDto1.setParentList(searchObj.getParentList());
                    studentDto1.setScore(NumberUtil.round(studentDto.getScore(), 1));
                    studentList.add(studentDto1);
                }
            }
        }
        finalResultDto.setStudentList(studentList);


        return finalResultDto;
    }

    /**
     * 生成第五阶段游戏结果
     * 决赛揭榜规则:
     * 1.团队奖惩：第二次测验只有上位区的小组可以竞争第1名
     * 1）根据第二次测验的小组平均分，先上位区进行排名、再下位区进行排名；同区段小组平均分相同时，比较平均排名，排名值低的获胜；平均排名也相同则并列名次；
     * 比如上位区有4组，下位区3组，则上位区排出前4名，下位区不管平均分多高，都只能获得5、6、7名；
     * 若有并列排名的组，则名次示例：1、1、3、4…
     * 2）小组加分/减分：小组成员按快照计，2次测验均未参加的成员不加分/减分；
     * 小组奖惩不触发师徒制连带加分。
     * <p>
     * 2.个人奖：仅颁发规则中勾选了的个人奖，并且不展示没有获奖人的奖项。
     * 1）FMVP：两次测验均排名第一的同学获得，允许多人并列；
     * 2）MVP：两次测验分数之和全班第一，允许多人并列；与FMVP不同享，优先FMVP；
     * 3）不屈之魂：团队在下位区，但第二次个人成绩在全班前5；
     * 4）不在五行：每获得一次满分加x分；若两次均获得满分，则加2x分；
     * 1-4个人奖由系统自动计算（若有获奖学生已不在本班，榜单照样展示）
     * 5）永无止境、声名显赫、恨铁不成钢：由教师选择人员，支持姓名模糊搜索&多选；
     * 选择范围：快照的学生，排序无要求。
     * 个人奖加分，将触发师徒制连带加分。
     *
     * @param twoWheelStageDto
     * @param xsGameRecord
     */
    public void genTwoWheelFinalResult(TwoWheelStageDto twoWheelStageDto, XsGameRecord xsGameRecord) {


        //获取分组快照
        String planMataData = xsGameRecord.getPlanMetaData();
        PlanMeteDataDto planMeteDataDto = JSONObject.parseObject(planMataData, PlanMeteDataDto.class);
        //获取游戏规则
        String ruleMetaData = xsGameRecord.getRuleMetaData();
        RuleMetaDataDto ruleMetaDataDto = JSONObject.parseObject(ruleMetaData, RuleMetaDataDto.class);

        //初始化小组排名
        initGroupAwardData(twoWheelStageDto, ruleMetaDataDto);
        //初始化个人榜数据
        initPersonAwardData(twoWheelStageDto, ruleMetaDataDto, planMeteDataDto);


    }

    public void genStuScoreAndRankMap(HashMap<String, Integer> stuRankMap, List<StudentDto> finalScores) {
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(finalScores)) {
            //获取学生排名
            Map<BigDecimal, List<StudentDto>> collect = finalScores.stream().collect(Collectors.groupingBy(StudentDto::getScore));
            List<BigDecimal> keyList = collect.keySet().stream().collect(Collectors.toList());
            keyList = keyList.stream().sorted(Comparator.comparing(BigDecimal::doubleValue).reversed()).collect(Collectors.toList());
            int rankCount = 0;
            for (int i = 0; i < keyList.size(); i++) {
                int curRank = rankCount + 1;
                for (StudentDto stu : collect.get(keyList.get(i))) {
                    rankCount++;
                    stuRankMap.put(stu.getStudentId(), curRank);
                }
            }


        }
    }

    /**
     * 根据个人规则 生成第五阶段个人奖项
     *
     * @param twoWheelStageDto
     * @param ruleMetaDataDto
     * @param planMeteDataDto
     */
    public void initPersonAwardData(TwoWheelStageDto twoWheelStageDto, RuleMetaDataDto ruleMetaDataDto, PlanMeteDataDto planMeteDataDto) {
        List<RuleDto> ruleDtos = ruleMetaDataDto.getPersonalRuleList();
        List<PersonalResultDto> personalResultList = new ArrayList<>();
        List<PersonalResultDto> teacherDefineList = twoWheelStageDto.getFinalResult().getTeacherDefineList();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(ruleDtos)) {
            //计算班级所有学生map
            List<StudentDto> classAllStudentList = classComponent.getClassAllStudentList(planMeteDataDto);
            Map<String, StudentDto> studentDtoMap = classAllStudentList.stream().collect(Collectors.toMap(StudentDto::getStudentId, studentDto -> studentDto));
            twoWheelStageDto.getFinalResult().setIsFmvpFound(false);
            for (RuleDto rule : ruleDtos) {
                if (rule.getIsUsed()) {
                    //设置个人奖项
                    doProcessRule(personalResultList, rule, twoWheelStageDto, studentDtoMap, classAllStudentList);
                }
            }
            if (twoWheelStageDto.getFinalResult().getIsFmvpFound()) {
                //有fmvp 过滤掉  mvp
                //personalResultList = personalResultList.stream().filter(dto -> !dto.getRuleCode().equals(GameRuleEnum.MVP.getCode())).collect(Collectors.toList());
                for (PersonalResultDto personal : personalResultList) {
                    //如果找到了 fmvp  那么mvp的绛奖项不算
                    if (personal.getRuleCode().equals(GameRuleEnum.MVP.getCode())) {
                        personal.setStudentList(null);
                    }
                }
            }
            //对奖项进行排序
            personalResultList = personalResultList.stream().sorted(Comparator.comparing(PersonalResultDto::getRank)).collect(Collectors.toList());
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(teacherDefineList)) {
                teacherDefineList = teacherDefineList.stream().sorted(Comparator.comparing(PersonalResultDto::getRank)).collect(Collectors.toList());
            }
        }
        twoWheelStageDto.getFinalResult().setPersonalResultList(personalResultList);
        //设置需要由老师指定的奖项
        twoWheelStageDto.getFinalResult().setTeacherDefineList(teacherDefineList);
    }

    /**
     * 根据个人榜规则生成个人奖项
     *
     * @param personalResultList
     * @param rule
     * @param twoWheelStageDto
     * @param studentDtoMap
     */
    public void doProcessRule(List<PersonalResultDto> personalResultList, RuleDto rule, TwoWheelStageDto twoWheelStageDto,
                              Map<String, StudentDto> studentDtoMap, List<StudentDto> classAllStudentList) {
        //首先获取所有学生决赛成绩排名
        List<StudentDto> finalScores = twoWheelStageDto.getFinalResult().getStudentList();
        //生成学生成绩映射、
        Map<String, BigDecimal> scoreMap = new HashMap<>();
        //生成所有学生的成绩排名
        HashMap<String, Integer> stuRankMap = new HashMap<>();
        //计算决赛成绩和排名map
        genStuScoreAndRankMap(stuRankMap, finalScores);
        scoreMap = finalScores.stream().collect(Collectors.toMap(StudentDto::getStudentId, StudentDto::getScore));
        //获取初赛成绩
        List<StudentDto> firstStuList = twoWheelStageDto.getPrepareResult().getStudentList();
        //第一次测验的 分数和 排名获取
        Map<String, BigDecimal> firstScore = new HashMap<>();
        HashMap<String, Integer> firstRankMap = new HashMap<>();
        genStuScoreAndRankMap(firstRankMap, firstStuList);
        firstScore = firstStuList.stream().collect(Collectors.toMap(StudentDto::getStudentId, StudentDto::getScore));

        //根据规则设置个人奖项
        setPersonAwardByRule(twoWheelStageDto, rule, stuRankMap, scoreMap, firstScore, firstRankMap,
                studentDtoMap, personalResultList, classAllStudentList);

    }

    /**
     * 根据规则 设置个人奖项     无人得奖的   也需要展示
     *
     * @param twoWheelStageDto
     * @param rule
     * @param stuRankMap
     * @param scoreMap
     * @param firstScore
     * @param firstRankMap
     * @param studentDtoMap
     * @param personalResultList
     */
    public void setPersonAwardByRule(TwoWheelStageDto twoWheelStageDto, RuleDto rule, HashMap<String, Integer> stuRankMap,
                                     Map<String, BigDecimal> scoreMap, Map<String, BigDecimal> firstScore,
                                     HashMap<String, Integer> firstRankMap, Map<String, StudentDto> studentDtoMap,
                                     List<PersonalResultDto> personalResultList, List<StudentDto> classAllStudentList) {
        //是否找到了fmvp
        Boolean isFmvpFound = twoWheelStageDto.getFinalResult().getIsFmvpFound();

        if (rule.getRuleCode().equals(GameRuleEnum.FMVP.getCode())) {
            //计算fmvp奖项  两次都得到第一名的学生
            //计算fmvp
            twoWheelComputeFmvp(stuRankMap, firstRankMap, twoWheelStageDto, personalResultList, studentDtoMap, rule);
        } else if (rule.getRuleCode().equals(GameRuleEnum.MVP.getCode())) {

            //mvp奖项计算 两次测验排名数值相加 最小的人
            personalResultList.add(personalRewardComponent.getTwoWheelMvpReward(twoWheelStageDto, classAllStudentList, rule));


        } else if (rule.getRuleCode().equals(GameRuleEnum.UNYIELD.getCode())) {
            //计算不屈之魂奖项
            //获取下位区组列表
            twoWheelComputeUnyieLd(twoWheelStageDto, stuRankMap, rule, personalResultList);
        } else if (rule.getRuleCode().equals(GameRuleEnum.NONE.getCode())) {
            //不在五行 奖项计算
            //记录得到满分的学生
            twoWheelComputeNone(scoreMap, twoWheelStageDto, firstScore,
                    studentDtoMap, rule, personalResultList);
        } /*else {
            PersonalResultDto dto = new PersonalResultDto();
            dto.setRuleCode(rule.getRuleCode());
            dto.setRuleName(rule.getRuleName());
            dto.setScore(rule.getScore());
            dto.setIcon(GameRuleEnum.getIconByCode(rule.getRuleCode()));
            dto.setRank(GameRuleEnum.getRankByCode(rule.getRuleCode()));
            teacherDefineList.add(dto);
        }*/
    }

    /**
     * 双轮大冒险计算不在五行个人奖项
     *
     * @param scoreMap
     * @param twoWheelStageDto
     * @param firstScore
     * @param studentDtoMap
     * @param rule
     * @param personalResultList
     */
    public void twoWheelComputeNone(Map<String, BigDecimal> scoreMap, TwoWheelStageDto twoWheelStageDto, Map<String, BigDecimal> firstScore, Map<String, StudentDto> studentDtoMap, RuleDto rule, List<PersonalResultDto> personalResultList) {
        Map<String, Integer> wardMap = new HashMap<>();
        //判断成绩
        if (null != scoreMap && !scoreMap.isEmpty()) {
            //获取第二轮满凤分值
            BigDecimal fullMark = twoWheelStageDto.getFinalResult().getFullMark();
            scoreMap.entrySet().forEach(entry -> {
                if (entry.getValue().compareTo(fullMark) == 0) {
                    if (wardMap.containsKey(entry.getKey())) {
                        Integer count = wardMap.get(entry.getKey());
                        wardMap.put(entry.getKey(), count + 1);
                    } else {
                        wardMap.put(entry.getKey(), 1);
                    }
                }
            });
        }
        if (null != firstScore && !firstScore.isEmpty()) {
            //获取第一轮满分分值
            BigDecimal fullMark = twoWheelStageDto.getPrepareResult().getFullMark();
            firstScore.entrySet().forEach(entry -> {
                if (entry.getValue().compareTo(fullMark) == 0) {
                    if (wardMap.containsKey(entry.getKey())) {
                        Integer count = wardMap.get(entry.getKey());
                        wardMap.put(entry.getKey(), count + 1);
                    } else {
                        wardMap.put(entry.getKey(), 1);
                    }

                }
            });
        }
        PersonalResultDto dto = new PersonalResultDto();
        dto.setRuleCode(rule.getRuleCode());
        dto.setRuleName(rule.getRuleName());
        dto.setScore(rule.getScore());
        dto.setIcon(GameRuleEnum.NONE.getIcon());
        dto.setRank(GameRuleEnum.NONE.getRank());
        if (null != wardMap && !wardMap.isEmpty()) {
            List<StudentDto> awardList = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : wardMap.entrySet()) {
                StudentDto student = studentDtoMap.get(entry.getKey());
                student.setScore(NumberUtil.mul(rule.getScore(), entry.getValue().intValue()));
                awardList.add(student);
            }
            dto.setStudentList(awardList);
        }
        personalResultList.add(dto);
    }

    /**
     * 双轮大冒险计算不去之魂奖项
     *
     * @param twoWheelStageDto
     * @param stuRankMap
     * @param rule
     * @param personalResultList
     */
    public void twoWheelComputeUnyieLd(TwoWheelStageDto twoWheelStageDto, HashMap<String, Integer> stuRankMap, RuleDto rule, List<PersonalResultDto> personalResultList) {
        PersonalResultDto dto = new PersonalResultDto();
        dto.setRuleCode(rule.getRuleCode());
        dto.setRuleName(rule.getRuleName());
        dto.setScore(rule.getScore());
        dto.setIcon(GameRuleEnum.UNYIELD.getIcon());
        dto.setRank(GameRuleEnum.UNYIELD.getRank());
        if (!CollectionUtils.isEmpty(twoWheelStageDto.getPrepareResult().getDownList())) {
            List<GroupDto> downList = twoWheelStageDto.getPrepareResult().getDownList();
            List<StudentDto> awardList = new ArrayList<>();
            for (GroupDto groupDto : downList) {
                for (StudentDto studentDto : groupDto.getStudentList()) {
                    //根据该学生决赛排排名判断是否的该奖项
                    Integer rank = stuRankMap.get(studentDto.getStudentId());
                    if (null != rank && rank.intValue() <= 5) {
                        awardList.add(studentDto);
                    }
                }
            }
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(awardList)) {
                //获奖人数部位空  添加奖项
                dto.setStudentList(awardList);

            }
        }
        personalResultList.add(dto);
    }


    /**
     * 计算双轮大冒险FMVP
     *
     * @param stuRankMap
     * @param firstRankMap
     * @param twoWheelStageDto
     * @param personalResultList
     * @param studentDtoMap
     * @param rule
     */
    public void twoWheelComputeFmvp(HashMap<String, Integer> stuRankMap, HashMap<String, Integer> firstRankMap, TwoWheelStageDto twoWheelStageDto, List<PersonalResultDto> personalResultList, Map<String, StudentDto> studentDtoMap, RuleDto rule) {
        if (null == stuRankMap || stuRankMap.isEmpty() || null == firstRankMap || firstRankMap.isEmpty()) {
            //有一轮排名为空  直接返回
            return;
        }
        List<String> prepareFirst = new ArrayList<>();
        List<String> finalFirst = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : firstRankMap.entrySet()) {
            if (entry.getValue().intValue() == 1) {
                prepareFirst.add(entry.getKey());
            }
        }
        //找到决赛第一名
        for (Map.Entry<String, Integer> entry : stuRankMap.entrySet()) {
            if (entry.getValue().intValue() == 1) {
                finalFirst.add(entry.getKey());
            }
        }
        //寻找初赛预赛 的第一名交集
        Set<String> result = prepareFirst.stream().distinct().filter(finalFirst::contains).collect(Collectors.toSet());

        PersonalResultDto fmvp = new PersonalResultDto();
        fmvp.setRuleCode(rule.getRuleCode());
        fmvp.setRuleName(rule.getRuleName());
        fmvp.setScore(rule.getScore());
        fmvp.setIcon(GameRuleEnum.FMVP.getIcon());
        fmvp.setRank(GameRuleEnum.FMVP.getRank());
        //判断是否找到了fmvp
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(result)) {
            //找到了 fmvp
            twoWheelStageDto.getFinalResult().setIsFmvpFound(true);
            List<StudentDto> studentDtoList = new ArrayList<>();
            for (String stu : result) {
                studentDtoList.add(studentDtoMap.get(stu));
            }
            //设置获奖学生
            fmvp.setStudentList(studentDtoList);
        }
        personalResultList.add(fmvp);
    }

    /**
     * 根据团队规则生成第五阶段团队排名
     *
     * @param twoWheelStageDto
     * @param ruleMetaDataDto
     */
    public void initGroupAwardData(TwoWheelStageDto twoWheelStageDto, RuleMetaDataDto ruleMetaDataDto) {
        //设置小组奖项
        //首先获取所有学生决赛成绩排名
        List<StudentDto> finalScores = twoWheelStageDto.getFinalResult().getStudentList();
        //生成学生成绩映射、
        Map<String, BigDecimal> scoreMap = new HashMap<>();
        //生成所有学生的成绩排名
        HashMap<String, Integer> stuRankMap = new HashMap<>();
        //计算决赛成绩和排名map
        genStuScoreAndRankMap(stuRankMap, finalScores);
        scoreMap = finalScores.stream().collect(Collectors.toMap(StudentDto::getStudentId, StudentDto::getScore));
        //获得上位区列表
        List<GroupDto> upList = twoWheelStageDto.getPrepareResult().getUpList();
        //获得下位区列表
        List<GroupDto> downList = twoWheelStageDto.getPrepareResult().getDownList();
        //初始化决赛平均分 小组平均排名
        initGrouFinalAvgScoreAndAvgRank(upList, downList, scoreMap, stuRankMap);
        //设置小组排行榜
        setFinalGroupRank(twoWheelStageDto, upList, downList, ruleMetaDataDto);
    }

    /**
     * 计算第五阶段小组具体排名
     *
     * @param twoWheelStageDto
     * @param upList
     * @param downList
     * @param ruleMetaDataDto
     */
    public void setFinalGroupRank(TwoWheelStageDto twoWheelStageDto, List<GroupDto> upList,
                                  List<GroupDto> downList, RuleMetaDataDto ruleMetaDataDto) {
        //小组排名规则获取
        Integer groupCount = 0;
        SortValue sortValue = new SortValue();
        sortValue.setGroupCount(0);
        List<GroupDto> groupList = new LinkedList<>();
        sortFinalGroup(sortValue, upList);
        sortFinalGroup(sortValue, downList);
        groupList.addAll(upList);
        groupList.addAll(downList);
        //设置每个小组应该加的分数
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(groupList)) {
            List<Map.Entry<Integer, List<GroupDto>>> groupScore = groupList.stream().collect(Collectors.groupingBy(GroupDto::getSortVal)).entrySet()
                    .stream().sorted(Comparator.comparingInt(Map.Entry::getKey)).collect(Collectors.toList());
            //转换规则成为map
            Map<Integer, BigDecimal> ruleScoreMap = new HashMap<>();
            List<RuleDto> groupRuleList = ruleMetaDataDto.getGroupRuleList();
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(groupRuleList)) {
                ruleScoreMap = groupRuleList.stream().collect(Collectors.toMap(RuleDto::getRank, RuleDto::getScore));
            }
            for (Map.Entry<Integer, List<GroupDto>> entry : groupScore) {
                //根据小组排名值获取应该加的分数
                BigDecimal score = ruleScoreMap.get(entry.getKey());
                for (GroupDto groupDto : entry.getValue()) {
                    groupDto.setAllPlusScore(score);
                }
            }
            groupList.sort(Comparator.comparing(GroupDto::getSortVal));
        }
        //设置小组排行榜
        twoWheelStageDto.getFinalResult().setGroupList(groupList);
    }


    /**
     * 设置
     *
     * @param groupDtos
     */
    public void sortFinalGroup(SortValue sortValue, List<GroupDto> groupDtos) {
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(groupDtos)) {
            //对小组平均分先集合在排序
            List<Map.Entry<BigDecimal, List<GroupDto>>> groupMapList = groupDtos.stream().collect(Collectors.groupingBy(GroupDto::getFinalAvgScore)).entrySet()
                    .stream().sorted((s1, s2) -> -NumberUtil.compare(s1.getKey().doubleValue(), s2.getKey().doubleValue())
                    ).collect(Collectors.toList());
            for (Map.Entry<BigDecimal, List<GroupDto>> entry : groupMapList) {
                if (entry.getValue().size() == 1) {
                    sortValue.setGroupCount(sortValue.getGroupCount() + 1);
                    //rankCount++;
                    //平均分没有并列
                    entry.getValue().get(0).setSortVal(sortValue.getGroupCount());
                }
                //处理并列排名的情况
                if (entry.getValue().size() > 1) {
                    List<GroupDto> avgRanList = entry.getValue();
                    List<Map.Entry<BigDecimal, List<GroupDto>>> rankMapList = avgRanList.stream().collect(Collectors.groupingBy(GroupDto::getAvgStuRank)).entrySet()
                            .stream().sorted((s1, s2) -> NumberUtil.compare(s1.getKey().doubleValue(), s2.getKey().doubleValue())
                            ).collect(Collectors.toList());
                    //
                    for (Map.Entry<BigDecimal, List<GroupDto>> ranEntry : rankMapList) {
                        if (ranEntry.getValue().size() == 1) {
                            sortValue.setGroupCount(sortValue.getGroupCount() + 1);
                            //rankCount++;
                            ranEntry.getValue().get(0).setSortVal(sortValue.getGroupCount());
                        }
                        if (ranEntry.getValue().size() > 1) {
                            //平均排名也一样那么这两个组并列
                            int rankCount = sortValue.getGroupCount() + 1;
                            for (GroupDto groupDto : ranEntry.getValue()) {
                                sortValue.setGroupCount(sortValue.getGroupCount() + 1);
                                groupDto.setSortVal(rankCount);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 初始化小组平均排名所需要用到的数据
     *
     * @param upList
     * @param downList
     * @param scoreMap
     * @param stuRankMap
     */
    private void initGrouFinalAvgScoreAndAvgRank(List<GroupDto> upList, List<GroupDto> downList, Map<String, BigDecimal> scoreMap,
                                                 HashMap<String, Integer> stuRankMap) {
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(upList)) {
            //初始化小组列表数据
            doGroupDataInit(upList, scoreMap, stuRankMap);
        }
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(downList)) {
            doGroupDataInit(downList, scoreMap, stuRankMap);
        }
    }

    /**
     * 初始化 小组排名平均风  小组平均排名数据
     *
     * @param upList
     * @param scoreMap
     * @param stuRankMap
     */
    private void doGroupDataInit(List<GroupDto> upList, Map<String, BigDecimal> scoreMap, HashMap<String, Integer> stuRankMap) {
        for (GroupDto groupDto : upList) {
            int rankCount = 0;
            int titalRank = 0;
            int stuCout = 0;
            BigDecimal totalScore = NumberUtil.round(0.0, 1);
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(groupDto.getStudentList())) {
                for (StudentDto stu : groupDto.getStudentList()) {
                    if (null != scoreMap.get(stu.getStudentId())) {
                        stuCout++;
                        totalScore = NumberUtil.add(totalScore, scoreMap.get(stu.getStudentId()));
                    }
                    //获取学生排名数据
                    if (null != stuRankMap.get(stu.getStudentId())) {
                        rankCount++;
                        titalRank = titalRank + stuRankMap.get(stu.getStudentId());
                    }
                }
            }
            //计算平均排名
            if (rankCount != 0) {
                BigDecimal avgRank = NumberUtil.round(NumberUtil.div(titalRank, rankCount), 6);
                groupDto.setAvgStuRank(avgRank);
            } else {
                //需要给一个 随机值排名
                groupDto.setAvgStuRank(NumberUtil.round(10000, 1));
            }
            //计算平均分数
            if (stuCout != 0) {
                BigDecimal finalAvg = NumberUtil.round(NumberUtil.div(totalScore, stuCout), 1);
                groupDto.setFinalAvgScore(finalAvg);
            } else {
                groupDto.setFinalAvgScore(NumberUtil.round(0.0, 1));
            }
        }

    }


    /**
     * 将接口传入的老师加分信息设置到游戏模型当中
     *
     * @param xsGameRecord
     * @param personRuleScoreList
     * @param gameData
     */
    public void setTeacherSpecifiedList(XsGameRecord xsGameRecord, List<GameReq.PersonalRuleDScore> personRuleScoreList, TwoWheelStageDto gameData) {
        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(personRuleScoreList)) {
            return;
        }
        //执行老师指定加分
        //将老师指定加分转换为标注格式
        String planMetaData = xsGameRecord.getPlanMetaData();
        PlanMeteDataDto planMeteDataDto = JSONObject.parseObject(planMetaData, PlanMeteDataDto.class);
        List<PersonalResultDto> teacherDefineList = gameData.getFinalResult().getTeacherDefineList();
        List<StudentDto> classAllStudentList = classComponent.getClassAllStudentList(planMeteDataDto);
        Map<String, StudentDto> stuMap = new HashMap<>();
        Map<String, PersonalResultDto> ruleMap = new HashMap<>();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(classAllStudentList)) {
            stuMap = classAllStudentList.stream().collect(Collectors.toMap(StudentDto::getStudentId, stu -> stu));
        }
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(teacherDefineList)) {
            ruleMap = teacherDefineList.stream().collect(Collectors.toMap(PersonalResultDto::getRuleCode, rule -> rule));
        }
        List<PersonalResultDto> teacherSpecifiedList = convertToStandPersonList(personRuleScoreList, stuMap, ruleMap);
        gameData.getFinalResult().setTeacherDefineList(teacherSpecifiedList);
    }


    private List<PersonalResultDto> convertToStandPersonList(List<GameReq.PersonalRuleDScore> personRuleScoreList, Map<String, StudentDto> stuMap, Map<String, PersonalResultDto> ruleMap) {
        List<PersonalResultDto> resultList = new ArrayList<>();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(personRuleScoreList)) {

            for (GameReq.PersonalRuleDScore base : personRuleScoreList) {
                if (ruleMap.get(base.getRuleCode()) == null) {
                    continue;
                }
                PersonalResultDto rule = ruleMap.get(base.getRuleCode());
                PersonalResultDto dto = new PersonalResultDto();
                dto.setRuleCode(base.getRuleCode());
                dto.setRuleName(rule.getRuleName());
                dto.setScore(rule.getScore());
                dto.setIcon(GameRuleEnum.getIconByCode(base.getRuleCode()));
                dto.setRank(GameRuleEnum.getRankByCode(base.getRuleCode()));
                List<StudentDto> studentList = new ArrayList<>();
                //没有设置奖项的学生也会入库
                if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(base.getStudentIds())) {
                    for (String id : base.getStudentIds()) {
                        StudentDto studentDto = stuMap.get(id);
                        if (null != studentDto) {
                            studentList.add(studentDto);
                        }
                    }
                }

                dto.setStudentList(studentList);
                resultList.add(dto);
            }

        }
        return resultList;
    }


    public List<XsPointRecord> genGameAddScoreRecord(FinalResultDto gameData, XsGameRecord xsGameRecord, GameReq gameReq, XsUserInfo xsUserInfo) {

        //执行小组加分  小组加分不需要考虑师徒关系
        //获取小组加分情况
        List<GroupDto> groupList = gameData.getGroupList();
        //生成小组加分记录
        List<XsPointRecord> groupScoreList = genGroupScoreAdd(groupList, xsGameRecord, gameReq, xsUserInfo);
        List<XsPointRecord> xsPointRecordList = new ArrayList<>(groupScoreList);
        //执行个人加分
        //获取个人奖项列表
        List<PersonalResultDto> personalResultList = gameData.getPersonalResultList();
        List<XsPointRecord> personScoreList = genPersonScoreAdd(personalResultList, xsGameRecord,
                gameReq, xsUserInfo);
        //老师指定奖项
        List<XsPointRecord> teacherScoreList = genPersonScoreAdd(gameData.getTeacherDefineList(),
                xsGameRecord, gameReq, xsUserInfo);
        xsPointRecordList.addAll(personScoreList);
        xsPointRecordList.addAll(teacherScoreList);
        //过滤掉0分的记录
        if (!CollectionUtils.isEmpty(xsPointRecordList)) {
            xsPointRecordList = xsPointRecordList.stream().
                    filter(xsPointRecord -> NumberUtil.compare(xsPointRecord.getScore(), 0.0) != 0).collect(Collectors.toList());
        }

        //获取最新班级学生的情况
        List<XsClassStudent> xsClassStudentList = xsClassStudentMapper.getListByClassId(gameReq.getSaasClassId(), xsUserInfo.getSchoolId());
        if (!CollectionUtils.isEmpty(xsClassStudentList)) {
            for (XsPointRecord xsPointRecord : xsPointRecordList) {

                Optional<XsClassStudent> xsClassStudentOptional = xsClassStudentList.stream()
                        .filter(t -> t.getStudentId().equals(xsPointRecord.getStudentId())).findFirst();
                if (!xsClassStudentOptional.isPresent()) {
                    //代表该学生已移出班级
                    xsPointRecord.setIsDeleted(DeletedConst.YES);
                }
            }
        } else {
            //此时班级下的学生全部解绑则加 被删除分
            for (XsPointRecord xsPointRecord : xsPointRecordList) {
                xsPointRecord.setIsDeleted(DeletedConst.YES);
            }
        }


        return xsPointRecordList;
    }


    public List<XsPointRecord> genPersonScoreAdd(List<PersonalResultDto> personalResultList, XsGameRecord xsGameRecord, GameReq gameReq, XsUserInfo xsUserInfo) {
        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(personalResultList)) {
            String planMetaData = xsGameRecord.getPlanMetaData();
            PlanMeteDataDto planMeteDataDto = JSONObject.parseObject(planMetaData, PlanMeteDataDto.class);
            //获取方案的积分率
            BigDecimal pointRetailRate = planMeteDataDto.getPointRetailRate();
            for (PersonalResultDto dto : personalResultList) {
                if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(dto.getStudentList())) {
                    for (StudentDto stu : dto.getStudentList()) {
                        XsPointRecord xsPointRecord = pointRecordConvert.gameGetPersonRecordObj(gameReq, xsUserInfo, stu, xsGameRecord, dto);
                        xsPointRecordList.add(xsPointRecord);
                        //师徒关系存在且   积分比例大于0  才进行师徒加分
                        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(stu.getParentList()) &&
                                NumberUtil.compare(pointRetailRate.doubleValue(), 0) > 0) {
                            //计算师傅该加多少分
                            BigDecimal parentScore = NumberUtil.mul(xsPointRecord.getScore(), pointRetailRate);
                            for (StudentDto parent : stu.getParentList()) {
                                //递归设置师傅的加分记录
                                setPersonParentReocrd(parent, stu, gameReq, xsGameRecord, xsUserInfo, xsPointRecordList, xsPointRecord.getContent(), parentScore, pointRetailRate);
                            }
                        }
                    }
                }
            }
        }
        return xsPointRecordList;
    }

    private void setPersonParentReocrd(StudentDto parent, StudentDto initStu, GameReq gameReq,
                                       XsGameRecord xsGameRecord,
                                       XsUserInfo xsUserInfo, List<XsPointRecord> xsPointRecordList, String content,
                                       BigDecimal initailScore, BigDecimal pointRetailRate) {
        //生成师傅的加分记录
        XsPointRecord xsPointRecord = pointRecordConvert.gameGetPersonParentRecordObj(gameReq, xsUserInfo, parent, xsGameRecord, content, initStu, initailScore);
        xsPointRecordList.add(xsPointRecord);
        //如果师傅的师傅不为空  那么继续递归进行设置
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(parent.getParentList())) {
            //计算师傅该加多少分
            BigDecimal parentScore = NumberUtil.mul(pointRetailRate, initailScore);
            for (StudentDto studentDto : parent.getParentList()) {
                //执行递归
                setPersonParentReocrd(studentDto, initStu, gameReq, xsGameRecord, xsUserInfo, xsPointRecordList, content, parentScore, pointRetailRate);
            }
        }


    }

    public List<XsPointRecord> genGroupScoreAdd(List<GroupDto> groupList, XsGameRecord xsGameRecord, GameReq gameReq, XsUserInfo xsUserInfo) {
        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(groupList)) {
            for (GroupDto groupDto : groupList) {
                if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(groupDto.getStudentList())) {
                    for (StudentDto student : groupDto.getStudentList()) {
                        XsPointRecord xsPointRecord = pointRecordConvert.gameGetGroupRecordObj(gameReq, xsUserInfo, student,
                                xsGameRecord, groupDto.getSortVal(), groupDto.getAllPlusScore());
                        xsPointRecordList.add(xsPointRecord);
                    }
                }
            }
        }
        return xsPointRecordList;
    }


    /**
     * 根据游戏记录  获取本次游戏学生的加分情况
     * 学生id，学生本次游戏加分的总和
     */
    public Map<String, Double> getStuScoreInfoByGame(Long gameRecordId) {
        Map<String, Double> result = new HashMap<>();
        //根据游戏id 查询本次游戏的加分记录
        PointRecordQuery query = new PointRecordQuery();
        query.setGameRecordId(gameRecordId);
        List<XsPointRecord> scoreList = xsPointRecordMapper.getListByPointRecordQuery(query);
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(scoreList)) {
            Map<Long, List<XsPointRecord>> groupMap = scoreList.stream().collect(
                    Collectors.groupingBy(XsPointRecord::getStudentId));
            for (Map.Entry<Long, List<XsPointRecord>> entry : groupMap.entrySet()) {
                String stuId = String.valueOf(entry.getKey());
                double totalScore = entry.getValue().stream().mapToDouble(XsPointRecord::getScore).sum();
                result.put(stuId, NumberUtil.round(totalScore, 6).doubleValue());
            }
        }
        return result;
    }


}

package com.hailiang.edu.xsjlsys.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.base.dto.ResultJson;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.consts.ApplyLevelConst;
import com.hailiang.edu.xsjlsys.consts.PlanTypeConst;
import com.hailiang.edu.xsjlsys.convert.zongping.SchoolTagConvert;
import com.hailiang.edu.xsjlsys.dal.dao.XsSmsReportStudentMapper;
import com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlsys.dal.entity.XsSmsReportStudent;
import com.hailiang.edu.xsjlsys.dto.UserDto;
import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.award.AwardRecordRespDto;
import com.hailiang.edu.xsjlsys.dto.behavior.BehaviorReportDetailDto;
import com.hailiang.edu.xsjlsys.dto.callName.RangeLevelRespDto;
import com.hailiang.edu.xsjlsys.dto.excel.TagDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanTagCommentDto;
import com.hailiang.edu.xsjlsys.dto.plan.PlanTagDto;
import com.hailiang.edu.xsjlsys.dto.point.PlanCommentTagDto;
import com.hailiang.edu.xsjlsys.dto.point.resp.RecordAddRespDto;
import com.hailiang.edu.xsjlsys.dto.point.resp.StudentReportRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.RankProgressDto;
import com.hailiang.edu.xsjlsys.dto.rank.RankSingleScoreDto;
import com.hailiang.edu.xsjlsys.dto.rank.RankStudentScoreDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.BehaviorInfoRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.RankGroupScoreRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.RankHierarchyRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.RankSingleRespDto;
import com.hailiang.edu.xsjlsys.dto.rank.resp.RankStudentScoreRespDto;
import com.hailiang.edu.xsjlsys.dto.report.SelectOptionDto;
import com.hailiang.edu.xsjlsys.dto.sms.SmsReportDetailDto;
import com.hailiang.edu.xsjlsys.dto.sms.SmsReportDto;
import com.hailiang.edu.xsjlsys.emuns.SmsContentRangeEnum;
import com.hailiang.edu.xsjlsys.reqo.PlanCommentReq;
import com.hailiang.edu.xsjlsys.reqo.PlanReq;
import com.hailiang.edu.xsjlsys.reqo.PlanTagReq;
import com.hailiang.edu.xsjlsys.reqo.RecordReq;
import com.hailiang.edu.xsjlsys.service.AwardService;
import com.hailiang.edu.xsjlsys.service.EvaluateService;
import com.hailiang.edu.xsjlsys.service.PlanCommentService;
import com.hailiang.edu.xsjlsys.service.PlanService;
import com.hailiang.edu.xsjlsys.service.PlanTagService;
import com.hailiang.edu.xsjlsys.service.RecordService;
import com.hailiang.edu.xsjlsys.service.RecordV2Service;
import com.hailiang.edu.xsjlsys.service.ZongPingService;
import com.hailiang.edu.xsjlsys.util.DateUtil;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class RecordV2Business {

    @Resource
    RecordV2Service recordV2Service;

    @Resource
    PlanService planService;

    @Resource
    AwardService awardService;
    @Resource
    XsSmsReportStudentMapper xsSmsReportStudentMapper;

    @Resource
    EvaluateService evaluateService;

    @Resource
    ZongPingService zongPingService;

    @Resource
    PlanTagService planTagService;
    @Resource
    PlanCommentService planCommentService;
    @Resource
    SchoolTagConvert schoolTagConvert;

    private List<PlanCommentTagDto> validateReqCommentId(RecordReq recordReq, List<PlanCommentTagDto> detailObjList) {
        Set<String> commentIds = detailObjList.stream().map(PlanCommentTagDto::getPlanCommentId).collect(Collectors.toSet());
        Set<String> reqCommentIds = recordReq.getPlanCommentIdList().stream().map(RecordReq.PlanComments::getPlanCommentId).collect(Collectors.toSet());

        Set<String> planTagIds = recordReq.getPlanCommentIdList().stream().map(RecordReq.PlanComments::getPlanTagId).collect(Collectors.toSet());
        //取交集
        if (!CollUtil.isEmpty(commentIds) && !CollUtil.isEmpty(reqCommentIds)) {
            // 求交集
            Set<String> intersection = new HashSet<>(commentIds);
            intersection.retainAll(reqCommentIds);

            // 比较交集长度和 reqCommentIds 的长度
            if (intersection.size() != reqCommentIds.size()) {
                throw new BusinessException("存在无效点评项");
            }
        }
        return detailObjList.stream().filter(t -> (reqCommentIds.contains(t.getPlanCommentId()) &&
                planTagIds.contains(String.valueOf(t.getPlanTagId())))).collect(Collectors.toList());
    }

    public BehaviorInfoRespDto classInfo(RecordReq recordReq, XsUserInfo xsUserInfo) {


        //获取方案下用户详情模块数据
        PlanReq planReq = new PlanReq();
        planReq.setPlanId(recordReq.getPlanId());
        planReq.setSaasClassId(recordReq.getSaasClassId());
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planGroupDetailDto);

        //查询积分明细数据
        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        List<XsPointRecord> beforeXsPointRecordList = new ArrayList<>();
        recordV2Service.setCurrentAndBeforePointRecordListV2(recordReq, xsUserInfo, xsPointRecordList, beforeXsPointRecordList);


        BehaviorInfoRespDto behaviorInfoRespDto = recordV2Service.classInfo(recordReq, xsUserInfo, planGroupDetailDto, studentDtoList
                , xsPointRecordList, beforeXsPointRecordList);


        //进行榜单数据汇总
        BehaviorInfoRespDto.BillboardDto billboardDto = new BehaviorInfoRespDto.BillboardDto();

        //获取组的统计
        RankGroupScoreRespDto rankGroupScoreRespDto = recordV2Service.group(recordReq, xsUserInfo, planGroupDetailDto
                , xsPointRecordList, beforeXsPointRecordList, false);
        //还要进行组的长度切割
        billboardDto.setGroupList(ListUtil.sub(rankGroupScoreRespDto.getGroupList(), 0, 3));

        //获取个人榜的统计
        RankStudentScoreRespDto rankStudentScoreRespDto = recordV2Service.personal(recordReq, xsUserInfo
                , xsPointRecordList, beforeXsPointRecordList, false);
        //还要进行个人榜数据的切割
        billboardDto.setPersonalStudentList(ListUtil.sub(rankStudentScoreRespDto.getStudentList(), 0, 3));


        //取原始个人榜统计
        RankStudentScoreRespDto rankStudentScoreRespDto1 = recordV2Service.personal(recordReq, xsUserInfo
                , xsPointRecordList, beforeXsPointRecordList, true);
        //获取进步榜的统计
        List<RankStudentScoreDto> rankStudentScoreDtos = recordV2Service.progress(rankStudentScoreRespDto1.getStudentList(), false);
        //还要进行个人进步榜数据的切割
        billboardDto.setProgressStudentList(ListUtil.sub(rankStudentScoreDtos, 0, 3));


        //获取单项榜的统计
        List<RankSingleScoreDto> rankSingleScoreDtoList = recordV2Service.single(recordReq, xsUserInfo, xsPointRecordList, 1, false);
        billboardDto.setSinglePlanTagList(ListUtil.sub(rankSingleScoreDtoList, 0, 4));

        //设置榜单信息
        behaviorInfoRespDto.setBillboard(billboardDto);

        //是否分组
        behaviorInfoRespDto.setIsGroup(!planGroupDetailDto.getPlanType().equals(PlanTypeConst.UN_GROUP));

        return behaviorInfoRespDto;


    }


    public RankGroupScoreRespDto group(RecordReq recordReq, XsUserInfo xsUserInfo) {

        //获取方案下用户详情模块数据
        PlanReq planReq = new PlanReq();
        planReq.setPlanId(recordReq.getPlanId());
        planReq.setSaasClassId(recordReq.getSaasClassId());
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //设置默认积分值
        planService.setInitScore(planGroupDetailDto, 2);

        //查询积分明细数据
        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        List<XsPointRecord> beforeXsPointRecordList = new ArrayList<>();
        recordV2Service.setCurrentAndBeforePointRecordListV2(recordReq, xsUserInfo, xsPointRecordList, beforeXsPointRecordList);

        RankGroupScoreRespDto rankGroupScoreRespDto = recordV2Service.group(recordReq, xsUserInfo, planGroupDetailDto, xsPointRecordList, beforeXsPointRecordList, true);

        return rankGroupScoreRespDto;

    }

    public RankStudentScoreRespDto personal(RecordReq recordReq, XsUserInfo xsUserInfo) {


        planService.validateJoinPlan(recordReq.getPlanId(), xsUserInfo.getUserId(), recordReq.getSaasClassId());

        //查询积分明细数据
        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        List<XsPointRecord> beforeXsPointRecordList = new ArrayList<>();
        recordV2Service.setCurrentAndBeforePointRecordListV2(recordReq, xsUserInfo, xsPointRecordList, beforeXsPointRecordList);


        RankStudentScoreRespDto rankStudentScoreRespDto = recordV2Service.personal(recordReq, xsUserInfo
                , xsPointRecordList, beforeXsPointRecordList, true);
        return rankStudentScoreRespDto;

    }

    public RankProgressDto progress(RecordReq recordReq, XsUserInfo xsUserInfo) {


        planService.validateJoinPlan(recordReq.getPlanId(), xsUserInfo.getUserId(), recordReq.getSaasClassId());

        //查询积分明细数据
        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        List<XsPointRecord> beforeXsPointRecordList = new ArrayList<>();
        recordV2Service.setCurrentAndBeforePointRecordListV2(recordReq, xsUserInfo, xsPointRecordList, beforeXsPointRecordList);

        RankStudentScoreRespDto rankStudentScoreRespDto = recordV2Service.personal(recordReq, xsUserInfo
                , xsPointRecordList, beforeXsPointRecordList, true);

        List<RankStudentScoreDto> rankStudentScoreDtoList1 = recordV2Service.progress(rankStudentScoreRespDto.getStudentList(), true);

//        JSONObject result = new JSONObject();
//        result.put("studentList", rankStudentScoreDtoList1);
//        result.put("isExistRecord", rankStudentScoreRespDto.getIsExistRecord());

        RankProgressDto rankProgressDto = new RankProgressDto();
        rankProgressDto.setStudentList(rankStudentScoreDtoList1);
        rankProgressDto.setIsExistRecord(rankStudentScoreRespDto.getIsExistRecord());

        return rankProgressDto;

    }

    public ResultJson getList(RecordReq recordReq, XsUserInfo xsUserInfo) {

        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(recordReq.getSaasClassId(), xsUserInfo);

        JSONObject result = recordV2Service.getList(recordReq, xsUserInfo, studentDtoList);

        return ResultJson.success(result);

    }

    public void export(RecordReq recordReq, XsUserInfo xsUserInfo, HttpServletResponse response) throws BusinessException, IOException {

        //获取方案下用户详情模块数据
        PlanReq planReq = new PlanReq();
        planReq.setPlanId(recordReq.getPlanId());
        planReq.setSaasClassId(recordReq.getSaasClassId());
        PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

        //进行学生列表数据整理
        List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planGroupDetailDto);


        recordV2Service.export(recordReq, xsUserInfo, planGroupDetailDto, studentDtoList, response);

    }


    public ResultJson tagGetList(RecordReq recordReq, XsUserInfo xsUserInfo) {

        List<TagDto> tagDtoList = recordV2Service.tagGetList(recordReq, xsUserInfo);
        JSONObject result = new JSONObject();
        result.put("tagList", tagDtoList);

        return ResultJson.success(result);

    }


    public ResultJson del(RecordReq recordReq, XsUserInfo xsUserInfo) {

        recordV2Service.del(recordReq, xsUserInfo);

        return ResultJson.success(new JSONObject());

    }


    public ResultJson studentReport(RecordReq recordReq, XsUserInfo xsUserInfo) {

        if (recordReq.getPlanId() != null) {
            //获取方案下用户详情模块数据
            PlanReq planReq = new PlanReq();
            planReq.setPlanId(recordReq.getPlanId());
            planReq.setSaasClassId(recordReq.getSaasClassId());
            PlanGroupDetailDto planGroupDetailDto = planService.groupDetail(planReq, xsUserInfo, true);

            List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(planGroupDetailDto);

            List<PlanGroupDetailDto.GroupDto> groupList = planGroupDetailDto.getGroupList();

            StudentReportRespDto studentReportRespDto = recordV2Service.report(recordReq, xsUserInfo, groupList, studentDtoList);

            //设置获奖数据
            studentReportRespDto.setAwardList(awardService.reportAwardList(recordReq, xsUserInfo, false));

            studentReportRespDto.setEvaluateList(evaluateService.reportEvaluateList(recordReq, xsUserInfo));

            return ResultJson.success(studentReportRespDto);
        } else {

            List<PlanGroupDetailDto.StudentDto> studentDtoList = planService.getPlanStudentList(recordReq.getSaasClassId(), xsUserInfo);

            StudentReportRespDto studentReportRespDto = recordV2Service.report(recordReq, xsUserInfo, new ArrayList<>(), studentDtoList);

            //设置获奖数据
            studentReportRespDto.setAwardList(awardService.reportAwardList(recordReq, xsUserInfo, false));

            studentReportRespDto.setEvaluateList(evaluateService.reportEvaluateList(recordReq, xsUserInfo));

            return ResultJson.success(studentReportRespDto);

        }

    }


    public ResultJson selectOptionLists(RecordReq recordReq, XsUserInfo xsUserInfo) {

        planService.validateJoinPlan(recordReq.getPlanId(), xsUserInfo.getUserId(), recordReq.getSaasClassId());

        PlanReq planReq = new PlanReq();
        planReq.setPlanId(recordReq.getPlanId());
        List<UserDto> userDtoList = planService.getPlanUserList(planReq, xsUserInfo);

        List<SelectOptionDto> selectOptionDtoList = recordV2Service.selectOptionLists(recordReq, xsUserInfo, userDtoList);

        JSONObject result = new JSONObject();
        result.put("selectOptionList", selectOptionDtoList);

        return ResultJson.success(result);

    }


    public ResultJson getStudent(RecordReq recordReq) throws MalformedURLException {

        Long studentId = recordV2Service.getStudent(recordReq);
        JSONObject result = new JSONObject();
        result.put("studentId", String.valueOf(studentId));

        return ResultJson.success(result);

    }


    public ResultJson reportGetLists(RecordReq recordReq) {

        List<SmsReportDto> smsReportDtoList = recordV2Service.reportGetLists(recordReq);
        JSONObject result = new JSONObject();
        result.put("smsReportList", smsReportDtoList);

        return ResultJson.success(result);

    }


    public ResultJson smsReportDetail(RecordReq recordReq) {

        //设置参数情况信息
        recordV2Service.setReportDetailReqParam(recordReq);

        //验证报告数据
        XsSmsReportStudent xsSmsReportStudent = recordV2Service.validateSmsReportStudent(recordReq);

        //如果不是教师 则更新该学生报告记录为已查看
        if (recordReq.getIsTeacher() != null && Boolean.FALSE.equals(recordReq.getIsTeacher())) {
            xsSmsReportStudent.setIsView(1);
            xsSmsReportStudent.setUpdateTime(DateUtil.getDateTime());
            xsSmsReportStudentMapper.updateById(xsSmsReportStudent);
        }

        List<SmsReportDetailDto> smsReportDetailDtoList = recordV2Service.smsReportDetail(recordReq, xsSmsReportStudent);

        //前端日志埋点数据
        BehaviorReportDetailDto smsReportExt = recordV2Service.getSmsReportExt(xsSmsReportStudent);

        List<AwardRecordRespDto.DetailDto> smsAwardDetailList = new ArrayList<>();
        if (xsSmsReportStudent != null && CharSequenceUtil.split(xsSmsReportStudent.getXsSmsReport().getSmsContentRange(), ",")
                .contains(SmsContentRangeEnum.AWARD.getCode())) {
            //获取奖状数据 取快照
            recordV2Service.setAwardDetailReqParam(recordReq, xsSmsReportStudent);
            smsAwardDetailList = awardService.setStudentAwardList(recordReq.getStudentId(), smsReportDetailDtoList.get(0).getAwardRecordIds());
        }

        List<SmsReportDetailDto.EvaluateDto> smsStudentEvaluates = new ArrayList<>();
        if (xsSmsReportStudent != null && CharSequenceUtil.split(xsSmsReportStudent.getXsSmsReport().getSmsContentRange(), ",")
                .contains(SmsContentRangeEnum.EVALUATE.getCode())) {
            List<SmsReportDetailDto.EvaluateDto> evaluates = smsReportDetailDtoList.get(0).getEvaluates();
            if (CollUtil.isNotEmpty(evaluates)) {
                smsStudentEvaluates = evaluates.stream()
                        .filter(p -> CharSequenceUtil.equals(p.getStudentId(), String.valueOf(xsSmsReportStudent.getStudentId()))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(smsStudentEvaluates)) {
                    ListUtil.sort(smsStudentEvaluates, Comparator.comparingLong(p -> Long.parseLong(p.getEvaluateDetailId())));
                    ListUtil.reverse(smsStudentEvaluates);
                    smsStudentEvaluates = Collections.singletonList(smsStudentEvaluates.get(0));
                }
            }
        }

        JSONObject result = new JSONObject();

        result.put("smsReportDetailList", smsReportDetailDtoList);
        result.put("smsReportExt", smsReportExt);
        result.put("smsAwardDetailList", smsAwardDetailList);
        result.put("smsEvaluateList", smsStudentEvaluates);

        return ResultJson.success(result);

    }

    public RankSingleRespDto single(RecordReq recordReq, XsUserInfo xsUserInfo) {


        //查询积分明细数据
        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        boolean isExistRecord = recordV2Service.setXsPointRecordListV2(recordReq, xsUserInfo, xsPointRecordList);

        List<RankSingleScoreDto> rankSingleScoreDtos = recordV2Service.single(recordReq, xsUserInfo
                , xsPointRecordList, 3, false);

        RankSingleRespDto result = new RankSingleRespDto();
        result.setPlanTagList(rankSingleScoreDtos);
        result.setIsExistRecord(isExistRecord);
        return result;

    }

    public RankHierarchyRespDto hierarchy(RecordReq recordReq, XsUserInfo xsUserInfo) {

        //查询积分明细数据
        List<XsPointRecord> xsPointRecordList = new ArrayList<>();
        boolean isExistRecord = recordV2Service.setXsPointRecordListV2(recordReq, xsUserInfo, xsPointRecordList);

        List<RangeLevelRespDto> rankHierarchyScoreDtoList = recordV2Service.hierarchy(recordReq, xsUserInfo
                , xsPointRecordList);

        RankHierarchyRespDto result = new RankHierarchyRespDto();
        result.setHierarchyList(rankHierarchyScoreDtoList);
        result.setIsExistRecord(isExistRecord);
        return result;

    }

}

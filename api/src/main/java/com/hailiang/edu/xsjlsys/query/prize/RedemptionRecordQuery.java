package com.hailiang.edu.xsjlsys.query.prize;

import lombok.Data;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class RedemptionRecordQuery {

    private String saasSchoolId;

    private String saasClassId;

    private Long planId;

    private Integer isRevoke;

    private List<Integer> redemptionRecordIds;

    private String redemptionMethod;

    private List<String> redemptionMethods;

    private String prizeType;

    private String startTime;

    private String endTime;


}

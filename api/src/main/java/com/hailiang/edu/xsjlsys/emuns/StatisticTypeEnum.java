package com.hailiang.edu.xsjlsys.emuns;

import lombok.Getter;

@Getter
public enum StatisticTypeEnum {

    /**
     * 按点评分统计
     */
    SCORE(1, "按点评分统计"),

    /**
     * 按金币统计
     */
    COIN(2, "按金币统计"),
    ;

    StatisticTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private final Integer type;
    private final String desc;

    public static String getDescByType(Integer type) {
        for (StatisticTypeEnum statisticTypeEnum : StatisticTypeEnum.values()) {
            if (type.equals(statisticTypeEnum.getType())) {
                return statisticTypeEnum.getDesc();
            }
        }
        return "";
    }

    public static StatisticTypeEnum getByType(Integer type) {
        for (StatisticTypeEnum statisticTypeEnum : StatisticTypeEnum.values()) {
            if (type.equals(statisticTypeEnum.getType())) {
                return statisticTypeEnum;
            }
        }
        return null;
    }

    // 添加参数有效性验证方法
    public static boolean isValidType(Integer type) {
        if (type == null) {
            return false;
        }
        for (StatisticTypeEnum statisticTypeEnum : StatisticTypeEnum.values()) {
            if (type.equals(statisticTypeEnum.getType())) {
                return true;
            }
        }
        return false;
    }
}
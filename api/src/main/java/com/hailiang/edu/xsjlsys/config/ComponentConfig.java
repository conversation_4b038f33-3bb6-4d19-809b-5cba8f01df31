package com.hailiang.edu.xsjlsys.config;


import cn.hutool.core.date.DateUtil;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.hailiang.base.consts.StringConst;
import com.hailiang.edu.xsjlsys.util.RedisUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.Map;

@Configuration
public class ComponentConfig {

    /**
     * redis 第一个实例
     */
    @Resource(name = "stringRedisTemplate")
    StringRedisTemplate stringRedisTemplate;

    @Value("${canary:green}")
    private String canary;

    @Bean
    RedisUtil redisUtil() {
        return new RedisUtil(stringRedisTemplate);
    }

    /**
     * redis 第二个实例
     */
    @Resource(name = "stringRedisTemplate2")
    StringRedisTemplate stringRedisTemplate2;


    @Bean("redisUtil2")
    RedisUtil redisUtil2() {
        return new RedisUtil(stringRedisTemplate2);
    }

    @Bean
    public NacosDiscoveryProperties nacosProperties() {
        NacosDiscoveryProperties nacosDiscoveryProperties = new NacosDiscoveryProperties();
        Map<String, String> metadata = nacosDiscoveryProperties.getMetadata();
        metadata.put("startup.time", DateUtil.now());
        metadata.put(StringConst.TAG_CANARY, canary);
        return nacosDiscoveryProperties;
    }
}

package com.hailiang.edu.xsjlsys.consts;

public class ApiCodeConst {
    public static final int CODE_SUCCESS = 10000;
    public static final int CODE_ERROR = 20000;

    public static final int CODE_LOGIN_ERROR = 19002;

    //需要图形验证码
    public static final int CODE_ERROR_VERIFICATION = 19003;
    //账号锁定
    public static final int CODE_ERROR_LOCK = 19004;
    //初次登录
    public static final int CODE_ERROR_FIRST_LOGIN = 19005;
    //密码过期
    public static final int CODE_ERROR_PASSWORD_EXPIRED = 19006;

    //名字重复
    public static final int CODE_ERROR_REPEAT = 20001;

    //学生信息异常
    public static final int CODE_ERROR_STUDENT = 20002;

    //用户不在方案里面  或方案不存在
    public static final int PLAN_ERROR = 20003;

    //规则异常
    public static final int RULE_ERROR = 20004;

    //游戏记录异常
    public static final int GAME_RECORD_ERROR = 20005;

    //游戏状态异常
    public static final int GAME_STATUS_ERROR = 20006;

    //分组异常
    public static final int GROUP_ERROR = 20007;

    //分享页解密异常
    public static final int SHARE_ERROR = 20008;

    //需刷新
    public static final int CODE_ERROR_REFRESH = 20009;

    //学校信息异常
    public static final int SCHOOL_ERROR = 20010;

    //需返回首页
    public static final int CODE_ERROR_RETURN_HOME_PAGE = 20011;

    //前端自己处理情况的code
    public static final int CODE_ERROR_REFRESH_CURRENT_PAGE = 20012;

    // 当前微信未绑定账户
    public static final int CODE_ERROR_WECHAT_UN_BINDING = 20013;
    // 当前账号系统未注册
    public static final int CODE_ERROR_WECHAT_UN_REGISTER = 20014;
    // 当前手机号已经被绑定
    public static final int CODE_ERROR_WECHAT_HAS_BINDING = 20015;
    // 短信验证码失效,请重新获取
    public static final int CODE_ERROR_WECHAT_INVALID_CODE = 20016;

    //智能加分错误 统一code
    public static final int AI_POINT_ERROR = 20017;
}

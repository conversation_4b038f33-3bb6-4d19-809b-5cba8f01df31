package com.hailiang.edu.xsjlsys.query.point;

import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class ScoreQuery {

    private Long planId;

    private Set<Long> planIds;

    private String saasClassId;

    private Long userId;

    private Set<Long> userIds;

    private Long planTagId;

    private Set<Long> planTagIds;

    private String startTime;

    private String endTime;

    private Boolean isExceptMinusScore;

    private String timeType;

    private Long studentId;

    private Boolean orderByCreateTime;

    private List<Long> exceptChannelIds;

}

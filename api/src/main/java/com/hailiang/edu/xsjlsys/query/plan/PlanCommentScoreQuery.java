package com.hailiang.edu.xsjlsys.query.plan;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 点评项分值查询
 *
 * @Description: 点评项分值查询
 * @Author: tan<PERSON>an
 * @Date: Created in 2025-07-17
 * @Version: 1.0.0
 */

@Data
public class PlanCommentScoreQuery {
    private List<Long> saasClassIdList;

    private BigDecimal maxScore;

    private BigDecimal minScore;

    private List<Long> planCommentIdList;
}

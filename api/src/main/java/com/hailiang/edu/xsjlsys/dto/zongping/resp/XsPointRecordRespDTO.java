package com.hailiang.edu.xsjlsys.dto.zongping.resp;

import lombok.Data;

/**
 * Table: xs_point_record
 */
@Data
public class XsPointRecordRespDTO {
    /**
     * Column: id
     */
    private Long id;

    /**
     * Column: content
     * Remark: 明细描述
     */
//    private String content;

    /**
     * Column: student_id
     * Remark: 学生id
     */
    private Long studentId;

    /**
     * Column: user_id
     * Remark: 用户id
     */
//    private Long userId;

    /**
     * Column: account_name
     * Remark: 用户姓名
     */
//    private String accountName;

    /**
     * Column: score
     * Remark: 分值
     */
    private Double score;

    /**
     * Column: plan_id
     * Remark: 方案id
     */
//    private Long planId;

    /**
     * Column: channel
     * Remark: 积分来源渠道 1|点评项 2|游戏 3|奖品
     */
//    private Integer channel;

    /**
     * Column: scene
     * Remark: 得分场景 personal|个人所得 help|帮扶所得
     */
//    private String scene;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
//    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
//    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
//    private String saasTenantId;

    /**
     * Column: is_deleted
     * Remark: 是否删除 0|否 1|是
     */
//    private Integer isDeleted;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
//    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
//    private String updateTime;

    /**
     * Column: plan_comment_id
     * Remark: 方案点评项id
     */
//    private String planCommentId;

    /**
     * Column: plan_comment_content
     * Remark: 点评项内容
     */
//    private String planCommentContent;

    /**
     * Column: plan_tag_id
     * Remark: 方案分类id
     */
//    private Long planTagId;

    /**
     * Column: plan_tag_name
     * Remark: 分类名称
     */
    private String planTagName;

    /**
     * Column: game_id
     * Remark: 游戏id
     */
//    private Long gameId;

    /**
     * Column: game_name
     * Remark: 游戏名称
     */
//    private String gameName;

    /**
     * Column: game_record_id
     * Remark: 游戏记录id
     */
//    private Long gameRecordId;

    /**
     * Column: game_record_title
     * Remark: 游戏记录名称
     */
//    private String gameRecordTitle;

    /**
     * Column: redemption_record_id
     * Remark: 奖品兑换记录id
     */
//    private Long redemptionRecordId;


    /**
     * Column: module_code
     * Remark: 综评 0|无 1|德 2|智 3|体 4|美 5|劳
     *
     */
//    private Integer moduleCode;

    /**
     * 学科 code
     */
//    private String subjectCode;

    /**
     * 第三方明细 id
     */
    private Long thirdBusinessId;

    /**
     * Column: apply_level
     * Remark: 应用级别 general|普通 school|校级
     *
     */
//    private String applyLevel;


    /**
     * 积分来源渠道 1|点评项 2|游戏 3|奖品
     */
    /*public static final int CHANNEL_COMMENT = 1;
    public static final int CHANNEL_GAME = 2;
    public static final int CHANNEL_PRIZE = 3;


    public static final String SCENE_PERSONAL = "personal";
    public static final String SCENE_HELP = "help";*/
    /**
     * 来源类型
     */
    private Integer sourceType;
}
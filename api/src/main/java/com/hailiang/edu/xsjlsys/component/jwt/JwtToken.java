package com.hailiang.edu.xsjlsys.component.jwt;


import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;

import com.hailiang.edu.xsjlsys.dto.XsUserInfo;
import com.hailiang.edu.xsjlsys.dto.share.SharePwdDto;
import com.hailiang.edu.xsjlsys.util.DateUtil;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 组件 jwt 处理类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class JwtToken {


    @Value("${jwt.appkey}")
    private String appKey;

    public XsUserInfo decodeEduUserInfo(String eduToken) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(appKey)).build();
            DecodedJWT jwt = verifier.verify(eduToken);
            String userInfoJson = jwt.getClaim("userInfoJson").asString();
            if (null != userInfoJson) {
                return JSONObject.parseObject(userInfoJson, XsUserInfo.class);
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    public String encodeEduUserInfo(XsUserInfo xsUserInfo) {
        try {
            //token 生成 有效期为 10分钟先
            long time = DateUtil.getCurrentTimeStamp() + 60 * 60 * 24 * 1000 * 7;
//            long time = DateUtil.getCurrentTimeStamp();
            Date expiresDate = new Date(time);

            Map<String, Object> map = new HashMap<String, Object>();
            map.put("alg", "HS256");
            map.put("typ", "JWT");
            return JWT.create()
                    .withHeader(map)
                    .withClaim("userInfoJson", JSONObject.toJSONString(xsUserInfo))
                    .withExpiresAt(expiresDate)
                    .sign(Algorithm.HMAC256(appKey));
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     * 验证教学登陆是否有效
     *
     * @param token
     * @return
     */
    public boolean isValidEduToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(appKey)).build();
            verifier.verify(token);

            return true;
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return false;
    }


    public String encodeSharePwd(SharePwdDto sharePwdDto) {
        try {
            long time = DateUtil.getCurrentTimeStamp() + 60L * 60 * 24 * 1000 * 365;

            Date expiresDate = new Date(time);


            Map<String, Object> map = new HashMap<String, Object>();
            map.put("alg", "HS256");
            map.put("typ", "JWT");
            return JWT.create()
                    .withHeader(map)
                    .withClaim("sharePwd", JSONObject.toJSONString(sharePwdDto))
                    .withExpiresAt(expiresDate)
                    .sign(Algorithm.HMAC256(appKey));
        } catch (Exception e) {
            log.info(e.getMessage());
        }

        return "";
    }

    public SharePwdDto decodeSharePwd(String sharePwd) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(appKey)).build();
            DecodedJWT jwt = verifier.verify(sharePwd);
            String userInfoJson = jwt.getClaim("sharePwd").asString();
            if (null != userInfoJson) {
                return JSONObject.parseObject(userInfoJson, SharePwdDto.class);
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }


}

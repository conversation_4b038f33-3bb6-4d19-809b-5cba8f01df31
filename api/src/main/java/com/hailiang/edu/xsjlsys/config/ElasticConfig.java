package com.hailiang.edu.xsjlsys.config;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * elasticsearch 配置
 *
 * <AUTHOR>
 */
@Configuration
public class ElasticConfig {


    @Value("${elastic.host}")
    private String host;

    @Value("${elastic.port}")
    private int port;


    @Value("${elastic.auth}")
    private String auth;

    @Value("${elastic.username}")
    private String username;

    @Value("${elastic.password}")
    private String password;


    private RestClientBuilder buildClientBuilder() {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();

        if ("true".equals(auth)) {
            credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(username, password));
        }

        return RestClient.builder(new HttpHost(host, port))
                .setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                    @Override
                    public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                        return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                    }
                });
    }

    /**
     * 获取Rest高级客户端
     *
     * @return
     */
    @Bean
    public RestHighLevelClient getRestHighLevelClient() {

        return new RestHighLevelClient(buildClientBuilder());
    }


    /**
     * 获取Rest低级客户端
     *
     * @return
     */
    @Bean
    public RestClient getRestClient() {
        return buildClientBuilder().build();
    }


}

package com.hailiang.edu.xsjlsys.consts;

public class EventCodeConst {


    //saas相关事件通知
    //作为下游接收
    //新增学生
    public static final String SAAS_STUDENT_00001 = "saas-student-00001";

    //班级关联学生
    public static final String SAAS_STUDENT_00002 = "saas-student-00002";

    //班级解绑关联学生
    public static final String SAAS_STUDENT_00003 = "saas-student-00003";

    //学生主体信息变更
    public static final String SAAS_STUDENT_00004 = "saas-student-00004";

    //学生删除
    public static final String SAAS_STUDENT_00005 = "saas-student-00005";

    //接收积分明细删除事件
    public static final String XWL_RECORD_BATCH_REMOVE_0001 = "xwl-record-batch-remove-0001";
    //接收积分明细新增事件
    public static final String XWL_RECORD_BATCH_ADD_0001 = "xwl-record-batch-add-0001";


    //综合素质评价相关事件通知
    //作为上游发送


    //点评分类变动通知
    public static final String XSJL_TAG_000001 = "xsjl-tag-000001";

    //发送积分明细新增事件
    public static final String XDL_RECORD_BATCH_ADD_0001 = "xdl-record-batch-add-0001";
    //发送积分明细删除事件
    public static final String XDL_RECORD_BATCH_REMOVE_0001 = "xdl-record-batch-remove-0001";

}

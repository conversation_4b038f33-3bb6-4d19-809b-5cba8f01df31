package com.hailiang.edu.xsjlsys.component.mq.point;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlsys.dto.point.PointTaskDto;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component
@Slf4j
public class PointProducer {

    @Value("${rocketmq.point.topic}")
    private String topic;

    @Resource
    private RocketMQTemplate rocketMqTemplate;

    public void send(PointTaskDto pointTaskDto) {

        if (pointTaskDto == null || CollUtil.isEmpty(pointTaskDto.getPointRecordIds())) {
            return;
        }

        String msg = JSONObject.toJSONString(pointTaskDto);
        log.info("DelPointRecordIds:{}" + msg);
        rocketMqTemplate.asyncSend(topic + ":", msg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                String msgId = sendResult.getMsgId();
                log.info(">>>> DelPointRecordIds message success, send status={}, message id={} <<<<",
                        sendResult.getSendStatus().name(), msgId);
            }

            @Override
            public void onException(Throwable throwable) {
                log.info(">>>> DelPointRecordIds message success, exception message={} <<<<", throwable.getMessage());
            }
        });

    }

}

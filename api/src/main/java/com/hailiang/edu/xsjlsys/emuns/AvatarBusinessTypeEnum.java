/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlsys.emuns;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version v0.1: FileSourceTypeEnum.java, v 0.1 2023年10月23日 16:17  zhousx Exp $
 */
@Getter
public enum AvatarBusinessTypeEnum {

    //分组
    PLAN_GROUP("planGroup", "分组"),
    //学生
    STUDENT("student", "学生"),
    ;


    AvatarBusinessTypeEnum(String type, String value) {
        this.type = type;
        this.value = value;
    }

    private final String type;
    private final String value;


    public static String getValue(String businessType) {
        for (AvatarBusinessTypeEnum gameEnum : AvatarBusinessTypeEnum.values()) {
            if (businessType.equals(gameEnum.getType())) {
                return gameEnum.getValue();
            }
        }
        return "";
    }

}
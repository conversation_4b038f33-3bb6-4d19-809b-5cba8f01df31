package com.hailiang.edu.xsjlsys.emuns;


import lombok.Getter;


@SuppressWarnings("all")
@Getter
public enum RedemptionMethodEnum {

    /**
     * 个人扣分
     */
    PERSONAL(1, "personalSubtract"),

    /**
     * 集体扣分
     */
    COLLECTIVE(2, "collectiveSubtract"),

    /**
     * 班级清空
     */
    ALL_CLEAR(3, "allClear"),
    ;


    RedemptionMethodEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    private final int code;
    private final String value;


    public static String getValueByCode(Integer code) {
        for (RedemptionMethodEnum redemptionMethodEnum : RedemptionMethodEnum.values()) {
            if (code.equals(redemptionMethodEnum.getCode())) {
                return redemptionMethodEnum.getValue();
            }
        }
        return "";
    }

}

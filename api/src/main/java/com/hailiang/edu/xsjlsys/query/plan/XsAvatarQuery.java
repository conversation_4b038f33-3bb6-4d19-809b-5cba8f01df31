/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlsys.query.plan;

import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version v0.1: XsFileQuery.java, v 0.1 2023年10月24日 13:58  zhousx Exp $
 */
@Data
public class XsAvatarQuery {

    private Long avatarId;

    private List<Long> avatarIds;

    private Long userId;

    private String fileUrl;

    private Long businessId;

    private Collection<Long> businessIds;

    private String businessType;

    private String dataType;

    private String saasClassId;

    private Set<String> saasClassIds;

}
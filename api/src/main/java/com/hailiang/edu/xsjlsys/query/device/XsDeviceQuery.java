package com.hailiang.edu.xsjlsys.query.device;

import lombok.Data;
import java.util.Set;

@Data
public class XsDeviceQuery {

	/**
	 * 主键设备id
	 */
	private Long deviceId;

	/**
	 * 设备名称
	 */
	private String deviceName;

	/**
	 * 是否删除 0|否 1|是
	 */
	private Integer isDeleted = 0;

	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 更新时间
	 */
	private String updateTime;

	/**
	 * 排序
	 */
	private String sortCriteria;

	/**
	 * 分页查询偏移量
	 */
	private Integer offset;

	/**
	 * 分页查询条数
	 */
	private Integer limit;


	/**
	 * 主键设备ids
	 */
	private Set<Long> deviceIds;

}

package com.hailiang.edu.xsjlsys.emuns;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 兑换方式
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum MotivationExchangeTypeEnum {


    /**
     * 21:积分柜兑换
     */
    CABINET(21, false, "积分柜兑换"),
    /**
     * 22：星未来线下兑换
     */
    EVALUATE_OFFLINE(22, false, "星未来线下兑换"),
    /**
     * 23：星动力班级兑换
     */
    XSJL_CLASS(23, false, "星动力班级兑换"),
    /**
     * 24：星动力个人兑换
     */
    XSJL_PERSON(24, true, "星动力个人兑换"),
    ;

    private final Integer code;
    /**
     * 是否指定积分板
     */
    private final boolean needPlan;
    private final String desc;

    public boolean equal(Integer code) {
        return ObjectUtil.equal(this.getCode(), code);
    }

    public static MotivationExchangeTypeEnum byCode(Integer code) {
        for (MotivationExchangeTypeEnum checkDimEnum : MotivationExchangeTypeEnum.values()) {
            if (code.equals(checkDimEnum.getCode())) {
                return checkDimEnum;
            }
        }
        return null;
    }

    /**
     * 是否积分板兑换
     *
     * @param code
     * @return
     */
    public static boolean isPlan(Integer code) {
        return ObjectUtil.equal(XSJL_PERSON.getCode(), code);
    }
}

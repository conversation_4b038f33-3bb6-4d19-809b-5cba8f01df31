package com.hailiang.edu.xsjlsys.component.mq.sms;


import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlsys.dto.sms.SmsRuleTaskDto;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class SmsRuleProducer {

    @Value("${rocketmq.sms.topic}")
    private String topic;

    @Resource
    private RocketMQTemplate rocketMqTemplate;

    public void send(SmsRuleTaskDto smsRuleTaskDto) {

        if (smsRuleTaskDto == null) {
            return;
        }

        String msg = JSONObject.toJSONString(smsRuleTaskDto);
        log.info("taskDto:{}" + msg);
        rocketMqTemplate.asyncSend(topic + ":", msg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                String msgId = sendResult.getMsgId();
                log.info(">>>> async message success, send status={}, message id={} <<<<",
                        sendResult.getSendStatus().name(), msgId);
            }

            @Override
            public void onException(Throwable throwable) {
                log.info(">>>> async message success, exception message={}  <<<<", throwable.getMessage());
            }
        });

    }

}

package com.hailiang.edu.xsjlsys.emuns;


import lombok.Getter;


@SuppressWarnings("all")
@Getter
public enum SaasAppVersionCodeEnum {

    VERSION_1("101","旗舰版"),
    VERSION_2("102","自定义版本2"),
    VERSION_3("103","自定义版本3"),
    VERSION_4("104","自定义版本4"),
    VERSION_5("105","基础版");


    SaasAppVersionCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String code;
    private final String name;


    public static String getGameNameById(String gameId) {
        for (SaasAppVersionCodeEnum gameEnum : SaasAppVersionCodeEnum.values()) {
            if (gameId.equals(gameEnum.getCode())) {
                return gameEnum.getName();
            }
        }
        return "";
    }

}

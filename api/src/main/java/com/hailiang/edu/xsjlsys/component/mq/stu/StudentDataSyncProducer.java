/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlsys.component.mq.stu;

import java.util.Objects;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlsys.dto.stu.StudentDataSyncTaskDto;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 * @version v0.1: StudentDataSyncProducer.java, v 0.1 2023年09月19日 17:40  zhousx Exp $
 */
@Component
@Slf4j
public class StudentDataSyncProducer {

    @Value("${rocketmq.sync.topic}")
    private String           topic;

    @Resource
    private RocketMQTemplate rocketMqTemplate;

    public void send(StudentDataSyncTaskDto taskDto) {
        if (Objects.isNull(taskDto)) {
            return;
        }
        String msg = JSONObject.toJSONString(taskDto);
        log.info("学生信息同步taskDto:{}" + msg);

        rocketMqTemplate.asyncSend(topic + ":", msg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                String msgId = sendResult.getMsgId();
                log.info(">>>> async message success, send status={},message id={} <<<<", sendResult.getSendStatus().name(), msgId);
            }
            @Override
            public void onException(Throwable throwable) {
                log.info(">>>> async message success, exception message= {}  <<<<", throwable.getMessage());
            }
        });

    }
}
package com.hailiang.edu.xsjlsys.query.evaluate;

import com.hailiang.edu.xsjlsys.query.common.BaseQuery;
import lombok.Data;

import java.util.Collection;


@Data
public class EvaluateRecordQuery extends BaseQuery {

    private String evaluateRecordId;

    private String saasClassId;

    private String planId;

    private String startTime;

    private String endTime;

    /**
     * 是否过滤未保存状态
     */
    private Boolean filterUnSave;

    /**
     * 是否过滤取消状态
     */
    private Boolean filterCancel;

    private String evaluateStatus;

    private Collection<Long> planIds;

    private String roleCode;

    private String userId;

    private Collection<Long> userIds;

    private Boolean orderByCreateTime;
}

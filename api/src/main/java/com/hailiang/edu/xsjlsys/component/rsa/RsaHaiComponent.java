package com.hailiang.edu.xsjlsys.component.rsa;

import com.hailiang.edu.xsjlsys.util.RsaUtil2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * hai家校对接密文 - 加密解密
 */
@Component
public class RsaHaiComponent {

    @Value("${rsa.privateKey}")
    private String privateKey;

    @Value("${rsa.publicKey}")
    private String publicKey;


    public String decrypt(String secretInfo) {
        try {
            return RsaUtil2.decrypt(secretInfo, privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }

    public String encrypt(String msg) {

        try {
            return RsaUtil2.encrypt(msg, publicKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


/*    public static void main(String[] args) {

        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDTrusQ07KtNWLMdL51dLeQTl3AjknpjxZOOZyvLqyraDmki2CLjyuON62LvJU80zxWFssdK0CbDpi77hJa4XmECxMTh1maJdUz7T3R4cf2FquOWr3xhZbtRIkt1iB6s3BVVaMsSIcjcyeoUK52pGSMmvTd4qQCC4jl5QjZWkhB8QIDAQAB";
        String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANOu6xDTsq01Ysx0vnV0t5BOXcCOSemPFk45nK8urKtoOaSLYIuPK443rYu8lTzTPFYWyx0rQJsOmLvuElrheYQLExOHWZol1TPtPdHhx/YWq45avfGFlu1EiS3WIHqzcFVVoyxIhyNzJ6hQrnakZIya9N3ipAILiOXlCNlaSEHxAgMBAAECgYAqr/t3STme9EUYNfKyfPcHgsYlLox5LQGyhKu0KBVp+3qkU/cELbr60v+MTltKCM8CDVaoazB8FFV4kGnjKdT3DYpdRTpBmg7Zwl0hDVIGb3vC9SBgNxcGwraxbzxjxQF2jIz3Wx2c6/rLVlbT+HPezKruASn44BKfb3bqEQoagQJBAP1ubdjga+SGs5Z9pzRO1V+whyHltl7f1UydX3IDhq9cuvEvsSKCxiiGmc9Z1MojzPr3B8onpgceXtMMt2wtIp8CQQDV1Cql9aOiTh4RgOAXt/hlHgCyRYgttAk2k2WFUUXPSZ3XDl56h+2IJ7u7jhQ6zGKi5wFAT9hp2X27ZGS4aGFvAkEAsPkdYjaNgV8DJ9MFIQTwActfAi/MY4JBDChVT2Pg+LJzpRsdwjC6OfCMlfxwGNxZNuOsn+GCEjSGGTEnITZ/lwJAJl9x2+COIgVkzf1Tuc3xDKxqsZNa0eDaOmoPMPKZ1Y4/fiV8/P5BuUpwHUNtC/Q7y0IrblAl5TD7qFJm5NUOjwJBANeb+PnXhBBd8GhFYkpoAQ8wCo+1vA8E4PIoaaXR4+Kf7x0rLVHmt9tBy0VF2h1RtJhHtycSTs1rCMifs8l8uOs=";

        String secretInfo = "ND7NdSSgIyDBvu9UOlcvUrhxE4QmZRA2lyZLwA6G+KF38zpHVgdz04BfGKdPSlURboCF77fiCXZEhhANf5fIxFfMXE2vChhA/EtUe25qULk3foaHZ6Pcx2iIZzt1AsxARGe9Tv9ePxct/CuRPajlbM+Owgege4Emrw1RIV0ChK8=";
        String a = RsaUtil2.decrypt(secretInfo, privateKey);

//        String p = RsaUtil2.encrypt("test",publicKey);
//        System.out.println(p);

        System.out.println("121");


    }*/
}

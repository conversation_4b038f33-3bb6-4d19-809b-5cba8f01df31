package com.hailiang.edu.xsjlsys.config;


import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class RedissonConfig {

    @Value("${spring.redis.hostName}")
    private String address;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.password}")
    private String password;

    @Value("${spring.redis.database}")
    private Integer database;



    @Bean
    public RedissonClient getRedisson() {

        Config config = new Config();

        config.useSingleServer().setAddress(address())
                .setPassword(password)
                .setConnectionPoolSize(5)
                .setConnectionMinimumIdleSize(1)
                .setClientName("RedissonClient")
                .setDatabase(database)
                .setIdleConnectionTimeout(10000)
                .setConnectTimeout(10000)
                .setTimeout(3000);
        config.setLockWatchdogTimeout(200);
        return Redisson.create(config);
    }

    /**
     * 生成address
     * @return
     */
    private String address() {
        return "redis://" + address + ":" + port;
    }
}

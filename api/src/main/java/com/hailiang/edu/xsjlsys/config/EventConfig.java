package com.hailiang.edu.xsjlsys.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class EventConfig {

    //作为下游的参数
    @Value("${event.salt}")
    private String salt;

    //作为下游的参数
    @Value("${event.accessKey}")
    private String accessKey;



    //作为上游的参数
    @Value("${event.up.open}")
    private String open;

    @Value("${event.up.serverId}")
    private String serverId;

    @Value("${event.up.host}")
    private String upHost;

    @Value("${event.up.salt}")
    private String upSalt;

    @Value("${event.up.accessKey}")
    private String upAccessKey;





}

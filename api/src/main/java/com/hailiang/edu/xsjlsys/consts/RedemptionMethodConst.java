package com.hailiang.edu.xsjlsys.consts;

/**
 * <AUTHOR>
 */
public class RedemptionMethodConst {

    /**
     * 奖品兑换方式 个人扣分 personalSubtract ｜集体扣分 collectiveSubtract｜ 全班清空 allClear
     */
    public static final String PERSONALSUBTRACT = "personalSubtract";
    public static final String COLLECTIVESUBTRACT = "collectiveSubtract";
    public static final String ALLCLEAR = "allClear";
    public static final String CLASSEXCHANGE = "classExchange";
    public static final String XWLEXCHANGE = "xwlExchange";
}

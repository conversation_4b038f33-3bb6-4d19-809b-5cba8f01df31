package com.hailiang.edu.xsjlsys.component.mq.evaluate;


import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlsys.dto.evaluate.EvaluateTaskDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class EvaluateProducer {

    @Value("${rocketmq.evaluate.topic}")
    private String topic;

    @Resource
    private RocketMQTemplate rocketMqTemplate;


    public void send(EvaluateTaskDto evaluateTaskDto) {

        if (evaluateTaskDto == null) {
            return;
        }

        String msg = JSONObject.toJSONString(evaluateTaskDto);
        log.info("evaluateTaskDto:{}", msg);
        rocketMqTemplate.asyncSend(topic + ":", msg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                String msgId = sendResult.getMsgId();
                log.info(">>>> async message success, send status={}, message id={} <<<<",
                        sendResult.getSendStatus().name(), msgId);
            }

            @Override
            public void onException(Throwable throwable) {
                log.info(">>>> async message success, exception message={} <<<<", throwable.getMessage());
            }
        });

    }

}

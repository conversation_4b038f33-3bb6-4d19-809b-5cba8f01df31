package com.hailiang.edu.xsjlsys.emuns;

import cn.hutool.core.collection.CollUtil;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 奖励订到 业务类型 1:校级礼品 2:个人礼品 3:重新计分 4:学期清空
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum MotivationProdoctSourceEnum {


    /**
     * 1:校级礼品
     */
    SCHOOL_GIFT(1, "校级礼品"),
    /**
     * 2:个人礼品
     */
    PERSONAL_GIFT(2, "个人礼品"),
    /**
     * 3:重新计分
     */
    COLLECTIVE_PRIZE(3, "重新计分"),
    /**
     * 4:学期清空
     */
    ZERO(4, "学期清空"),
    ;

    private final Integer code;
    private final String desc;

    public static List<Integer> normalCodes() {
        return CollUtil.newArrayList(SCHOOL_GIFT.getCode(), PERSONAL_GIFT.getCode());
    }

    /**
     * 是否是兑换类型
     * @return
     */
    public static boolean isExchange(Integer productSource) {
        return MotivationProdoctSourceEnum.normalCodes().contains(productSource);
    }

    public boolean equal(Integer code) {
        return Objects.equals(this.getCode(), code);
    }

}

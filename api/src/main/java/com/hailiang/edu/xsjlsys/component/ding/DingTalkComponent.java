package com.hailiang.edu.xsjlsys.component.ding;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.hailiang.edu.xsjlsys.dto.plan.PlanGroupDetailDto;
import com.hailiang.edu.xsjlsys.dto.report.BehaviorReportDto;
import com.hailiang.edu.xsjlsys.dto.report.GameRecordReportDto;
import com.hailiang.edu.xsjlsys.dto.report.PointRecordReportDto;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;

@Component
public class DingTalkComponent {

    @Value("${dingRobot.secret}")
    private String secret;

    @Value("${dingRobot.sendUrl}")
    private String sendUrl;

    @Value("${spring.profiles.active}")
    private String env;

    private String getServerUrl() {

        try {
            Long timestamp = System.currentTimeMillis();

            String stringToSign = timestamp + "\n" + this.secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(this.secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");

            return this.sendUrl
                    + "&timestamp=" + timestamp + "&sign=" + sign;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public void send(String markDownText) {
        try {
            String serverUrl = getServerUrl();
            if (StrUtil.isEmpty(serverUrl)) {
                return;
            }
            DingTalkClient client = new DefaultDingTalkClient(serverUrl);
            OapiRobotSendRequest request = new OapiRobotSendRequest();

            request.setMsgtype("markdown");
            OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
            markdown.setTitle("每日早报");
          /*  markdown.setText("#### 昨日统计情况 \n" +
                    "> 9度，西北风1级，空气良dd89，相对温度73%\n\n" +
                    "> ![screenshot](https://gw.alicdn.com/tfs/TB1ut3xxbsrBKNjSZFpXXcXhFXa-846-786.png)\n" +
                    "> ###### 10点20分发布 [天气](http://www.thinkpage.cn/) \n");*/
            markdown.setText(markDownText);
            request.setMarkdown(markdown);

            OapiRobotSendResponse response = client.execute(request);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public String getMarkDownText(List<PointRecordReportDto> pointRecordReportDtoList, List<GameRecordReportDto> gameRecordReportDtoList,
                                  List<BehaviorReportDto> behaviorReportDtoList) {
        StringBuilder text = new StringBuilder("## 【" + env+"】" + DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd") + "运营情况 \n");
        text.append("> ![screenshot](https://static-inspire-stu.hailiangedu.com/system/stat.jpeg)\n");

        text.append("### 点评 \n\n");
        if (!CollectionUtils.isEmpty(pointRecordReportDtoList)) {

            Comparator<PointRecordReportDto> num = Comparator.comparing(PointRecordReportDto::getNum);
            ListUtil.sort(pointRecordReportDtoList, num.reversed());

            for (PointRecordReportDto pointRecordReportDto : pointRecordReportDtoList) {

                String pointStr = "> " + pointRecordReportDto.getAccountName() + " "
                        + pointRecordReportDto.getPhone() + " 点评数:" + pointRecordReportDto.getNum() + "\n\n";
                text.append(pointStr);
            }
        } else {
            text.append("> 无\n\n");
        }
        text.append("### 测验 \n\n");
        if (!CollectionUtils.isEmpty(gameRecordReportDtoList)) {

            Comparator<GameRecordReportDto> num = Comparator.comparing(GameRecordReportDto::getNum);
            ListUtil.sort(gameRecordReportDtoList, num.reversed());

            for (GameRecordReportDto gameRecordReportDto : gameRecordReportDtoList) {
                String gameStr = "> " + gameRecordReportDto.getAccountName() + " "
                        + gameRecordReportDto.getPhone() + " 测验数:" + gameRecordReportDto.getNum() + "\n\n";
                text.append(gameStr);
            }
        } else {
            text.append("> 无\n\n");
        }

        text.append("### 访问 \n");
        if (!CollectionUtils.isEmpty(behaviorReportDtoList)) {

            Comparator<BehaviorReportDto> num = Comparator.comparing(BehaviorReportDto::getNum);
            ListUtil.sort(behaviorReportDtoList, num.reversed());

            for (BehaviorReportDto behaviorReportDto : behaviorReportDtoList) {
                String accessStr = "> " + behaviorReportDto.getAccountName() + " "
                        + behaviorReportDto.getPhone() + " 访问数:" + behaviorReportDto.getNum() + "\n\n";
                text.append(accessStr);
            }
        } else {
            text.append("> 无\n\n");
        }

        return text.toString();

    }


}

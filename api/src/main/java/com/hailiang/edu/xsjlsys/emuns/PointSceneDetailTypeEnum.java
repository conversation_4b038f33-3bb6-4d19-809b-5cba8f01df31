package com.hailiang.edu.xsjlsys.emuns;

import lombok.RequiredArgsConstructor;

/**
 * 积分变动场景颁发类型
 * 枚举类
 */
@RequiredArgsConstructor
public enum PointSceneDetailTypeEnum {
    EXCHANGE_CABINET(21, "积分柜兑换"),
    EXCHANGE_OFFLINE(22, "线下兑换"),
    ;

    private final Integer code;
    private final String name;


    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.name;
    }

    public static String getMessageByCode(Integer code) {
        for (PointSceneDetailTypeEnum activityTypeEnum : PointSceneDetailTypeEnum.values()) {
            if (code.equals(activityTypeEnum.getCode())) {
                return activityTypeEnum.getMessage();
            }
        }
        return null;
    }

    public static Integer getCodeByMessage(String message) {
        for (PointSceneDetailTypeEnum activityTypeEnum : PointSceneDetailTypeEnum.values()) {
            if (message.equals(activityTypeEnum.getMessage())) {
                return activityTypeEnum.getCode();
            }
        }
        return null;
    }
}

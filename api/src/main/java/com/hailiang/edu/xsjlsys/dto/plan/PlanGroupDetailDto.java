package com.hailiang.edu.xsjlsys.dto.plan;

import com.alibaba.fastjson.annotation.JSONField;
import com.hailiang.edu.xsjlsys.consts.AvatarDataTypeConst;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 方案分组详情模型
 */
@Data
public class PlanGroupDetailDto {

    //分组列表
    @JSONField(ordinal = 1)
    private List<GroupDto> groupList;

    private boolean temperatureEnable;

    @Data
    public static class GroupDto {

        @JSONField(ordinal = 1)
        private Long groupId;

        @JSONField(ordinal = 2)
        private String groupName;

        @JSONField(ordinal = 3)
        private String icon;

        private Integer iconType;

        @JSONField(ordinal = 4)
        private List<StudentDto> studentList = new ArrayList<>();

        @JSONField(ordinal = 5)
        private BigDecimal plusScore;

        @JSONField(ordinal = 6)
        private BigDecimal behaviorPlusScore;

        @JSONField(ordinal = 7)
        private BigDecimal minusScore;

        @JSONField(ordinal = 8)
        private BigDecimal behaviorMinusScore;

        @JSONField(ordinal = 9)
        private BigDecimal avgScore;

        @JSONField(ordinal = 10)
        private BigDecimal sumScore;

        @JSONField(ordinal = 10)
        private BigDecimal coinNum;

        @JSONField(ordinal = 11)
        private List<StudentDto> treeStudentList;

        //是否存在积分记录
        @JSONField(ordinal = 12)
        private Boolean isExistPointRecord;

        @JSONField(ordinal = 13)
        private String awardIconUrl;

        @JSONField(ordinal = 14)
        private String miniAwardIconUrl;

        //excel 导出使用中间字段 mini榜单排序字段
        @JSONField(ordinal = 15)
        private Integer sortVal;

        @JSONField(ordinal = 16)
        private String dataType;

        @JSONField(ordinal = 17)
        private Boolean isException;

    }


    //未分组学生列表
    @JSONField(ordinal = 2)
    private List<UnGroupDto> unGroupList;


    @Data
    public static class UnGroupDto {

        @JSONField(ordinal = 1)
        private Long groupId = -1L;

        @JSONField(ordinal = 2)
        private String groupName = "待分组学生";

        @JSONField(ordinal = 3)
        private String icon = "";

        @JSONField(ordinal = 4)
        private List<StudentDto> studentList = new ArrayList<>();

        @JSONField(ordinal = 5)
        private BigDecimal plusScore;

        @JSONField(ordinal = 6)
        private BigDecimal behaviorPlusScore;

        @JSONField(ordinal = 7)
        private BigDecimal minusScore;

        @JSONField(ordinal = 8)
        private BigDecimal behaviorMinusScore;

        @JSONField(ordinal = 9)
        private BigDecimal avgScore;

        @JSONField(ordinal = 10)
        private BigDecimal sumScore;


        @JSONField(ordinal = 11)
        private List<StudentDto> treeStudentList;

        //是否存在积分记录
        @JSONField(ordinal = 12)
        private Boolean isExistPointRecord;

        //头像数据类型
        @JSONField(ordinal = 13)
        private String dataType = AvatarDataTypeConst.SYSTEM;

        //是否异常
        @JSONField(ordinal = 14)
        private Boolean isException = false;
    }


    //积分比例
    @JSONField(ordinal = 3)
    private Integer pointRetailRate;

    //是否创建人
    @JSONField(ordinal = 4)
    private Boolean isCreator;

    @JSONField(ordinal = 5)
    private Integer planType;

    private List<PlanGroupTemplateTreeDto> templateTree;

    @JSONField(ordinal = 6)
    private String groupType;

    @JSONField(ordinal = 7)
    private Integer groupNum;


    @Data
    public static class StudentDto {

        @JSONField(ordinal = 1)
        private String studentId;


        @JSONField(ordinal = 2)
        private String studentNo;

        @JSONField(ordinal = 3)
        private String studentName;

        @JSONField(ordinal = 4)
        private List<StudentDto> parentList;

        @JSONField(ordinal = 5)
        private List<String> parentIds;

        @JSONField(ordinal = 6)
        private List<StudentDto> childList;

        @JSONField(ordinal = 7)
        private BigDecimal score;

        //正向得分情况
        @JSONField(ordinal = 8)
        private BigDecimal plusScore;

        @JSONField(ordinal = 9)
        private BigDecimal behaviorPlusScore;

        //负向得分情况
        @JSONField(ordinal = 10)
        private BigDecimal minusScore;

        @JSONField(ordinal = 11)
        private BigDecimal behaviorMinusScore;

        //是否存在积分记录
        @JSONField(ordinal = 12)
        private Boolean isExistPointRecord;

        //学生头像
        @JSONField(ordinal = 13)
        private String url;

        //是否组长 true｜是 false ｜否
        @JSONField(ordinal = 14)
        private Boolean isGroupLeader;

        //是否小组长 true｜是 false ｜ 否
        @JSONField(ordinal = 15)
        private Boolean isSmallGroupLeader;

        @JSONField(ordinal = 16)
        private String awardIconUrl;

        @JSONField(ordinal = 17)
        private String namePinYin;

        @JSONField(ordinal = 18)
        private String miniAwardIconUrl;

        //排序使用而已
        private String pinyinStr;

        //excel导出用到 分类分数
        private Map<String, BigDecimal> tagScoreMap;

        //excel 导出使用中间字段 mini榜单排序
        @JSONField(ordinal = 19)
        private Integer sortVal;

        //获取分层学生数据用到
        private Integer rangeLevel;

        @JSONField(ordinal = 20)
        private String dataType;

        private Integer iconType;

        private Integer temperatureVal;
        // 序号
        private String sequenceNumber;

        /**
         * 金币数
         */
        private BigDecimal coinNum = BigDecimal.ZERO;

        /**
         * 金余额
         */
        private BigDecimal coinBalance = BigDecimal.ZERO;
    }

    /**
     * 排序字段
     */
    @JSONField(ordinal = 8)
    private SortBy sortBy;

    @Data
    public static class SortBy {

        String code;
        String type;
    }

    //异常分组列表
    @JSONField(ordinal = 9)
    private List<ExceptionGroup> exceptionGroupList;

    @Data
    public static class ExceptionGroup {

        @JSONField(ordinal = 1)
        private Long groupId;

        @JSONField(ordinal = 2)
        private String groupName;
    }

}

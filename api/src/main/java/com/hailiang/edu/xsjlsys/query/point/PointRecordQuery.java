package com.hailiang.edu.xsjlsys.query.point;

import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Data
public class PointRecordQuery {

    private String saasClassId;

    private String saasSchoolId;

    private Long planId;

    private Long userId;

    private Set<Long> userIds;

    private String startTime;

    private String endTime;

    private String scene;

    private Long gameRecordId;

    private Long exceptGameRecordId;

    private Set<Long> studentIds;

    private List<String> tagNameList;

    private Boolean isPlusScore;

    private Boolean isMinusScore;

    private List<Long> exceptChannelIds;

    private Collection<Long> planIds;

    private Long studentId;

    private int channel;

    private Integer deleted;

    private String ltUpdateTime;

    private Long pointRecordId;

    private Long thirdBusinessId;

    private Long startPointRecordId;

    /**
     * 是否新校区
     */
    private Boolean isNewCoinCampus = false;

}

package com.hailiang.edu.xsjlsys.component.oss;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.hailiang.base.exception.BusinessException;
import com.hailiang.edu.xsjlsys.dto.oss.StsTokenDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class OssComponent {

    @Value("${oss.access.keyId}")
    private String keyId;

    @Value("${oss.access.keySecret}")
    private String keySecret;

    @Value("${oss.access.accountId}")
    private String accountId;

    @Value("${oss.access.ramRoleName}")
    private String ramRoleName;

    @Value("${oss.access.bucket}")
    private String bucket;

    @Value("${oss.access.region}")
    private String region;


    public StsTokenDto getStsToken() {

        AssumeRoleResponse ossToken = createOssToken();
        if (null == ossToken) {
            throw new BusinessException("token获取异常");
        }

        AssumeRoleResponse.Credentials credentials = ossToken.getCredentials();

        StsTokenDto stsTokenDto = new StsTokenDto();
        stsTokenDto.setAccessKeyId(credentials.getAccessKeyId());
        stsTokenDto.setAccessKeySecret(credentials.getAccessKeySecret());
        stsTokenDto.setSecurityToken(credentials.getSecurityToken());
        stsTokenDto.setBucket(bucket);
        stsTokenDto.setRegion(region);

        return stsTokenDto;

    }


    private AssumeRoleResponse createOssToken() {
        try {
            String roleArn = "acs:ram::" + accountId + ":role/" + ramRoleName;
            DefaultProfile.addEndpoint("", "cn-hangzhou", "Sts", "sts.aliyuncs.com");
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", keyId, keySecret);
            DefaultAcsClient client = new DefaultAcsClient(profile);
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setMethod(MethodType.POST);
            request.setRoleSessionName(ramRoleName);
            request.setDurationSeconds(3600L);
            request.setRoleArn(roleArn);
            request.setProtocol(ProtocolType.HTTPS);
            return client.getAcsResponse(request);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public String text(){
        return "123";
    }



}

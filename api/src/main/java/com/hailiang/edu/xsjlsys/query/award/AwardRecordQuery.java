package com.hailiang.edu.xsjlsys.query.award;

import com.hailiang.edu.xsjlsys.query.common.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
public class AwardRecordQuery extends BaseQuery {

    private Long planId;

    private Long awardRecordId;

    private Long awardRuleId;

    /**
     * 时间是否重叠，1是 0不是
     */
    private Integer isDuplicateTime;

    private String startTime;

    private String endTime;

    private Long userId;

    private String saasClassId;

    private Integer includeDeleted;

    private Collection<Long> planIds;
}

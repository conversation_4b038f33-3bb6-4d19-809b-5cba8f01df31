<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsGameDefaultRuleMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsGameDefaultRule">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="game_id" jdbcType="INTEGER" property="gameId" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
    <result column="is_used" jdbcType="BIT" property="isUsed" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, game_id, rule_name, rule_code, score, rule_type, is_used, create_time, update_time
  </sql>




  <select id="getListByGameId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_game_default_rule
    where 1=1
    and is_used = 1
    and game_id = #{gameId}
    group by id
    order by id asc
  </select>

</mapper>
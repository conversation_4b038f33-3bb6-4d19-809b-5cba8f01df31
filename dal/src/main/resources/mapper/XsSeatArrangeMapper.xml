<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsSeatArrangeMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsSeatArrange">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="seat_arrange_id" jdbcType="BIGINT" property="seatArrangeId"/>
        <result column="x_count" jdbcType="INTEGER" property="xCount"/>
        <result column="y_count" jdbcType="INTEGER" property="yCount"/>
        <result column="hallway_after_ys" jdbcType="VARCHAR" property="hallwayAfterYs"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
        <result column="modifier_id" jdbcType="INTEGER" property="modifierId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        seat_arrange_id, x_count, y_count, hallway_after_ys, is_deleted, saas_class_id, saas_school_id,
        saas_tenant_id, creator_id, modifier_id, create_time, update_time
    </sql>
    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_seat_arrange
        <where>
            <include refid="where"/>
        </where>
    </select>

    <sql id="where">
        and is_deleted = 0
        <if test="row.saasClassId != null and row.saasClassId != ''">
            and saas_class_id = #{row.saasClassId}
        </if>
    </sql>
</mapper>
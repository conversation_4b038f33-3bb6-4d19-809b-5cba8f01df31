<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsInviteRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsInviteRecord">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="invite_record_id" jdbcType="INTEGER" property="inviteRecordId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="invite_user_id" jdbcType="INTEGER" property="inviteUserId"/>
        <result column="invite_status" jdbcType="VARCHAR" property="inviteStatus"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        invite_record_id, user_id, invite_user_id, invite_status, plan_id, is_deleted, saas_class_id,
        saas_school_id, saas_tenant_id, create_time, update_time
    </sql>


    <select id="getListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_invite_record
        <where>
            <include refid="where"/>
        </where>
    </select>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_invite_record
        <where>
            <include refid="where"/>
        </where>
        limit 1
    </select>

    <select id="getListCount" resultType="java.lang.Integer">
        select count(*) from (
        select
        invite_record_id
        from xs_invite_record
        <where>
            <include refid="where"/>
        </where>
        ) as invite_record
    </select>

    <select id="getPageListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_invite_record
        <where>
            <include refid="where"/>
        </where>
        order by create_time desc
        limit #{offset},#{limit}
    </select>

    <sql id="where">
        is_deleted = 0
        <if test="row.inviteRecordId != null">
            and invite_record_id = #{row.inviteRecordId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.inviteUserIds != null and row.inviteUserIds.size() > 0">
            and invite_user_id in
            <foreach collection="row.inviteUserIds" item="inviteUserId" index="index"
                     open="(" close=")" separator=",">
                #{inviteUserId}
            </foreach>
        </if>
        <if test="row.inviteStatus != null">
            and invite_status = #{row.inviteStatus}
        </if>
    </sql>
</mapper>
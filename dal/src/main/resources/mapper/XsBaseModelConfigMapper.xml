<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsBaseModelConfigMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsBaseModelConfig">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="config_type" jdbcType="VARCHAR" property="configType" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="key_value" jdbcType="REAL" property="keyValue" />
  </resultMap>

  <sql id="Base_Column_List">
          id, config_type, keyword, key_value
  </sql>

</mapper>
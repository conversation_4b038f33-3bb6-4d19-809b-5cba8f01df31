<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsPlanTimeAimMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsPlanTimeAim">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="aim_time" jdbcType="TIMESTAMP" property="aimTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, plan_id, user_id, aim_time, is_deleted, create_time, update_time
  </sql>


  <select id="getLatest" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_plan_time_aim
    where is_deleted = 0
    <if test="planId != null">
      and plan_id = #{planId}
    </if>
    order by aim_time desc
    limit 1
  </select>

</mapper>
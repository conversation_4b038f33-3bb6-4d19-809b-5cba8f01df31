<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsSmsReportStudentMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsSmsReportStudent">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sms_report_id" jdbcType="BIGINT" property="smsReportId"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="is_view" jdbcType="BIT" property="isView"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>


        <association property="xsSmsReport" javaType="com.hailiang.edu.xsjlsys.dal.entity.XsSmsReport">
            <result column="sms_report_id" jdbcType="BIGINT" property="smsReportId"/>
            <result column="user_id" jdbcType="INTEGER" property="userId"/>
            <result column="send_hour" jdbcType="INTEGER" property="sendHour"/>
            <result column="send_minute" jdbcType="INTEGER" property="sendMinute"/>
            <result column="sms_rule_id" jdbcType="INTEGER" property="smsRuleId"/>
            <result column="sms_content_range" jdbcType="VARCHAR" property="smsContentRange"/>
            <result column="sms_rule_meta_data" jdbcType="VARCHAR" property="smsRuleMetaData"/>
            <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
            <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
            <result column="srcreate_time" jdbcType="TIMESTAMP" property="createTime"/>
        </association>

    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, sms_report_id, student_id, is_view, is_deleted, saas_class_id, saas_school_id, saas_tenant_id,
        create_time, update_time
    </sql>

    <sql id="where">
        srs.is_deleted = 0
        and sr.is_deleted = 0
        <if test="row.studentId != null">
            and srs.student_id = #{row.studentId}
        </if>
        <if test="row.lastSmsReportId != null and row.lastSmsReportId != 0">
            and sr.sms_report_id &lt; #{row.lastSmsReportId}
        </if>
        <if test="row.smsReportId != null">
            and sr.sms_report_id = #{row.smsReportId}
        </if>
    </sql>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select * from (
        select
        srs.*,sr.user_id,sr.send_hour,sr.send_minute,sr.sms_rule_id,sr.sms_content_range,sr.start_time,sr.end_time,sr.sms_rule_meta_data
        from xs_sms_report_student as srs
        inner join xs_sms_report as sr on sr.sms_report_id = srs.sms_report_id
        <where>
            <include refid="where"/>
        </where>
        group by srs.sms_report_id
        ) as p
        limit 1
    </select>

    <select id="getListByCondition" resultMap="BaseResultMap">
        select * from (
        select
        srs.*,sr.user_id,sr.send_hour,sr.send_minute,sr.sms_rule_id,sr.sms_content_range,sr.start_time,sr.end_time,sr.create_time
        as srcreate_time
        from xs_sms_report_student as srs
        inner join xs_sms_report as sr on sr.sms_report_id = srs.sms_report_id
        <where>
            <include refid="where"/>
        </where>
        group by srs.sms_report_id
        ) as p
        <if test="row.sortCriteria != null and row.sortCriteria != ''">
            order by ${row.sortCriteria}
        </if>
        <if test="row.pageSize != null and row.pageSize != 0">
            limit #{row.pageSize}
        </if>
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsPointRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsPointRecord">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="account_name" jdbcType="VARCHAR" property="accountName"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="scene" jdbcType="VARCHAR" property="scene"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="plan_comment_id" jdbcType="VARCHAR" property="planCommentId"/>
        <result column="plan_comment_content" jdbcType="VARCHAR" property="planCommentContent"/>
        <result column="plan_tag_id" jdbcType="INTEGER" property="planTagId"/>
        <result column="plan_tag_name" jdbcType="VARCHAR" property="planTagName"/>
        <result column="game_id" jdbcType="INTEGER" property="gameId"/>
        <result column="game_name" jdbcType="VARCHAR" property="gameName"/>
        <result column="game_record_id" jdbcType="INTEGER" property="gameRecordId"/>
        <result column="game_record_title" jdbcType="VARCHAR" property="gameRecordTitle"/>
        <result column="redemption_record_id" jdbcType="VARCHAR" property="redemptionRecordId"/>
        <result column="module_code" jdbcType="INTEGER" property="moduleCode"/>
        <result column="apply_level" jdbcType="VARCHAR" property="applyLevel"/>
        <result column="subject_code" jdbcType="VARCHAR" property="subjectCode"/>
        <result column="third_business_id" jdbcType="INTEGER" property="thirdBusinessId"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, content, student_id, user_id, account_name, score, plan_id, channel, scene, saas_class_id,
        saas_school_id, saas_tenant_id, is_deleted, create_time, update_time, plan_comment_id,
        plan_comment_content, plan_tag_id, plan_tag_name, game_id, game_name, game_record_id,
        game_record_title, module_code, apply_level,third_business_id
    </sql>

    <sql id="Simple_Column_List">
        id, student_id, score,channel
    </sql>


    <insert id="insertBatch" parameterType="java.util.List">
        insert into xs_point_record (id, content, student_id, user_id,
        account_name, score, plan_id,
        channel, scene, saas_class_id,
        saas_school_id, saas_tenant_id, is_deleted,
        create_time, update_time, plan_comment_id,
        plan_comment_content, plan_tag_id, plan_tag_name,
        game_id, game_name, game_record_id,game_record_title,redemption_record_id,module_code,apply_level,
        subject_code,third_business_id)
        values
        <foreach collection="xsPointRecordList" item="xsPointRecord" index="index" separator=",">
            (
            #{xsPointRecord.id},
            #{xsPointRecord.content},
            #{xsPointRecord.studentId},
            #{xsPointRecord.userId},
            #{xsPointRecord.accountName},
            #{xsPointRecord.score},
            #{xsPointRecord.planId},
            #{xsPointRecord.channel},
            #{xsPointRecord.scene},
            #{xsPointRecord.saasClassId},
            #{xsPointRecord.saasSchoolId},
            #{xsPointRecord.saasTenantId},
            #{xsPointRecord.isDeleted},
            #{xsPointRecord.createTime},
            #{xsPointRecord.updateTime},
            #{xsPointRecord.planCommentId},
            #{xsPointRecord.planCommentContent},
            #{xsPointRecord.planTagId},
            #{xsPointRecord.planTagName},
            #{xsPointRecord.gameId},
            #{xsPointRecord.gameName},
            #{xsPointRecord.gameRecordId},
            #{xsPointRecord.gameRecordTitle},
            #{xsPointRecord.redemptionRecordId},
            #{xsPointRecord.moduleCode},
            #{xsPointRecord.applyLevel},
            #{xsPointRecord.subjectCode},
            #{xsPointRecord.thirdBusinessId}
            )
        </foreach>
    </insert>
    <insert id="bakByCondition">
        insert into xs_point_record_del_bak
        select
        *
        from xs_point_record
        <where>
            <include refid="bakAndDelWhere"/>
        </where>
    </insert>

    <delete id="delByCondition">
        delete from xs_point_record
        <where>
            <include refid="bakAndDelWhere"/>
        </where>
    </delete>

    <sql id="bakAndDelWhere">
        <if test="row.deleted != null">
            and is_deleted = #{row.deleted}
        </if>
        <if test="row.ltUpdateTime != null and row.ltUpdateTime != ''">
            and update_time &lt; #{row.ltUpdateTime}
        </if>
        <if test="row.planIds != null">
            <if test="row.planIds.size() > 0">
                and plan_id in
                <foreach collection="row.planIds" item="planId" open="(" close=")" separator=",">
                    #{planId}
                </foreach>
            </if>
            <if test="row.planIds.size() == 0">
                and 1 = 0
            </if>
        </if>
    </sql>


    <select id="getListByPointRecordQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        <where>
            <include refid="where"/>
        </where>

    </select>

    <select id="getTagNamesByPointRecord" resultType="java.lang.String">
        select
        distinct plan_tag_name
        from xs_point_record
        <where>
            <include refid="where"/>
        </where>
    </select>

    <select id="getUsedPlanIds" resultType="java.lang.Long">
        select
        distinct plan_id
        from xs_point_record
        <where>
            <include refid="where"/>
        </where>
    </select>

    <select id="getSimpleListByPointRecordQuery" resultMap="BaseResultMap">
        select
        <include refid="Simple_Column_List"/>
        from xs_point_record
        <where>
            <include refid="where"/>
        </where>
    </select>

    <resultMap id="getStudentSumScoreResult" type="com.hailiang.edu.xsjlsys.dto.plan.StudentSumScoreDto">
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
    </resultMap>

    <select id="getStudentSumScoreByPointRecordQuery" resultMap="getStudentSumScoreResult">
        select student_id,sum(score) as score
        from xs_point_record
        <where>
            <include refid="where"/>
        </where>
        group by student_id
    </select>



    <select id="getListByQuery" resultMap="BaseResultMap">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="row.startTime != null">
            and create_time &gt;= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.userIds != null and row.userIds.size() > 0">
            and user_id in
            <foreach collection="row.userIds" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="row.planTagIds != null and row.planTagIds.size() > 0">
            and plan_tag_id in
            <foreach collection="row.planTagIds" item="planTagId" index="index"
                     open="(" close=")" separator=",">
                #{planTagId}
            </foreach>
        </if>
        <if test="row.isExceptMinusScore != null and row.isExceptMinusScore == true">
            and score &gt; 0
        </if>
        order by id+1 desc
        limit #{offset},#{pageSize}
    </select>


    <select id="getListByScoreQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="row.startTime != null">
            and create_time &gt;= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>

        <if test="row.planIds != null and row.planIds.size() > 0">
            and plan_id in
            <foreach collection="row.planIds" item="planId" index="index"
                     open="(" close=")" separator=",">
                #{planId}
            </foreach>
        </if>

        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>

        <if test="row.studentId != null">
            and student_id = #{row.studentId}
        </if>

        <if test="row.userIds != null and row.userIds.size() > 0">
            and user_id in
            <foreach collection="row.userIds" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        <if test="row.planTagIds != null and row.planTagIds.size() > 0">
            and plan_tag_id in
            <foreach collection="row.planTagIds" item="planTagId" index="index"
                     open="(" close=")" separator=",">
                #{planTagId}
            </foreach>
        </if>

        <if test="row.exceptChannelIds != null and row.exceptChannelIds.size() > 0">
            and !(1!=1
            <foreach collection="row.exceptChannelIds" item="exceptChannelId" index="index">
                or channel = #{exceptChannelId}
            </foreach>
            )
        </if>

        <if test="row.isExceptMinusScore != null and row.isExceptMinusScore == true">
            and score &gt; 0
        </if>
        <if test="row.orderByCreateTime != null and row.orderByCreateTime == true">
            order by id+1 desc
        </if>
    </select>


    <select id="getListCount" resultType="java.lang.Integer">
        select count(*) from (
        select id from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>

        <if test="row.userIds != null and row.userIds.size() > 0">
            and user_id in
            <foreach collection="row.userIds" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        <if test="row.studentIds != null and row.studentIds.size() > 0">
            and student_id in
            <foreach collection="row.studentIds" item="studentId" index="index"
                     open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>

        <if test="row.tagNameList != null and row.tagNameList.size() > 0">
            and (1!=1
            <foreach collection="row.tagNameList" item="tagName" index="index">
                or plan_tag_name = #{tagName}
            </foreach>
            )
        </if>

        <if test="row.isPlusScore != null and row.isPlusScore == true">
            and score &gt;= 0
        </if>
        <if test="row.isMinusScore != null and row.isMinusScore == true">
            and score &lt; 0
        </if>

        <if test="row.startTime != null">
            and create_time >= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>

        <if test="row.exceptChannelIds != null and row.exceptChannelIds.size() > 0">
            and !(1!=1
            <foreach collection="row.exceptChannelIds" item="exceptChannelId" index="index">
                or channel = #{exceptChannelId}
            </foreach>
            )
        </if>
        <if test="row.startPointRecordId != null">
            and id >= #{row.startPointRecordId}
        </if>

        group by id
        ) as point_record
    </select>

    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>

        <if test="row.userIds != null and row.userIds.size() > 0">
            and user_id in
            <foreach collection="row.userIds" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

        <if test="row.studentIds != null and row.studentIds.size() > 0">
            and student_id in
            <foreach collection="row.studentIds" item="studentId" index="index"
                     open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>

        <if test="row.tagNameList != null and row.tagNameList.size() > 0">
            and (1!=1
            <foreach collection="row.tagNameList" item="tagName" index="index">
                or plan_tag_name = #{tagName}
            </foreach>
            )
        </if>

        <if test="row.isPlusScore != null and row.isPlusScore == true">
            and score &gt;= 0
        </if>
        <if test="row.isMinusScore != null and row.isMinusScore == true">
            and score &lt; 0
        </if>

        <if test="row.startTime != null">
            and create_time >= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>

        <if test="row.exceptChannelIds != null and row.exceptChannelIds.size() > 0">
            and !(1!=1
            <foreach collection="row.exceptChannelIds" item="exceptChannelId" index="index">
                or channel = #{exceptChannelId}
            </foreach>
            )
        </if>

        <if test="row.startPointRecordId != null">
            and id >= #{row.startPointRecordId}
        </if>
        order by id+1 desc
        limit #{offset},#{limit}
    </select>


    <update id="delByStudentIds">
        update xs_point_record
        set is_deleted = 1,
        update_time = #{updateTime}
        where 1=1
        <if test="saasClassId != null">
            and saas_class_id = #{saasClassId}
        </if>
        <if test="studentIds != null and studentIds.size() > 0">
            and student_id in
            <foreach collection="studentIds" item="studentId" index="index"
                     open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>

    </update>


    <update id="delByIds">
        update xs_point_record
        set is_deleted = 1,
        update_time = #{updateTime}
        where 1=1
        <if test="xslRecordIds != null and xslRecordIds.size() > 0">
            and id in
            <foreach collection="xslRecordIds" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="thirdRecordIds != null and thirdRecordIds.size() > 0">
            and third_business_id in
            <foreach collection="thirdRecordIds" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

    </update>

    <resultMap id="getDayStatResult" type="com.hailiang.edu.xsjlsys.dto.report.PointRecordReportDto">
        <result column="time" jdbcType="VARCHAR" property="time"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="account_name" jdbcType="VARCHAR" property="accountName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="num" jdbcType="INTEGER" property="num"/>
    </resultMap>

    <select id="getDayStatByRangeTime" resultMap="getDayStatResult">
        SELECT b.time, b.user_id, b.account_name, b.phone, count(*) as num
        from (
        SELECT pr.user_id, pr.account_name, date (pr.create_time) as time,u.phone
        from
        xs_point_record as pr
        inner join xs_user as u
        on u.user_id = pr.user_id
        where 1=1
        and pr.create_time >= #{row.startTime}
        and pr.create_time &lt;= #{row.endTime}
        and pr.is_deleted = 0 ) as b
        group by b.time, b.user_id
        ORDER BY b.time, b.user_id

    </select>


    <select id="getListByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        group by id
    </select>

    <select id="getListByStudentIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="studentIds != null and studentIds.size() > 0">
            and student_id in
            <foreach collection="studentIds" item="studentId" index="index"
                     open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>
        group by id
    </select>


    <resultMap id="getCommentGroupResult" type="com.hailiang.edu.xsjlsys.dto.plan.CommentSortDto">
        <result column="plan_comment_id" jdbcType="BIGINT" property="planCommentId"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
    </resultMap>

    <select id="getListGroupCountByCondition" resultMap="getCommentGroupResult">
        SELECT plan_comment_id,plan_tag_id,count(*) as count
        from xs_point_record
        where 1=1
        and is_deleted = 0
        and channel = 1
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.isPlusScore != null and row.isPlusScore == true">
            and score &gt;= 0
        </if>
        <if test="row.isMinusScore != null and row.isMinusScore == true">
            and score &lt; 0
        </if>
        <if test="row.startTime != null and row.startTime != ''">
            and create_time &gt;= #{row.startTime}
        </if>
        <if test="row.endTime != null and row.endTime != ''">
            and create_time &lt;= #{row.endTime}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        and plan_comment_id > 0
        group by plan_comment_id,plan_tag_id
        order by count desc
    </select>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        <where>
            <if test="row.pointRecordId != null">
                and id = #{row.pointRecordId}
            </if>

            <if test="row.thirdBusinessId != null">
                and third_business_id = #{row.thirdBusinessId}
            </if>
        </where>
        limit 1
    </select>

    <select id="getListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>

        <if test="row.channel != null and row.channel != 0">
            and channel = #{row.channel}
        </if>

        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>

        <if test="row.planIds != null and row.planIds.size() > 0">
            and plan_id in
            <foreach collection="row.planIds" item="planId" index="index"
                     open="(" close=")" separator=",">
                #{planId}
            </foreach>
        </if>

        <if test="row.scene != null">
            and scene = #{row.scene}
        </if>
        <if test="row.studentId != null">
            and student_id = #{row.studentId}
        </if>
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.startTime != null">
            and create_time &gt;= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>

        <if test="row.exceptChannelIds != null and row.exceptChannelIds.size() > 0">
            and !(1!=1
            <foreach collection="row.exceptChannelIds" item="exceptChannelId" index="index">
                or channel = #{exceptChannelId}
            </foreach>
            )
        </if>
        <if test="row.startPointRecordId != null">
            and id >= #{row.startPointRecordId}
        </if>
    </select>

    <select id="getSimpleColumnListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Simple_Column_List"/>
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>

        <if test="row.channel != null and row.channel != 0">
            and channel = #{row.channel}
        </if>

        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>

        <if test="row.planIds != null and row.planIds.size() > 0">
            and plan_id in
            <foreach collection="row.planIds" item="planId" index="index"
                     open="(" close=")" separator=",">
                #{planId}
            </foreach>
        </if>

        <if test="row.scene != null">
            and scene = #{row.scene}
        </if>
        <if test="row.studentId != null">
            and student_id = #{row.studentId}
        </if>
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.startTime != null">
            and create_time &gt;= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>

        <if test="row.exceptChannelIds != null and row.exceptChannelIds.size() > 0">
            and !(1!=1
            <foreach collection="row.exceptChannelIds" item="exceptChannelId" index="index">
                or channel = #{exceptChannelId}
            </foreach>
            )
        </if>
        <if test="row.startPointRecordId != null">
            and id >= #{row.startPointRecordId}
        </if>
    </select>
    <resultMap id="getStudentPointTimeResult" type="com.hailiang.edu.xsjlsys.dto.plan.StudentPointTimeDto">
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="times" jdbcType="INTEGER" property="times"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
    </resultMap>

    <select id="getStudentPointTimeByCondition" resultMap="getStudentPointTimeResult">
        select
        student_id,
        count(*) as times,
        sum(score) as score
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>

        <if test="row.channel != null and row.channel != 0">
            and channel = #{row.channel}
        </if>

        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>

        <if test="row.planIds != null and row.planIds.size() > 0">
            and plan_id in
            <foreach collection="row.planIds" item="planId" index="index"
                     open="(" close=")" separator=",">
                #{planId}
            </foreach>
        </if>

        <if test="row.scene != null">
            and scene = #{row.scene}
        </if>
        <if test="row.studentId != null">
            and student_id = #{row.studentId}
        </if>
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.startTime != null">
            and create_time &gt;= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>

        <if test="row.exceptChannelIds != null and row.exceptChannelIds.size() > 0">
            and !(1!=1
            <foreach collection="row.exceptChannelIds" item="exceptChannelId" index="index">
                or channel = #{exceptChannelId}
            </foreach>
            )
        </if>
        <if test="row.startPointRecordId != null">
            and id >= #{row.startPointRecordId}
        </if>
        group by student_id
    </select>


    <sql id="where">
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.saasSchoolId != null">
            and saas_school_id = #{row.saasSchoolId}
        </if>
        <if test="row.channel != null and row.channel != 0">
            and channel = #{row.channel}
        </if>
        <if test="row.scene != null">
            and scene = #{row.scene}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.userIds != null and row.userIds.size() > 0">
            and user_id in
            <foreach collection="row.userIds" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="row.planIds != null and row.planIds.size() > 0">
            and plan_id in
            <foreach collection="row.planIds" item="planId" index="index"
                     open="(" close=")" separator=",">
                #{planId}
            </foreach>
        </if>

        <if test="row.studentIds != null and row.studentIds.size() > 0">
            and student_id in
            <foreach collection="row.studentIds" item="studentId" index="index"
                     open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>

        <if test="row.tagNameList != null and row.tagNameList.size() > 0">
            and (1!=1
            <foreach collection="row.tagNameList" item="tagName" index="index">
                or plan_tag_name = #{tagName}
            </foreach>
            )
        </if>

        <if test="row.isPlusScore != null and row.isPlusScore == true">
            and score &gt;= 0
        </if>
        <if test="row.isMinusScore != null and row.isMinusScore == true">
            and score &lt; 0
        </if>
        <if test="row.startTime != null">
            and create_time &gt;= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>

        <if test="row.gameRecordId != null">
            and game_record_id = #{row.gameRecordId}
        </if>
        <if test="row.exceptGameRecordId != null">
            and game_record_id != #{row.exceptGameRecordId}
        </if>

        <if test="row.exceptChannelIds != null and row.exceptChannelIds.size() > 0">
            and !(1!=1
            <foreach collection="row.exceptChannelIds" item="exceptChannelId" index="index">
                or channel = #{exceptChannelId}
            </foreach>
            )
        </if>
    </sql>
</mapper>
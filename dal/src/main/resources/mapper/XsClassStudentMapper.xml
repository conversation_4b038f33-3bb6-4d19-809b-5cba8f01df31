<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsClassStudentMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsClassStudent">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

        <association property="xsStudent" javaType="com.hailiang.edu.xsjlsys.dal.entity.XsStudent">
            <result column="student_id" jdbcType="BIGINT" property="studentId"/>
            <result column="student_no" jdbcType="VARCHAR" property="studentNo"/>
            <result column="student_name" jdbcType="VARCHAR" property="studentName"/>
            <result column="password" jdbcType="VARCHAR" property="password"/>
            <result column="is_leader" jdbcType="BIT" property="isLeader"/>
            <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
            <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        </association>

    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, saas_class_id, student_id, url, saas_school_id, saas_tenant_id, create_time,
        update_time
    </sql>


    <insert id="addBatch" parameterType="com.hailiang.edu.xsjlsys.dal.entity.XsClassStudent">
        insert into xs_class_student (saas_class_id,
        student_id,
        url,
        saas_school_id,
        saas_tenant_id,
        create_time,
        update_time
        )
        values
        <foreach collection="list" item="row" index="index" separator=",">
            (#{row.saasClassId},
            #{row.studentId},
            #{row.url},
            #{row.saasSchoolId},
            #{row.saasTenantId},
            #{row.createTime},
            #{row.updateTime}
            )
        </foreach>
    </insert>


    <select id="getListByClassId" resultMap="BaseResultMap">
        select cs.*,s.student_no,s.student_name,s.`password`,s.is_leader,s.saas_school_id,s.saas_tenant_id
        from xs_class_student as cs
        inner join xs_student as s on s.student_id = cs.student_id
        where 1=1
        and s.is_suspend = 0
        and cs.saas_class_id = #{saasClassId}
        <!--        <if test="saasSchoolId != null">-->
        <!--            and s.saas_school_id = #{saasSchoolId}-->
        <!--        </if>-->
        group by cs.student_id
    </select>


    <delete id="deleteByClassId">
        delete from xs_class_student
        where 1=1
        and saas_class_id = #{saasClassId}
        and student_id in
        <foreach collection="studentIds" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByIds">
        delete from xs_class_student
        where 1=1
        and id in
        <foreach collection="classStudentIds" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>


    <delete id="deleteByStudentIds">
        delete from xs_class_student
        where 1=1
        and student_id in
        <foreach collection="studentIds" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>


    <select id="getListGroupByClassId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_class_student
        where 1=1
        and url = ''
        group by saas_class_id
    </select>

    <select id="getNoUrlListByClassId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_class_student
        where 1=1
        and url = ''
        and saas_class_id = #{saasClassId}
        limit 1000
    </select>


    <insert id="batchUpdate" parameterType="java.util.List">
        insert into xs_class_student (
        id,
        url,
        update_time
        ) values
        <foreach collection="xsClassStudentList" item="xsClassStudent" index="index" separator=",">
            (
            #{xsClassStudent.id},
            #{xsClassStudent.url},
            #{xsClassStudent.updateTime}
            )
        </foreach>
        on duplicate key update url=values(url),update_time=values(update_time);
    </insert>


    <select id="getIdByCondition" resultType="java.lang.Long">
        select
        id
        from xs_class_student
        where 1=1
        and saas_class_id = #{saasClassId}
        and student_id in
        <foreach collection="studentIds" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

</mapper>
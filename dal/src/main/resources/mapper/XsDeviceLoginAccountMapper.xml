<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsDeviceLoginAccountMapper">
    <!-- 结果映射 resultMap-->
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsDeviceLoginAccount">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="deviceId" column="device_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        `id`, `device_id`, `user_id`, `is_deleted`, `create_time`,
        `update_time`
    </sql>

    <select id="getCountByCondition" resultType="java.lang.Integer">
        select count(*) from
        (
        select
        <include refid="Base_Column_List"/>
        from xs_device_login_account
        <where>
            <include refid="where"/>
        </where>
        ) as tg
    </select>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_device_login_account
        <where>
            <include refid="where"/>
        </where>
        limit 1
    </select>

    <select id="getListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_device_login_account
        <where>
            <include refid="where"/>
        </where>
        <if test="row.sortCriteria != null and row.sortCriteria != ''">
            order by ${row.sortCriteria} desc
        </if>
        <if test="row.offset != null and row.limit != null">
            limit #{row.offset},#{row.limit}
        </if>
    </select>

    <sql id="where">
        1=1
        <if test="row.deviceId != null">
            AND device_id = #{row.deviceId}
        </if>
        <if test="row.userId != null">
            AND user_id = #{row.userId}
        </if>
        <if test="row.isDeleted != null">
            AND is_deleted = #{row.isDeleted}
        </if>
    </sql>

    <insert id="insertBatch">
        insert into xs_device_login_account(id, device_id, user_id, is_deleted, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.deviceId}, #{entity.userId}, #{entity.isDeleted}, #{entity.createTime},
            #{entity.updateTime})
        </foreach>
    </insert>


    <update id="updateBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE xs_device_login_account
            <set>
                <if test="item.id != null">
                    id = #{item.id},
                </if>
                <if test="item.deviceId != null">
                    device_id = #{item.deviceId},
                </if>
                <if test="item.userId != null">
                    user_id = #{item.userId},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
            </set>
            WHERE id = #{item.id} AND is_deleted = 0
        </foreach>
    </update>


    <delete id="deleteByDeviceIdAndUserId">
        delete from xs_device_login_account
        where 1=1
        and device_id = #{deviceId}
        <if test="userIds != null and userIds.size() > 0">
            and user_id in
            <foreach collection="userIds" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
    </delete>

    <delete id="deleteOtherDevice">
        delete from xs_device_login_account
        where 1=1
        and device_id != #{deviceId}
        <if test="userIds != null and userIds.size() > 0">
            and user_id in
            <foreach collection="userIds" item="userId" index="index"
                     open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsPlanCommentMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsPlanComment">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="plan_comment_id" jdbcType="INTEGER" property="planCommentId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="plan_tag_id" jdbcType="INTEGER" property="planTagId" />
    <result column="sort_val" jdbcType="INTEGER" property="sortVal" />
    <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="comment_template_id" jdbcType="INTEGER" property="commentTemplateId" />
    <result column="apply_level" jdbcType="VARCHAR" property="applyLevel" />
    <result column="module_code" jdbcType="INTEGER" property="moduleCode"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    plan_comment_id, content, plan_id, user_id, score, plan_tag_id, sort_val, saas_class_id,
    saas_school_id, saas_tenant_id, is_deleted, create_time, update_time, comment_template_id,
    apply_level, module_code
  </sql>



  <insert id="insertBatch" parameterType="java.util.List">
    insert into xs_plan_comment (plan_comment_id,content, plan_id, user_id,
                                 score, plan_tag_id, sort_val,
                                 saas_class_id, saas_school_id, saas_tenant_id,
                                 is_deleted, create_time, update_time,comment_template_id,apply_level,module_code
    ) values
    <foreach collection ="xsPlanCommentList" item="xsPlanComment" index= "index" separator =",">
      (
      #{xsPlanComment.planCommentId},
      #{xsPlanComment.content},
      #{xsPlanComment.planId},
      #{xsPlanComment.userId},
      #{xsPlanComment.score},
      #{xsPlanComment.planTagId},
      #{xsPlanComment.sortVal},
      #{xsPlanComment.saasClassId},
      #{xsPlanComment.saasSchoolId},
      #{xsPlanComment.saasTenantId},
      #{xsPlanComment.isDeleted},
      #{xsPlanComment.createTime},
      #{xsPlanComment.updateTime},
      #{xsPlanComment.commentTemplateId},
      #{xsPlanComment.applyLevel},
      #{xsPlanComment.moduleCode}
      )
    </foreach>

  </insert>



  <select id="getListByRow" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>
    <if test="row.planTagId != null">
      and plan_tag_id = #{row.planTagId}
    </if>
    <if test="row.content != null">
      and content = #{row.content}
    </if>
    order by sort_val asc
  </select>


  <select id="getMaxSort" resultType="java.lang.Long">
    select
    max(sort_val)
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>
    <if test="row.planTagId != null">
      and plan_tag_id = #{row.planTagId}
    </if>
  </select>



  <insert id="resetSortVal" parameterType="java.util.List">
    insert into  xs_plan_comment(plan_comment_id,sort_val,update_time) values
    <foreach collection ="list" item="row" index= "index" separator =",">
      (
      #{row.planCommentId},
      #{row.sortVal},
      #{row.updateTime}
      )
    </foreach >
    on duplicate key update sort_val=values(sort_val),update_time=values(update_time);
  </insert>


  <select id="getRowByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="row.planCommentId != null">
      and plan_comment_id = #{row.planCommentId}
    </if>
    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>
    <if test="row.saasClassId != null">
      and saas_class_id = #{row.saasClassId}
    </if>
    <if test="row.content != null">
      and content = #{row.content}
    </if>
    limit 1
  </select>


  <select id="getRowByPlanIdAndName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="planId != null">
      and plan_id = #{planId}
    </if>
    <if test="content != null">
      and content = #{content}
    </if>

    limit 1
  </select>


  <select id="getRowByNotIdAndName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="planId != null">
      and plan_id = #{planId}
    </if>
    <if test="content != null">
      and content = #{content}
    </if>
    <if test="planCommentId != null">
      and plan_comment_id != #{planCommentId}
    </if>

    limit 1
  </select>

  <insert id="batchUpdateModuleCode" parameterType="java.util.List">
    insert into xs_plan_comment(plan_comment_id,module_code,update_time) values
    <foreach collection="xsPlanCommentList" item="xsPlanComment" index="index" separator=",">
      (
      #{xsPlanComment.planCommentId},
      #{xsPlanComment.moduleCode},
      #{xsPlanComment.updateTime}
      )
    </foreach>
    on duplicate key update module_code=values(module_code),update_time=values(update_time);
  </insert>


  <select id="getListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="row.planCommentId != null">
      and plan_comment_id = #{row.planCommentId}
    </if>
    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>
    <if test="row.saasClassId != null">
      and saas_class_id = #{row.saasClassId}
    </if>
    <if test="row.content != null">
      and content = #{row.content}
    </if>

    <if test="row.planTagIds != null and row.planTagIds.size() > 0">
      and plan_tag_id in
      <foreach collection="row.planTagIds" item="planTagId" index="index"
               open="(" close=")" separator=",">
        #{planTagId}
      </foreach>
    </if>

    <if test="row.commentTemplateIds != null and row.commentTemplateIds.size() > 0">
      and comment_template_id in
      <foreach collection="row.commentTemplateIds" item="commentTemplateId" index="index"
               open="(" close=")" separator=",">
        #{commentTemplateId}
      </foreach>
    </if>
    <if test="row.applyLevel != null">
      and apply_level = #{row.applyLevel}
    </if>
    <if test="row.planCommentIds != null and row.planCommentIds.size() > 0">
      and plan_comment_id in
      <foreach collection="row.planCommentIds" item="planCommentId" index="index"
               open="(" close=")" separator=",">
        #{planCommentId}
      </foreach>
    </if>



    group by plan_comment_id
    order by sort_val asc

  </select>

  <select id="queryOutOffScoreListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="row.saasClassIdList != null">
      and saas_class_id in
      <foreach collection="row.saasClassIdList" item="classId" index="index"
        open="(" close=")" separator=",">
        #{classId}
      </foreach>
    </if>
    <if test="row.maxScore != null and row.minScore != null">
      and score &gt; #{row.maxScore} or score &lt; #{row.minScore}
    </if>
  </select>

  <select id="queryOutOffMinScoreListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="row.saasClassIdList != null">
      and saas_class_id in
      <foreach collection="row.saasClassIdList" item="classId" index="index"
        open="(" close=")" separator=",">
        #{classId}
      </foreach>
    </if>
    <if test="row.minScore != null">
      and score &lt; #{row.minScore}
    </if>

  </select>

  <update id="updateScoreMax">
    update xs_plan_comment
    set score = #{row.maxScore}
    where 1=1
    and is_deleted = 0
    <if test="row.planCommentIdList != null">
      and plan_comment_id in
      <foreach collection="row.planCommentIdList" item="planCommentId" index="index"
        open="(" close=")" separator=",">
        #{planCommentId}
      </foreach>
    </if>
  </update>

  <update id="updateScoreMin">
    update xs_plan_comment
    set score = #{row.minScore}
    where 1=1
    and is_deleted = 0
    <if test="row.planCommentIdList != null">
      and plan_comment_id in
      <foreach collection="row.planCommentIdList" item="planCommentId" index="index"
        open="(" close=")" separator=",">
        #{planCommentId}
      </foreach>
    </if>
  </update>

  <select id="getListIncludeDeleteByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="row.planCommentId != null">
      and plan_comment_id = #{row.planCommentId}
    </if>
    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>
    <if test="row.saasClassId != null">
      and saas_class_id = #{row.saasClassId}
    </if>
    <if test="row.content != null">
      and content = #{row.content}
    </if>

    <if test="row.planTagIds != null and row.planTagIds.size() > 0">
      and plan_tag_id in
      <foreach collection="row.planTagIds" item="planTagId" index="index"
               open="(" close=")" separator=",">
        #{planTagId}
      </foreach>
    </if>

    <if test="row.commentTemplateIds != null and row.commentTemplateIds.size() > 0">
      and comment_template_id in
      <foreach collection="row.commentTemplateIds" item="commentTemplateId" index="index"
               open="(" close=")" separator=",">
        #{commentTemplateId}
      </foreach>
    </if>
    <if test="row.applyLevel != null">
      and apply_level = #{row.applyLevel}
    </if>

    group by plan_comment_id
    order by sort_val asc

  </select>



  <insert id="addBatch" parameterType="java.util.List">
    insert into xs_plan_comment (plan_comment_id,content, plan_id, user_id,
    score, plan_tag_id, sort_val,
    saas_class_id, saas_school_id, saas_tenant_id,
    is_deleted, create_time, update_time,comment_template_id,apply_level, module_code
    ) values
    <foreach collection ="xsPlanCommentList" item="xsPlanComment" index= "index" separator =",">
      (
      #{xsPlanComment.planCommentId},
      #{xsPlanComment.content},
      #{xsPlanComment.planId},
      #{xsPlanComment.userId},
      #{xsPlanComment.score},
      #{xsPlanComment.planTagId},
      #{xsPlanComment.sortVal},
      #{xsPlanComment.saasClassId},
      #{xsPlanComment.saasSchoolId},
      #{xsPlanComment.saasTenantId},
      #{xsPlanComment.isDeleted},
      #{xsPlanComment.createTime},
      #{xsPlanComment.updateTime},
      #{xsPlanComment.commentTemplateId},
      #{xsPlanComment.applyLevel},
      #{xsPlanComment.moduleCode}
      )
    </foreach>

  </insert>

  <select id="getPlanIdByCondition" resultType="java.lang.Long">
    select
    distinct plan_id
    from xs_plan_comment
    where 1=1
    and is_deleted = 0
    <if test="row.applyLevel != null">
      and apply_level = #{row.applyLevel}
    </if>
    <if test="row.saasSchoolId != null">
      and saas_school_id = #{row.saasSchoolId}
    </if>

    <if test="row.saasClassIds != null and row.saasClassIds.size() > 0">
      and saas_class_id in
      <foreach collection="row.saasClassIds" item="saasClassId" index="index"
               open="(" close=")" separator=",">
        #{saasClassId}
      </foreach>
    </if>
    <if test="row.planIds != null and row.planIds.size() > 0">
      and plan_id in
      <foreach collection="row.planIds" item="planId" index="index"
               open="(" close=")" separator=",">
        #{planId}
      </foreach>
    </if>
  </select>


</mapper>
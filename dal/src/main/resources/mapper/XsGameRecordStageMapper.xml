<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsGameRecordStageMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsGameRecordStage">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="stage_no" jdbcType="INTEGER" property="stageNo" />
    <result column="game_record_id" jdbcType="INTEGER" property="gameRecordId" />
    <result column="game_id" jdbcType="INTEGER" property="gameId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.hailiang.edu.xsjlsys.dal.entity.XsGameRecordStage">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <result column="stage_meta_data" jdbcType="LONGVARCHAR" property="stageMetaData" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, stage_no, game_record_id, game_id, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    stage_meta_data
  </sql>





  <select id="getByRow" resultMap="ResultMapWithBLOBs">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from xs_game_record_stage
    where 1=1
    <if test="row.stageNo != null">
      and stage_no = #{row.stageNo}
    </if>
    <if test="row.gameRecordId != null">
      and game_record_id = #{row.gameRecordId}
    </if>
    <if test="row.gameId != null">
      and game_id = #{row.gameId}
    </if>
    order by id desc
  </select>


  <insert id="addBatch" parameterType="java.util.List">
    insert into xs_game_record_stage (stage_no, game_record_id, game_id,
    create_time, update_time, stage_meta_data
    )
    values
    <foreach collection ="list" item="row" index= "index" separator =",">
         (
                #{row.stageNo},
                #{row.gameRecordId},
                #{row.gameId},
                #{row.createTime},
                #{row.updateTime},
                #{row.stageMetaData}
        )
    </foreach>
  </insert>

  <select id="getRowByCondition" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from xs_game_record_stage
    where 1=1
    <if test="row.stageNo != null">
      and stage_no = #{row.stageNo}
    </if>
    <if test="row.gameRecordId != null">
      and game_record_id = #{row.gameRecordId}
    </if>
    <if test="row.gameId != null">
      and game_id = #{row.gameId}
    </if>
    order by id desc
    limit 1
  </select>


</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsEvaluateDetailMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsEvaluateDetail">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="evaluate_detail_id" jdbcType="BIGINT" property="evaluateDetailId"/>
        <result column="evaluate_record_id" jdbcType="BIGINT" property="evaluateRecordId"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="student_name" jdbcType="VARCHAR" property="studentName"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.hailiang.edu.xsjlsys.dal.entity.XsEvaluateDetail">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <result column="ask_data" jdbcType="LONGVARCHAR" property="askData"/>
        <result column="evaluate_data" jdbcType="LONGVARCHAR" property="evaluateData"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        evaluate_detail_id, evaluate_record_id, student_id,student_name, is_deleted, saas_class_id, saas_school_id,
        saas_tenant_id, create_time, update_time
    </sql>
    <sql id="Ask_data_Column">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        ask_data
    </sql>
    <sql id="Evaluate_data_Column">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        evaluate_data
    </sql>


    <select id="getListByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>

        <if test="row.includeEvaluateData != null and row.includeEvaluateData == 1">
            ,
            <include refid="Evaluate_data_Column"/>
        </if>

        FROM xs_evaluate_detail
        <where>
            <include refid="where"/>
        </where>
        order by create_time desc
    </select>

    <sql id="where">
        is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.evaluateRecordId != null">
            and evaluate_record_id = #{row.evaluateRecordId}
        </if>
        <if test="row.studentId != null">
            and student_id = #{row.studentId}
        </if>

        <if test="row.evaluateRecordIds != null">
            <if test="row.evaluateRecordIds.size() == 0">
                and 1 = 0
            </if>
            <if test="row.evaluateRecordIds.size() > 0">
                and evaluate_record_id in
                <foreach collection="row.evaluateRecordIds" separator="," open="(" close=")" item="evaluateRecordId">
                    #{evaluateRecordId}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_evaluate_detail
        <where>
            <include refid="where"/>
        </where>
        limit 1
    </select>


    <insert id="insertBatch" parameterType="com.hailiang.edu.xsjlsys.dal.entity.XsEvaluateDetail">
        insert into xs_evaluate_detail ( evaluate_detail_id, evaluate_record_id, student_id,student_name, is_deleted,
        saas_class_id,
        saas_school_id, saas_tenant_id, create_time, update_time,ask_data,evaluate_data )
        values
        <foreach collection="list" item="row" index="index" separator=",">
            ( #{row.evaluateDetailId},
            #{row.evaluateRecordId},
            #{row.studentId},
            #{row.studentName},
            #{row.isDeleted},
            #{row.saasClassId},
            #{row.saasSchoolId},
            #{row.saasTenantId},
            #{row.createTime},
            #{row.updateTime},
            #{row.askData},
            #{row.evaluateData}
            )
        </foreach>
    </insert>


</mapper>
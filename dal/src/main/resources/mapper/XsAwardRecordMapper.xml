<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsAwardRecordMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsAwardRecord">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="award_record_id" jdbcType="BIGINT" property="awardRecordId" />
    <result column="award_rule_id" jdbcType="BIGINT" property="awardRuleId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="rule_desc" jdbcType="VARCHAR" property="ruleDesc" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="qrcode_url" jdbcType="VARCHAR" property="qrcodeUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    award_record_id, award_rule_id, plan_id, start_time, end_time, user_id, rule_desc, 
    is_deleted, saas_class_id, saas_school_id, saas_tenant_id, create_time, update_time,qrcode_url
  </sql>


  <select id="getCountByCondition" resultType="java.lang.Integer">
    select count(*) from (
        select award_record_id
        from xs_award_record
        <where>
          <include refid="where"/>
        </where>
    ) as award_record
  </select>

  <select id="getPageListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from xs_award_record
    <where>
      <include refid="where"/>
    </where>
    order by create_time desc,award_record_id desc
    limit #{row.offset},#{row.limit}
  </select>

  <sql id="where">

    <choose>
      <when test="row.includeDeleted != null and row.includeDeleted == 1">
        1 = 1
      </when>
      <otherwise>
        is_deleted = 0
      </otherwise>
    </choose>

    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>
    <if test="row.userId != null">
      and user_id = #{row.userId}
    </if>
    <if test="row.awardRuleId != null">
      and award_rule_id = #{row.awardRuleId}
    </if>
    <if test="row.awardRecordId != null">
      and award_record_id = #{row.awardRecordId}
    </if>

    <if test="row.isDuplicateTime != null and row.isDuplicateTime == 1">
        and (
              (start_time between #{row.startTime} and #{row.endTime}
              or end_time between #{row.startTime} and #{row.endTime})
            or
              (#{row.startTime} between start_time and end_time
              or #{row.endTime} between start_time and end_time)
        )
    </if>

    <if test="row.saasClassId != null">
      and saas_class_id = #{row.saasClassId}
    </if>

    <if test="row.planIds != null and row.planIds.size() > 0">
      and plan_id in
      <foreach collection="row.planIds" item="planId" index="index"
               open="(" close=")" separator=",">
        #{planId}
      </foreach>
    </if>

  </sql>

  <sql id="sort">
    <if test="row.sortCriteria != null and row.sortCriteria != ''">
      order by ${row.sortCriteria}
    </if>
  </sql>


  <select id="getRowByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from xs_award_record
    <where>
      <include refid="where"/>
    </where>
    limit 1
  </select>

  <select id="getListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from xs_award_record
    <where>
      <include refid="where"/>
    </where>
    <include refid="sort"/>
  </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsCommentTemplateMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsCommentTemplate">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="comment_template_id" jdbcType="INTEGER" property="commentTemplateId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="is_enabled" jdbcType="BIT" property="isEnabled" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_school_name" jdbcType="VARCHAR" property="saasSchoolName" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    comment_template_id, user_id, template_name, is_enabled, saas_school_id, saas_school_name, 
    saas_tenant_id, is_deleted, create_time, update_time
  </sql>

  <select id="getListByCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from xs_comment_template
    where 1=1
    and is_deleted = 0
    <if test="row.saasSchoolId != null">
      and saas_school_id = #{row.saasSchoolId}
    </if>
    <if test="row.isEnabled != null">
      and is_enabled = #{row.isEnabled}
    </if>
    <if test="row.commentTemplateId != null">
      and comment_template_id = #{row.commentTemplateId}
    </if>
    <if test="row.commentTemplateIds != null and row.commentTemplateIds.size() > 0">
      and comment_template_id in
      <foreach collection="row.commentTemplateIds" item="planTagId" index="index"
               open="(" close=")" separator=",">
        #{planTagId}
      </foreach>
    </if>
    <if test="row.neqCommentTemplateId != null">
      and comment_template_id != #{row.neqCommentTemplateId}
    </if>
    order by create_time desc
  </select>

  <select id="getRowByCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from xs_comment_template
    where 1=1
    and is_deleted = 0
    <if test="row.saasSchoolId != null">
      and saas_school_id = #{row.saasSchoolId}
    </if>
    <if test="row.isEnabled != null">
      and is_enabled = #{row.isEnabled}
    </if>
    <if test="row.commentTemplateId != null">
      and comment_template_id = #{row.commentTemplateId}
    </if>
    limit 1
  </select>


</mapper>
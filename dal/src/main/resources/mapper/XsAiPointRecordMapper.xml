<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsAiPointRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsAiPointRecord">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="point_record_id" jdbcType="BIGINT" property="pointRecordId"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="is_confirm" jdbcType="BIT" property="isConfirm"/>
        <result column="evaluate" jdbcType="VARCHAR" property="evaluate"/>
        <result column="batch_id" jdbcType="BIGINT" property="batchId"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, point_record_id, plan_id, is_confirm, evaluate, batch_id, is_deleted, create_time,
        update_time
    </sql>

    <insert id="batchInsert">
        INSERT INTO xs_ai_point_record (
        id,
        point_record_id,
        plan_id,
        is_confirm,
        evaluate,
        batch_id,
        is_deleted,
        create_time,
        update_time
        )
        VALUES
        <foreach item="row" collection="list" separator=",">
            (#{row.id},
            #{row.pointRecordId},
            #{row.planId},
            #{row.isConfirm},
            #{row.evaluate},
            #{row.batchId},
            #{row.isDeleted},
            #{row.createTime},
            #{row.updateTime})
        </foreach>
    </insert>


    <select id="getListByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM xs_ai_point_record
        <where>
            <include refid="where"/>
        </where>
        order by create_time desc
    </select>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM xs_ai_point_record
        <where>
            <include refid="where"/>
        </where>
        order by id asc
        limit 1
    </select>

    <select id="getRowByConditionIncludeDelete" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM xs_ai_point_record
        where 1=1
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.isConfirm != null">
            and is_confirm = #{row.isConfirm}
        </if>
        <if test="row.pointRecordIds != null and row.pointRecordIds.size() > 0">
            and point_record_id in
            <foreach collection="row.pointRecordIds" item="pointRecordId" index="index"
                     open="(" close=")" separator=",">
                #{pointRecordId}
            </foreach>
        </if>
        order by id asc
        limit 1
    </select>

    <sql id="where">
        is_deleted = 0
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.isConfirm != null">
            and is_confirm = #{row.isConfirm}
        </if>
        <if test="row.pointRecordIds != null and row.pointRecordIds.size() > 0">
            and point_record_id in
            <foreach collection="row.pointRecordIds" item="pointRecordId" index="index"
                     open="(" close=")" separator=",">
                #{pointRecordId}
            </foreach>
        </if>
    </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsStudentMasterSlaveMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsStudentMasterSlave">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="student_id" jdbcType="BIGINT" property="studentId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, student_id, parent_id, plan_id, group_id, saas_class_id, saas_school_id, saas_tenant_id, 
    create_time, update_time
  </sql>






  <select id="getListByPlanId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_student_master_slave
    where 1=1
    and plan_id = #{planId}
  </select>

  <delete id="delByPlanId">
        delete from xs_student_master_slave
        where 1=1
        and plan_id = #{planId}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into xs_student_master_slave (student_id, parent_id, plan_id,
    group_id, saas_class_id, saas_school_id,
    saas_tenant_id, create_time, update_time
    )
    values
    <foreach collection ="xsStudentMasterSlaveList" item="xsStudentMasterSlave" index= "index" separator =",">
      (
      #{xsStudentMasterSlave.studentId},
      #{xsStudentMasterSlave.parentId},
      #{xsStudentMasterSlave.planId},
      #{xsStudentMasterSlave.groupId},
      #{xsStudentMasterSlave.saasClassId},
      #{xsStudentMasterSlave.saasSchoolId},
      #{xsStudentMasterSlave.saasTenantId},
      #{xsStudentMasterSlave.createTime},
      #{xsStudentMasterSlave.updateTime}
      )
    </foreach>
  </insert>


  <delete id="delByStudentIds">
    delete from xs_student_master_slave
    where 1=1
    <if test="saasClassId != null">
      and saas_class_id = #{saasClassId}
    </if>
    <if test="studentIds != null and studentIds.size() > 0">
      and (
      student_id in
      <foreach collection="studentIds" item="studentId" index="index"
               open="(" close=")" separator=",">
        #{studentId}
      </foreach>
      or parent_id in
      <foreach collection="studentIds" item="studentId" index="index"
               open="(" close=")" separator=",">
        #{studentId}
      </foreach>
      )
    </if>
  </delete>

</mapper>
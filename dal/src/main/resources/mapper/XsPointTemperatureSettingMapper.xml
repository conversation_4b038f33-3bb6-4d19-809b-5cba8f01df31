<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsPointTemperatureSettingMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsPointTemperatureSetting">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="point_temperature_id" jdbcType="BIGINT" property="pointTemperatureId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="min_val" jdbcType="INTEGER" property="minVal"/>
        <result column="max_val" jdbcType="INTEGER" property="maxVal"/>
        <result column="interval_day" jdbcType="INTEGER" property="intervalDay"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="BIGINT" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="BIGINT" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="BIGINT" property="saasTenantId"/>
        <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
        <result column="modifier_id" jdbcType="INTEGER" property="modifierId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        point_temperature_id, user_id, min_val, max_val, interval_day, enable, is_deleted, saas_class_id,
        saas_school_id, saas_tenant_id, creator_id, modifier_id, create_time, update_time
    </sql>

    <sql id="where">
        is_deleted = 0
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
    </sql>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_temperature_setting
        <where>
            <include refid="where"/>
        </where>
        limit 1
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsSchoolSceneSortMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsSchoolSceneSort">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="rel_id" jdbcType="BIGINT" property="relId" />
    <result column="scene_code" jdbcType="VARCHAR" property="sceneCode" />
    <result column="sort_val" jdbcType="INTEGER" property="sortVal" />
    <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, plan_id, rel_id, scene_code, sort_val, saas_class_id, saas_school_id, saas_tenant_id, 
    is_deleted, create_time, update_time
  </sql>

  <select id="getListByCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from xs_school_scene_sort
    where 1=1
    and is_deleted = 0
    <if test="row.relIds != null and row.relIds.size() > 0">
      and rel_id in
      <foreach collection="row.relIds" item="relId" index="index"
               open="(" close=")" separator=",">
        #{relId}
      </foreach>
    </if>
    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>
    <if test="row.sceneCode != null">
      and scene_code = #{row.sceneCode}
    </if>
  </select>

</mapper>
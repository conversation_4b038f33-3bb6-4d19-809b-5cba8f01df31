<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsPosterRecordMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsPosterRecord">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="poster_record_id" jdbcType="BIGINT" property="posterRecordId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="qrcode_url" jdbcType="VARCHAR" property="qrcodeUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    poster_record_id, plan_id, user_id, business_code, url, saas_class_id, saas_school_id, 
    saas_tenant_id, create_time, update_time,qrcode_url
  </sql>

  <select id="getRowByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_poster_record
    where 1=1
    <if test="row.posterRecordIds != null and row.posterRecordIds.size() > 0">
      and poster_record_id in
      <foreach collection="row.posterRecordIds" item="posterRecordId" index="index"
               open="(" close=")" separator=",">
        #{posterRecordId}
      </foreach>
    </if>
    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>
    limit 1
  </select>
</mapper>
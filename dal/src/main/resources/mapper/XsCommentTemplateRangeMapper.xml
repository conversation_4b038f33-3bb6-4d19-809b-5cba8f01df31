<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsCommentTemplateRangeMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsCommentTemplateRange">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="comment_template_id" jdbcType="INTEGER" property="commentTemplateId" />
    <result column="saas_grade_id" jdbcType="VARCHAR" property="saasGradeId" />
    <result column="saas_grade_code" jdbcType="VARCHAR" property="saasGradeCode" />
    <result column="saas_campus_id" jdbcType="VARCHAR" property="saasCampusId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, comment_template_id, saas_grade_id, saas_grade_code, saas_campus_id, saas_school_id, 
    saas_tenant_id, is_deleted, create_time, update_time
  </sql>


  <select id="getListByCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from xs_comment_template_range
    where 1=1
    and is_deleted = 0
    <if test="row.commentTemplateIds != null and row.commentTemplateIds.size() > 0">
      and comment_template_id in
      <foreach collection="row.commentTemplateIds" item="commentTemplateId" index="index"
               open="(" close=")" separator=",">
        #{commentTemplateId}
      </foreach>
    </if>
    <if test="row.neqTemplateId != null">
      and comment_template_id != #{row.neqTemplateId}
    </if>

  </select>

  <select id="getGroupListByCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from xs_comment_template_range
    where 1=1
    and is_deleted = 0
    <if test="row.commentTemplateIds != null and row.commentTemplateIds.size() > 0">
      and comment_template_id in
      <foreach collection="row.commentTemplateIds" item="commentTemplateId" index="index"
               open="(" close=")" separator=",">
        #{commentTemplateId}
      </foreach>
    </if>
    <if test="row.neqTemplateId != null">
      and comment_template_id != #{row.neqTemplateId}
    </if>
    <if test="row.saasSchoolId != null">
      and saas_school_id = #{row.saasSchoolId}
    </if>
    group by comment_template_id,saas_grade_code,saas_campus_id

  </select>

  <insert id="addBatch" parameterType="com.hailiang.edu.xsjlsys.dal.entity.XsCommentTemplateRange">
    insert into xs_comment_template_range (comment_template_id,
    saas_grade_id,
    saas_grade_code,
    saas_campus_id,
    saas_school_id,
    saas_tenant_id,
    is_deleted,
    create_time,
    update_time)

    values
    <foreach collection ="list" item="row" index= "index" separator =",">
      (#{row.commentTemplateId},
      #{row.saasGradeId},
      #{row.saasGradeCode},
      #{row.saasCampusId},
      #{row.saasSchoolId},
      #{row.saasTenantId},
      #{row.isDeleted},
      #{row.createTime},
      #{row.updateTime})
    </foreach >
  </insert>

</mapper>
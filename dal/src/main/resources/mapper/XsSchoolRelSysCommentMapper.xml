<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsSchoolRelSysCommentMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsSchoolRelSysComment">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="sys_comment_id" jdbcType="INTEGER" property="sysCommentId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, saas_school_id, sys_comment_id, create_time, update_time
  </sql>







  <select id="getListBySchoolId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_school_rel_sys_comment
    where 1=1
    <if test="saasSchoolId != null">
      and saas_school_id = #{saasSchoolId}
    </if>
    group by id
    order by id asc
  </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsSchoolExperienceMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsSchoolExperience">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <id column="saas_school_id" jdbcType="BIGINT" property="saasSchoolId" />
    <result column="saas_school_name" jdbcType="VARCHAR" property="saasSchoolName" />
    <result column="scene" jdbcType="VARCHAR" property="scene" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
          id,saas_school_id, saas_school_name,scene, create_time, update_time
  </sql>

  <select id="getListBySchoolId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from xs_school_experience
    <where>
      <if test="schoolId != null">
        and saas_school_id = #{schoolId}
      </if>
    </where>
  </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsRedemptionRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsRedemptionRecord">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="redemption_record_id" jdbcType="BIGINT" property="redemptionRecordId"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="student_name" jdbcType="VARCHAR" property="studentName"/>
        <result column="prize_id" jdbcType="INTEGER" property="prizeId"/>
        <result column="prize_name" jdbcType="VARCHAR" property="prizeName"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="redemption_method" jdbcType="VARCHAR" property="redemptionMethod"/>
        <result column="redemption_instruction" jdbcType="VARCHAR" property="redemptionInstruction"/>
        <result column="prize_type" jdbcType="VARCHAR" property="prizeType"/>
        <result column="point_record_id" jdbcType="BIGINT" property="pointRecordId"/>
        <result column="is_revoke" jdbcType="BIT" property="isRevoke"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        redemption_record_id, student_id, student_name, prize_id, prize_name, user_id, score,
        plan_id,redemption_method,redemption_instruction,xwl_exchange_source_data,prize_type, point_record_id, is_revoke, saas_class_id,
        saas_school_id, saas_tenant_id,
        is_deleted, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select
        <include refid="Base_Column_List"/>
        from xs_redemption_record
        where redemption_record_id = #{redemptionRecordId,jdbcType=INTEGER}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into xs_redemption_record (redemption_record_id, student_id, student_name, prize_id, prize_name, user_id,
        score,
        plan_id,redemption_method,redemption_instruction,prize_type, point_record_id, is_revoke, saas_class_id,
        saas_school_id, saas_tenant_id,
        is_deleted, create_time, update_time)
        values
        <foreach collection="xsRedemptionRecordList" item="xsRedemptionRecord" index="index" separator=",">
            (
            #{xsRedemptionRecord.redemptionRecordId},
            #{xsRedemptionRecord.studentId},
            #{xsRedemptionRecord.studentName},
            #{xsRedemptionRecord.prizeId},
            #{xsRedemptionRecord.prizeName},
            #{xsRedemptionRecord.userId},
            #{xsRedemptionRecord.score},
            #{xsRedemptionRecord.planId},
            #{xsRedemptionRecord.redemptionMethod},
            #{xsRedemptionRecord.redemptionInstruction},
            #{xsRedemptionRecord.prizeType},
            #{xsRedemptionRecord.pointRecordId},
            #{xsRedemptionRecord.isRevoke},
            #{xsRedemptionRecord.saasClassId},
            #{xsRedemptionRecord.saasSchoolId},
            #{xsRedemptionRecord.saasTenantId},
            #{xsRedemptionRecord.isDeleted},
            #{xsRedemptionRecord.createTime},
            #{xsRedemptionRecord.updateTime}
            )
        </foreach>
    </insert>

    <select id="getPageListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_redemption_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasSchoolId != null">
            and saas_school_id = #{row.saasSchoolId}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.isRevoke!= null">
            and is_revoke = #{row.isRevoke}
        </if>
        order by create_time desc
        limit #{offset},#{limit}
    </select>

    <select id="getIncludeClassExchangePageListByCondition" resultMap="BaseResultMap">
        select * from (
        select
        <include refid="Base_Column_List"/>
        from xs_redemption_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.isRevoke!= null">
            and is_revoke = #{row.isRevoke}
        </if>
        <if test="row.startTime != null">
            and create_time >= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>
        UNION ALL
        select
        <include refid="Base_Column_List"/>
        from xs_redemption_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.redemptionMethod != null">
            and redemption_method = #{row.redemptionMethod}
        </if>
        <if test="row.redemptionMethods != null and row.redemptionMethods.size() > 0">
            and redemption_method in
            <foreach collection="row.redemptionMethods" item="d" index="index"
              open="(" close=")" separator=",">
                #{d}
            </foreach>
        </if>
        <if test="row.isRevoke!= null">
            and is_revoke = #{row.isRevoke}
        </if>
        <if test="row.startTime != null">
            and create_time >= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>
        ) as t
        order by create_time desc
        limit #{offset},#{limit}
    </select>

    <select id="getListCount" resultType="java.lang.Integer">
        select count(*) from (
        select redemption_record_id
        from xs_redemption_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasSchoolId != null">
            and saas_school_id = #{row.saasSchoolId}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.isRevoke!= null">
            and is_revoke = #{row.isRevoke}
        </if>
        group by redemption_record_id
        ) as redemption_record
    </select>

    <select id="getIncludeClassExchangeListCount" resultType="java.lang.Integer">
        select count(*) from (
        select redemption_record_id
        from xs_redemption_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.isRevoke!= null">
            and is_revoke = #{row.isRevoke}
        </if>
        <if test="row.startTime != null">
            and create_time >= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>
        UNION ALL
        select redemption_record_id
        from xs_redemption_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.redemptionMethod != null">
            and redemption_method = #{row.redemptionMethod}
        </if>
        <if test="row.redemptionMethods != null and row.redemptionMethods.size() > 0">
            and redemption_method in
            <foreach collection="row.redemptionMethods" item="d" index="index"
              open="(" close=")" separator=",">
                #{d}
            </foreach>
        </if>
        <if test="row.isRevoke!= null">
            and is_revoke = #{row.isRevoke}
        </if>
        <if test="row.startTime != null">
            and create_time >= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>
        ) as redemption_record
    </select>


    <update id="delByStudentIds">
        update xs_redemption_record
        set is_deleted = 1,
        update_time = #{updateTime}
        where 1=1
        <if test="saasClassId != null">
            and saas_class_id = #{saasClassId}
        </if>
        <if test="studentIds != null and studentIds.size() > 0">
            and student_id in
            <foreach collection="studentIds" item="studentId" index="index"
                     open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>

    </update>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_redemption_record
        where 1=1
        <if test="row.saasSchoolId != null">
            and saas_school_id = #{row.saasSchoolId}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>
        <if test="row.isRevoke!= null">
            and is_revoke = #{row.isRevoke}
        </if>
        <if test="row.redemptionMethod!= null">
            and redemption_method = #{row.redemptionMethod}
        </if>
        <if test="row.prizeType!= null">
            and prize_type = #{row.prizeType}
        </if>
        order by redemption_record_id desc
        limit 1
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsSeatStudentMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsSeatStudent">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="seat_student_id" jdbcType="BIGINT" property="seatStudentId"/>
        <result column="seat_arrange_id" jdbcType="BIGINT" property="seatArrangeId"/>
        <result column="x" jdbcType="INTEGER" property="x"/>
        <result column="y" jdbcType="INTEGER" property="y"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
        <result column="modifier_id" jdbcType="INTEGER" property="modifierId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        seat_student_id, seat_arrange_id, x, y, student_id, is_deleted, saas_class_id, saas_school_id,
        saas_tenant_id, creator_id, modifier_id, create_time, update_time
    </sql>

    <insert id="batchInsert">
        INSERT INTO xs_seat_student (
        seat_student_id,
        seat_arrange_id,
        x,
        y,
        student_id,
        is_deleted,
        saas_class_id,
        saas_school_id,
        saas_tenant_id,
        creator_id,
        modifier_id,
        create_time,
        update_time
        )
        VALUES
        <foreach item="row" collection="list" separator=",">
            (#{row.seatStudentId},
            #{row.seatArrangeId},
            #{row.x},
            #{row.y},
            #{row.studentId},
            #{row.isDeleted},
            #{row.saasClassId},
            #{row.saasSchoolId},
            #{row.saasTenantId},
            #{row.creatorId},
            #{row.modifierId},
            #{row.createTime},
            #{row.updateTime})
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE xs_seat_student SET x = #{item.x}, y = #{item.y}, modifier_id = #{item.modifierId}
            , update_time = #{item.updateTime}
            where seat_student_id = #{item.seatStudentId} AND is_deleted = 0
        </foreach>
    </update>

    <update id="batchDeletedByIds">
        <foreach collection="seatStudentIds" item="seatStudentId" separator=";">
            UPDATE xs_seat_student SET modifier_id = #{userId}, update_time = now()
            , is_deleted = 1
            where seat_student_id = #{seatStudentId} AND is_deleted = 0
        </foreach>
    </update>

    <update id="deleteByCondition">
        UPDATE xs_seat_student SET modifier_id = #{row.modifierId}, update_time = now()
        , is_deleted = 1
        where seat_arrange_id = #{row.seatArrangeId} AND is_deleted = 0
    </update>

    <select id="getListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_seat_student
        <where>
            <include refid="where"/>
        </where>
    </select>

    <sql id="where">
        and is_deleted = 0
        <if test="row.seatArrangeId != null and row.seatArrangeId != ''">
            and seat_arrange_id = #{row.seatArrangeId}
        </if>
    </sql>
</mapper>
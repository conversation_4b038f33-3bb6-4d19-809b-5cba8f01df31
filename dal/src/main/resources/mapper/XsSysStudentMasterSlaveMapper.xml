<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsSysStudentMasterSlaveMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsSysStudentMasterSlave">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="student_id" jdbcType="BIGINT" property="studentId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, student_id, parent_id, create_time, update_time
  </sql>




  <select id="getAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_sys_student_master_slave
  </select>
</mapper>
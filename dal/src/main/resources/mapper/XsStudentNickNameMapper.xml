<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsStudentNickNameMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsStudentNickName">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="nice_name" jdbcType="VARCHAR" property="niceName"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, student_id, nice_name, user_id, saas_class_id
    </sql>


    <select id="getListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_student_nick_name
        where 1=1
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
    </select>

    <insert id="addBatch" parameterType="com.hailiang.edu.xsjlsys.dal.entity.XsStudentNickName">
        insert into xs_student_nick_name (
        id,
        student_id,
        nice_name,
        user_id,
        saas_class_id,
        is_deleted,
        create_time,
        update_time )
        values
        <foreach collection="list" item="row" index="index" separator=",">
            (#{row.id},
            #{row.studentId},
            #{row.niceName},
            #{row.userId},
            #{row.saasClassId},
            #{row.isDeleted},
            #{row.createTime},
            #{row.updateTime})
        </foreach>
    </insert>
</mapper>
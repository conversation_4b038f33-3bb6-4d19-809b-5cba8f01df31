<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlsys.dal.dao.XsUserExpireClassMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlsys.dal.entity.XsUserExpireClass">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="expire_class_id" jdbcType="VARCHAR" property="expireClassId" />
    <result column="expire_class_name" jdbcType="VARCHAR" property="expireClassName" />
    <result column="expire_class_alias" jdbcType="VARCHAR" property="expireClassAlias" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, user_id, expire_class_id, expire_class_name, expire_class_alias, create_time, 
    update_time
  </sql>




  <select id="getRowByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_user_expire_class
    where 1=1
    and user_id = #{userId}
    group by user_id
    limit 1
  </select>

</mapper>